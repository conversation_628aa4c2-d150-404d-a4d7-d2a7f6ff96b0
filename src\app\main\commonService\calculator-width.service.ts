import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CalculatorWidthService {

  constructor() { }

  calculatorWidth(componentWidth) {
    let coefficient = componentWidth / 134//widget寬度/每項預留的寬度
    if (coefficient > 15) {
      return 'custom_cols_w6'//一列15個=>寬度100% /15=6.xxx%
    } else if (coefficient > 14 && coefficient <= 15) {
      return 'custom_cols_w7'//14
    } else if (coefficient > 12 && coefficient <= 14) {
      return 'custom_cols_w8'//12
    } else if (coefficient > 11 && coefficient <= 12) {
      return 'custom_cols_w9'//11
    } else if (coefficient > 10 && coefficient <= 11) {
      return 'custom_cols_w10'//10
    } else if (coefficient > 9 && coefficient <= 10) {
      return 'custom_cols_w11'//9
    } else if (coefficient > 8 && coefficient <= 9) {
      return 'custom_cols_w12'//8
    } else if (coefficient > 7 && coefficient <= 8) {
      return 'custom_cols_w14'//7
    } else if (coefficient > 6 && coefficient <= 7) {
      return 'custom_cols_w16'//6
    } else if (coefficient > 5 && coefficient <= 6) {
      return 'custom_cols_w20'//5
    } else if (coefficient > 4 && coefficient <= 5) {
      return 'custom_cols_w25'//4
    } else if (coefficient > 3 && coefficient <= 4) {
      return 'custom_cols_w33'//3
    } else if (coefficient > 2 && coefficient <= 3) {
      return 'custom_cols_w50'//2
    } else if (coefficient <= 2) {
      return 'custom_cols_w100'//1
    }
  }
  
  calculateWidth(componentWidth: number, itemsCount: number, defaultWidth: number = 134): string {
    if(componentWidth > 0 ){
      let coefficient = componentWidth / defaultWidth; // widget寬度/每項預留的寬度
      let maxColumns = Math.min(Math.floor(coefficient), itemsCount); // 最大列數vs.資料數=>取最小的
      return `${100 / maxColumns}%`; // 返回每列的寬度百分比
    }
  }
}
