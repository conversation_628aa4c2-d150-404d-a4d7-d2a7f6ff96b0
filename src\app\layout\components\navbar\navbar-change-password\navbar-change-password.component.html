<section class="modern-horizontal-wizard app-ecommerce-details">
  <form
    class="form form-vertical"
    (ngSubmit)="(changePasswordForm.form.valid)"
    #changePasswordForm="ngForm">
    <div class="row">
      <!-- Hide user avatar -->
      <div class="col-md-12">
        <div class="form-group">
          <label
            class="form-label"
            for="basic-icon-default-uname">
            {{ 'COMMON.USERNAME1' | translate }}
          </label>
          <input
            type="text"
            trim="blur"
            id="basic-icon-default-uname"
            class="form-control dt-uname"
            placeholder="Web Developer"
            aria-label="jdoe1"
            aria-describedby="basic-icon-default-uname2"
            name="user-name"
            [(ngModel)]="userData.name"
            [disabled]="true"
            pattern="[a-zA-Z0-9@!#?$/\\_\-\.]{1,128}"
            [class.error]="changePasswordForm.submitted && FUserNameRef.invalid"
            #FUserNameRef="ngModel"
            required>
          <span
            *ngIf="changePasswordForm.submitted && FUserNameRef.invalid"
            class="invalid-form">
            <small
              class="form-text text-danger"
              *ngIf="FUserNameRef.errors.required">
              {{ 'COMMON.FIELDREQUIRED' | translate }}
            </small>
            <small *ngIf="FUserNameRef.errors.pattern">
              {{ 'USERS.SPECIALSYMBOLS' | translate }}
            </small>
          </span>
        </div>
      </div>
      <div class="col-md-12">
        <div class="form-group">
          <label
            class="form-label"
            for="basic-icon-default-password">
            {{ 'USERS.OLDPASSWORD' | translate }}
          </label>
          <div class="input-group form-password-toggle mb-2">
            <input
              [type]="basicOldPwdShow ? 'text' : 'password'"
              trim="blur"
              class="form-control"
              id="basic-default-password"
              placeholder="{{ 'USERS.OLDPASSWORD' | translate }}"
              name="user-old-password"
              [(ngModel)]="userData.oldPassword"
              [class.error]="changePasswordForm.submitted && FOldPasswordRef.invalid"
              #FOldPasswordRef="ngModel"
              [required]="true"
              aria-label="oldPassword"
              aria-describedby="basic-default-password">
            <div
              class="input-group-append"
              (click)="basicOldPwdShow = !basicOldPwdShow">
              <span class="input-group-text cursor-pointer">
                <i
                  class="feather"
                  [ngClass]="{
                        'icon-eye-off': basicOldPwdShow,
                        'icon-eye': !basicOldPwdShow
                      }"></i>
              </span>
            </div>
          </div>
          <span
            *ngIf="changePasswordForm.submitted && FOldPasswordRef.invalid"
            class="invalid-form">
            <small
              class="form-text text-danger"
              *ngIf="FOldPasswordRef.errors.required">
              {{ 'COMMON.FIELDREQUIRED' | translate }}
            </small>
          </span>
        </div>
      </div>
      <div class="col-md-12">
        <div class="form-group">
          <label
            class="form-label"
            for="basic-icon-default-password">
            {{ 'USERS.NEWPASSWORD' | translate }}
          </label>
          <div class="input-group form-password-toggle mb-2">
            <input
              [type]="basicNewPwdShow ? 'text' : 'password'"
              trim="blur"
              class="form-control"
              id="basic-default-password1"
              placeholder="{{ 'USERS.NEWPASSWORD' | translate }}"
              name="user-new-password"
              [(ngModel)]="userData.newPassword"
              [class.error]="changePasswordForm.submitted && FNewPasswordRef.invalid"
              #FNewPasswordRef="ngModel"
              [required]="true"
              aria-label="newPassword"
              aria-describedby="basic-default-password1"
              pattern="(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,128}">
            <div
              class="input-group-append"
              (click)="basicNewPwdShow = !basicNewPwdShow">
              <span class="input-group-text cursor-pointer">
                <i
                  class="feather"
                  [ngClass]="{
                        'icon-eye-off': basicNewPwdShow,
                        'icon-eye': !basicNewPwdShow
                      }"></i>
              </span>
            </div>
          </div>
          <span
            *ngIf="changePasswordForm.submitted && FNewPasswordRef.invalid"
            class="invalid-form">
            <small
              class="form-text text-danger"
              *ngIf="FNewPasswordRef.errors.required">
              {{ 'COMMON.FIELDREQUIRED' | translate }}
            </small>
            <small *ngIf="FNewPasswordRef.errors.pattern">{{ 'USERS.CONFIRMPSW' | translate }}</small>
          </span>
        </div>
      </div>
      <div class="col-md-12">
        <div class="form-group">
          <label
            class="form-label"
            for="basic-icon-default-conformPassword">
            {{ 'USERS.CONFIRMPASSWORD' | translate }}
          </label>
          <div class="input-group form-password-toggle mb-2">
            <input
              [type]="basicConfirmPwdShow ? 'text' : 'password'"
              trim="blur"
              class="form-control"
              id="basic-default-password2"
              placeholder="{{ 'USERS.CONFIRMPASSWORD' | translate }}"
              name="user-confirm-password"
              [(ngModel)]="userData.confirmPassword"
              [class.error]="changePasswordForm.submitted && FConfirmRef.invalid"
              #FConfirmRef="ngModel"
              [required]="true"
              aria-label="conformPassword"
              aria-describedby="basic-default-password2"
              pattern="{{ FNewPasswordRef.value }}">
            <div
              class="input-group-append"
              (click)="basicConfirmPwdShow = !basicConfirmPwdShow">
              <span class="input-group-text cursor-pointer">
                <i
                  class="feather"
                  [ngClass]="{
                        'icon-eye-off': basicConfirmPwdShow,
                        'icon-eye': !basicConfirmPwdShow
                      }"></i>
              </span>
            </div>
          </div>
          <span
            *ngIf="changePasswordForm.submitted && FConfirmRef.invalid"
            class="invalid-form">
            <small
              class="form-text text-danger"
              *ngIf="FConfirmRef.errors.required">
              {{ 'COMMON.FIELDREQUIRED' | translate }}
            </small>
            <small *ngIf="FConfirmRef.errors.pattern">{{ 'USERS.NOTMATCH2' | translate }}</small>
          </span>
        </div>
      </div>
      <div class="col-12 d-flex justify-content-end">
        <button
          type="submit"
          class="btn btn-success btn-next"
          (click)="onSubmit(changePasswordForm)"
          [disabled]="loading"
          rippleEffect>
          <span
            *ngIf="loading"
            class="spinner-border spinner-border-sm mr-1"></span>
          <span class="align-middle d-sm-inline-block d-none">{{ 'USERS.SUBMIT' | translate }}</span>
          <i class="align-middle ml-sm-25 ml-0"></i>
        </button>
      </div>
    </div>
  </form>
</section>
