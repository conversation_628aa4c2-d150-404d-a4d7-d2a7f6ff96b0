<a
  [disabled]="(selectedDevices?selectedDevices.length==0:false) || noPermission"
  (click)="modalOpenSC(modalSC)"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center">
  <i
    data-feather="server"
    class="font-medium-1 mr-50 font-weight-bold">
  </i>
  {{ 'DEVICES.ADDTOGROUP' | translate }}
</a>
<!-- Modal -->
<ng-template
  #modalSC
  let-modal>
  <div class="modal-header d-flex justify-content-between align-items-center">
    <h4
      class="modal-title d-flex text-nowrap overflow-hidden text-truncate "
      id="myModalLabel1">
      <span class="modal-title-name">
        {{ 'DEVICES.ADDDEVTOGROUP' | translate }}
      </span>
      <!-- <span class="ml-75 badge badge-light-info " *ngIf="currentDeviceSN" >{{currentDeviceSN?currentDeviceSN:""}}</span> -->
      <!-- <span class="ml-75 badge badge-light-info " *ngIf="currentDeviceInfo.serial" >{{currentDeviceInfo.serial?currentDeviceInfo.serial:""}}</span> -->
      <div class="ml-75 badge badge-light-info mr-25 text-truncate overflow-hidden">
        <span *ngIf="serialArr?.length">
          {{ serialArr.length > 1 ? ('COMMON.SELECTED' | translate) + ': ' + serialArr.length : serialArr[0] }}
        </span>
      </div>
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    container="body"
    ngbAutofocus>
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label for="group-name-vertical">{{ 'GROUPS.NAME' | translate }}</label>
          <span class="text-warning ml-50">*</span>
          <input
            type="text"
            placeholder="Enter Group Name"
            class="form-control"
            [(ngModel)]="groupSelected"
            name="groupName"
            trim
            [ngbTypeahead]="search">
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <label>{{ 'DEVICES.PROTOCOL' | translate }}</label>
          <span class="text-warning ml-50">*</span>
          <ng-select
            class="column-select-filter"
            [clearable]="false"
            [searchable]="false"
            [closeOnSelect]="true"
            [items]="protocolOption"
            [(ngModel)]="protocolSelected"
            (change)="onChangeType($event)"
            bindLabel="name"
            bindValue="value"
            labelForId="modernType"
            #modernTypeRef="ngModel"
            required
            name="modernType"
            disabled
            placeholder="{{ 'DEVICES.SELECTPROTOCOL' | translate }}">
            <ng-template
              ng-label-tmp
              let-item="item"
              let-clear="clear">
              <span class="ng-value-label">{{ item.name }}</span>
              <span
                class="ng-value-icon right"
                (click)="clear(item)"
                aria-hidden="true">
                ×
              </span>
            </ng-template>
          </ng-select>
        </div>
      </div>
    </div>
    <div class="divider divider-success">
      <div class="divider-text">
        {{ 'DEVICES.SELECTDEVICE' | translate }}
      </div>
    </div>
    <div>
      <ngx-datatable
        #tableRowDetails
        appDatatableRecalculate
        [datatable]="tableRowDetails"
        [rows]="deviceArr"
        [rowHeight]="37"
        class="bootstrap core-bootstrap"
        [limit]="selectedOption"
        [columnMode]="ColumnMode.force"
        [headerHeight]="40"
        [footerHeight]="40"
        [scrollbarH]="true">
        <!-- serial -->
        <ngx-datatable-column
          [width]="200"
          name="Serial"
          prop="serial"
          [sortable]="false">
          <ng-template
            let-row="row"
            let-serial="value"
            ngx-datatable-cell-template>
            {{serial}}
          </ng-template>
        </ngx-datatable-column>
        <!-- ModelName -->
        <ngx-datatable-column
          *ngIf="mode != 'multiPMDevices'"
          [width]="150"
          name="Model Name"
          prop="modelName"
          [sortable]="false">
          <ng-template
            let-row="row"
            let-modelName="value"
            ngx-datatable-cell-template>
            {{modelName||'-'}}
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [width]="150"
          name="Product"
          prop="productName"
          [sortable]="false">
          <ng-template
            let-row="row"
            let-productName="value"
            ngx-datatable-cell-template>
            {{productName||'-'}}
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name=" "
          headerClass="tableActionHead"
          cellClass="tableActionCell"
          [width]="60"
          [sortable]="false"
          [canAutoResize]="false"
          [draggable]="false"
          [resizeable]="false">
          <ng-template
            let-row="row"
            ngx-datatable-cell-template>
            <button
              type="button"
              ngbTooltip="Delete"
              container="body"
              class="btn icon-btn btn-sm tableActionButton"
              (click)="removeDevice(row)"
              rippleEffect>
              <span
                [data-feather]="'trash-2'"
                class="text-primary"></span>
            </button>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
  <div class="modal-footer d-flex justify-content-between">
    <div class="custom-control custom-checkbox">
      <input
        type="checkbox"
        [(ngModel)]="autoFiltering"
        class="custom-control-input"
        id="autoFilterCheckBox">
      <label
        class="custom-control-label"
        for="autoFilterCheckBox">
        {{ 'GROUPS.FILTERING' | translate }}
      </label>
    </div>
    <div>
      <button
        type="button"
        class="btn btn-primary mr-1"
        [disabled]="!groupSelected || !protocolSelected || loading || !deviceArr.length"
        (click)="addDeviceToGroup(groupSelected,modal)">
        <span
          *ngIf="loading"
          class="spinner-border spinner-border-sm mr-1"></span>
        {{'COMMON.SAVE' | translate}}
      </button>
      <!-- <button
        type="button"
        class="btn btn-primary"
        (click)="modal.close('Close click')"
        rippleEffect>
        {{ 'COMMON.CLOSE' | translate }}
      </button> -->
    </div>
  </div>
</ng-template>
