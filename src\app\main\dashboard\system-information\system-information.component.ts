import { Component, OnInit, Input } from '@angular/core';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { DashboardService } from '../dashboard.service';
import{CalculatorWidthService} from 'app/main/commonService/calculator-width.service'
@Component({
  selector: 'app-system-information',
  templateUrl: './system-information.component.html',
  styleUrls: ['./system-information.component.scss']
})
export class SystemInformationComponent extends Unsubscribe implements OnInit {

  @Input() accessor: any;
  public systemInfoItems = []
  public blockUIStatus = false;
  public componentWidth: number = 0;
  public systemInfoNameMapping = {
    'tr069Port': 'TR-069 Port',
    'uspPort': 'TR-369 Port',
    'netconfPort': 'NETCONF Port',
    'nodesNumber': 'Nodes',
    // 'totalDevices': 'Online Devices (Online/Total)',
    // 'totalUsers': 'Online Users (Online/Total)',
    // 'WLANClientsTotal': 'WLAN Clients',
    '3GPPUEs': '3GPP UEs',
    'groups': 'Groups',
    // 'WorkFlowsTotal': 'WorkFlows (Active/Total)',
    'actions': 'Profiles Templates',
    'alarms': 'Alarms',
    'notifications': 'Notifications',
    'sysEvents': 'System Events',
    'deviceEvents': 'Device Events',
    'uptime': 'UpTime'
  }

  /**
   * Uptime formatter
   * @param time 
   * @returns 
   */

  constructor(
    private _gridSystemService: GridSystemService,
    private _dashboardService: DashboardService,
    private _calculatorWidthService: CalculatorWidthService,
  ) {
    super();
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getSystemInfo();
        break;
    }
  };

  getWidth() {
    return this._calculatorWidthService.calculateWidth(this.componentWidth, this.systemInfoItems.length);
  }

  getSystemInfo() {
    this.systemInfoItems = []
    this._dashboardService.getSystemInformation().then((res: any) => {
      for (let i in res) {
        if (this.systemInfoNameMapping[i]) {
          this.systemInfoItems.push({
            name: this.systemInfoNameMapping[i],
            reportedValue: res[i]
          })
        }
      }
      this.systemInfoItems.push({
        name: 'Online Users (Online/Total)',
        reportedValue: res.onlineUsers >= 0 && res.totalUsers >= 0 ? `${res.onlineUsers} / ${res.totalUsers}` : '-'
      })
      this.systemInfoItems.push({
        name: 'Online Devices (Online/Total)',
        reportedValue: res.onlineDevices >= 0 && res.totalDevices >= 0 ? `${res.onlineDevices} / ${res.totalDevices}` : '-'
      })
      this.systemInfoItems.push({
        name: 'WorkFlows (Active/Total)',
        reportedValue: res.activeWorkflow >= 0 && res.totalWorkflow >= 0 ? `${res.activeWorkflow} / ${res.totalWorkflow}` : '-'
      })
    })



    function reOrderResAccordingToMappingTable(arr, mapping) {
      let mappingArr = Object.entries(mapping);
      arr.sort(function (a, b) {
        let indexA = mappingArr.findIndex(([key, value]) => key === a.name);
        let indexB = mappingArr.findIndex(([key, value]) => key === b.name);
        if (indexA === -1) {
          indexA = mapping.length;
        }
        if (indexB === -1) {
          indexB = mapping.length;
        }
        return indexA - indexB;
      });
    }
    reOrderResAccordingToMappingTable(this.systemInfoItems, this.systemInfoNameMapping)
  }

  ngAfterViewInit(): void {
    this.customSubscribe(this._gridSystemService.onGridsterItemComponentInfoChanged, res => {
      this.componentWidth = res['SystemModule/SystemInformationComponent'] ? res['SystemModule/SystemInformationComponent']['width'] : 0
    })
  }

  ngOnInit(): void {
    this.getSystemInfo()
  }

}
