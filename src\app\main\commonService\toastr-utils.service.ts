import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class ToastrUtilsService {

  constructor(
    private toastr: ToastrService,
  ) { }

  capitalize(str) {
    return str && str.charAt(0).toUpperCase() + str.slice(1);
  }
  showSuccessMessage(title, content) {
    this.toastr.success(this.capitalize(content), this.capitalize(title), {
      positionClass: 'toast-bottom-right',
      toastClass: 'toast ngx-toastr',
      closeButton: true,
      progressBar: true,
      tapToDismiss: false,
      enableHtml: true
    });
  }
  showWarningMessage(title, content) {
    this.toastr.warning(this.capitalize(content), this.capitalize(title), {
      positionClass: 'toast-bottom-right',
      toastClass: 'toast ngx-toastr',
      closeButton: true,
      progressBar: true,
      tapToDismiss: false,
      enableHtml: true
    });
  }
  showErrorMessage(title, content) {
    this.toastr.error(this.capitalize(content), this.capitalize(title), {
      positionClass: 'toast-bottom-right',
      toastClass: 'toast ngx-toastr',
      closeButton: true,
      progressBar: true,
      tapToDismiss: false,
      enableHtml: true
    });
  }
  showInfoMessage(title, content) {
    this.toastr.info(this.capitalize(content), this.capitalize(title), {
      positionClass: 'toast-bottom-right',
      toastClass: 'toast ngx-toastr',
      closeButton: true,
      progressBar: true,
      tapToDismiss: false,
      enableHtml: true
    });
  }

}
