<core-card
  [actions]="['columnDragula', 'reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  [columnDragula]="tableOption"
  (events)="reload($event)"
  (changeColumn)="changeColumn($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <ngx-datatable
    #tableRowDetails
    appDatatableRecalculate
    [datatable]="tableRowDetails"
    [rows]="rows"
    [rowHeight]="37"
    class="bootstrap core-bootstrap"
    [limit]="selectedOption"
    [columnMode]="ColumnMode.standard"
    [headerHeight]="45"
    [footerHeight]="45"
    [scrollbarH]="true"
    [scrollbarV]="true"
    (resize)="onResize($event)">
    <ng-container *ngFor="let col of tableOption">
      <!-- name -->
      <ngx-datatable-column
        *ngIf="col.prop === 'name' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="name"
        [sortable]="false">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            class="form-control form-control-sm"
            placeholder="{{column.name | titlecase}}"
            [(ngModel)]="queryParams.filter['name']"
            (keyup)="filterFn('name')">
        </ng-template>
        <ng-template
          let-row="row"
          let-name="value"
          ngx-datatable-cell-template>
          <div>
            <a routerLink="/groups/{{row.id}}/group-info">
              <app-beautify-content
                [content]="name"
                [placement]="'right'"></app-beautify-content>
            </a>
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- cpe -->
      <ngx-datatable-column
        *ngIf="col.prop === 'number' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="true"
        prop="number">
        <ng-template
          let-row="row"
          let-number="value"
          ngx-datatable-cell-template>
          {{number}}
        </ng-template>
      </ngx-datatable-column>
      <!-- online rate -->
      <ngx-datatable-column
        *ngIf="col.prop === 'onlineRate' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="true"
        prop="onlineRate">
        <ng-template
          let-row="row"
          let-onlineRate="value"
          ngx-datatable-cell-template>
          {{onlineRate?onlineRate:'-'}}
        </ng-template>
      </ngx-datatable-column>
      <!-- service availability -->
      <ngx-datatable-column
        *ngIf="col.prop === 'serviceAvailability' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="true"
        prop="serviceAvailability">
        <ng-template
          let-row="row"
          let-serviceAvailability="value"
          ngx-datatable-cell-template>
          {{serviceAvailability?serviceAvailability:'-'}}
        </ng-template>
      </ngx-datatable-column>
      <!-- alarmCount -->
      <ngx-datatable-column
        *ngIf="col.prop === 'alarmList' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="false"
        prop="alarmList">
        <ng-template
          let-row="row"
          let-alarmList="value"
          ngx-datatable-cell-template>
          <div
            class="text-truncate"
            (mouseenter)="spanMouseenter($event,row)"
            [ngbPopover]="content && content.length ? popTagContent : ''"
            triggers="mouseenter:mouseleave"
            container="body">
            <button
              (click)="checkAlarms(row.id)"
              type="button"
              class="btn icon-btn btn-sm hide-arrow"
              style="padding: 0px !important;margin-right: 5px;"
              rippleEffect>
              <span
                class="text-primary"
                size="16"
                [data-feather]="'refresh-ccw'">
              </span>
            </button>
            {{row.alarmCount}}
            <div
              class="badge badge-light-danger ml-50"
              *ngIf="row.alarmList?.Critical">
              {{'ALARMS.CRITICAL' | translate}}: {{row.alarmList?.Critical}}
            </div>
            <div
              class="badge badge-light-warning ml-50"
              *ngIf="row.alarmList?.Major">
              {{ 'ALARMS.MAJOR' | translate }}: {{row.alarmList?.Major}}
            </div>
            <div
              class="badge badge-light-gray ml-50"
              *ngIf="row.alarmList?.Minor">
              {{ 'ALARMS.MINOR' | translate }}: {{row.alarmList?.Minor}}
            </div>
            <div
              class="badge badge-light-gray ml-50"
              *ngIf="row.alarmList?.Warning">
              {{ 'ALARMS.WARNING' | translate }}: {{row.alarmList?.Warning}}
            </div>
            <div
              class="badge badge-light-secondary ml-50"
              *ngIf="row.alarmList?.Indeterminate">
              {{ 'ALARMS.INDETERMINATE' | translate }}: {{row.alarmList?.Indeterminate}}
            </div>
          </div>
          <ng-template #popTagContent>
            <div *ngFor="let item of content">
              <li>{{item.name}}: {{item.value}}</li>
            </div>
          </ng-template>
        </ng-template>
      </ngx-datatable-column>
      <!-- deplotmentMode -->
      <ngx-datatable-column
        *ngIf="col.prop === 'deploymentMode' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="deploymentMode"
        [sortable]="true">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <div>{{'GROUPS.NETWORK' | translate}} </div>
        </ng-template>
        <ng-template
          let-row="row"
          let-deploymentMode="value"
          ngx-datatable-cell-template>
          <app-beautify-content 
          [content]="deploymentMode?deploymentModeMapping[deploymentMode]:'-'"
          [ngClass]="{'badge badge-light-warning': deploymentMode}"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- product -->
      <ngx-datatable-column
        *ngIf="col.prop === 'productName' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="productName"
        [sortable]="false">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <select
            class="form-control form-control-sm"
            [(ngModel)]="queryParams.filter['productName']"
            (change)="filterFn('productName')">
            <option
              *ngFor="let option of selectScope"
              [value]="option.value">
              {{option.name}}
            </option>
          </select>
        </ng-template>
        <ng-template
          let-row="row"
          let-productName="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="productName[0] == 'ADMIN' ? ['ALL'] : productName"
            [type]="'list'"
            [textClass]="'badge-light-info'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- protocol -->
      <ngx-datatable-column
        *ngIf="col.prop === 'protocol' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="protocol"
        [sortable]="false">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <select
            class="form-control form-control-sm"
            [(ngModel)]="queryParams.filter['protocol']"
            (change)="filterFn('protocol')">
            <option value>{{ 'COMMON.ALLPROTOCOL' | translate }}</option>
            <option
              *ngFor="let option of selectProtocol"
              [value]="option.value">
              {{option.name}}
            </option>
          </select>
        </ng-template>
        <ng-template
          let-row="row"
          let-protocol="value"
          ngx-datatable-cell-template>
          <div>
            {{protocol == 'usp' ? 'USP (TR-369)' : protocol == 'netconf' ? 'NETCONF' : 'CWMP (TR-069)'}}
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Created By -->
      <ngx-datatable-column
        *ngIf="col.prop === 'creator' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="creator"
        [sortable]="false">
        <ng-template
          let-row="row"
          let-creator="value"
          ngx-datatable-cell-template>
          <div>{{creator}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Created Time -->
      <ngx-datatable-column
        *ngIf="col.prop === 'established' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="established"
        [sortable]="true">
        <ng-template
          let-row="row"
          let-established="value"
          ngx-datatable-cell-template>
          {{established | date:'MM/dd/yy, HH:mm:ss'}}
        </ng-template>
      </ngx-datatable-column>
      <!-- tags -->
      <ngx-datatable-column
        *ngIf="col.prop === 'tags' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="tags"
        [sortable]="false">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            class="form-control form-control-sm"
            placeholder="{{column.name | titlecase}}"
            [(ngModel)]="queryParams.filter['tags']"
            (ngModelChange)="filterInputChange($event)">
        </ng-template>
        <ng-template
          let-row="row"
          let-tags="value"
          ngx-datatable-cell-template>
          <div *ngIf="tags.length>0">
            <app-beautify-content
              [content]="tags"
              [type]="'list'"
              [textClass]="'badge-light-success'"
                  [icon]="'tag'"></app-beautify-content>
          </div>
          <div *ngIf="!tags || tags.length===0">
            <span>-</span>
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- location -->
      <ngx-datatable-column
        *ngIf="col.prop === 'location' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="location"
        [sortable]="false">
        <ng-template
          let-row="row"
          let-location="value"
          ngx-datatable-cell-template>
          <div>
            {{location.trim().length>0?location:'-'}}
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Group Location -->
      <ngx-datatable-column
        *ngIf="col.prop === 'viewLocation' && col.columnStatus"
        name="{{ col.translate | translate }}"
        [width]="150"
        headerClass="tableActionHead"
        cellClass="tableActionCell"
        [sortable]="false"
        [canAutoResize]="false"
        [draggable]="true"
        [resizeable]="true"
        ngbTooltip="{{ 'GROUPS.VIEWLOCATIONDESCRIB' | translate }}"
        prop="viewLocation">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <div
            container="body"
            class="hide-arrow tableActionButton"
            style="padding: 0px !important;"
            ngbTooltip="{{ 'GROUPS.VIEWLOCATIONDESCRIB' | translate }}"
            >
            <!-- <span [data-feather]="'map-pin'"></span> -->
            {{'GROUPS.VIEWLOCATION'|translate}}
          </div>
        </ng-template>
        <ng-template
          let-row="row"
          let-viewLocation="'value"
          ngx-datatable-cell-template>
          <div container="body">
            <button
              *ngIf="row.address && row.address.lat!==0 && row.address.lng!==0"
              (click)="lookGroupLocation(row.id)"
              type="button"
              class="btn icon-btn btn-sm hide-arrow tableActionButton"
              style="padding: 0px !important;"
              ngbTooltip="{{ col.translate | translate }}"
              placement="left"
              rippleEffect>
              <span
                class="text-primary"
                [data-feather]="'map-pin' ">
              </span>
            </button>
            <div
              *ngIf="!row.address|| (row.address.lat===0 && row.address.lng===0)"
              style="padding: 0px !important;margin-left: 5px;">
              -
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Coverage map -->
      <ngx-datatable-column
        *ngIf="col.prop === 'viewCoverageMap' && col.columnStatus"
        name="{{ col.translate | translate }}"
        [width]="150"
        headerClass="tableActionHead"
        cellClass="tableActionCell"
        [sortable]="false"
        [canAutoResize]="false"
        [draggable]="true"
        [resizeable]="true"
        ngbTooltip="{{ 'GROUPS.VIEWCOVERAGEMAPDESCRIB' | translate }}"
        prop="viewCoverageMap">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <div
            container="body"
            class="hide-arrow tableActionButton"
            style="padding: 0px !important;"
            ngbTooltip="{{ 'GROUPS.VIEWCOVERAGEMAPDESCRIB' | translate }}"
            >
            <!-- <span [data-feather]="'map'"></span> -->
            {{'GROUPS.VIEWCOVERAGEMAP'|translate}}
          </div>
        </ng-template>
        <ng-template
          let-row="row"
          let-viewCoverageMap="value"
          ngx-datatable-cell-template>
          <div container="body">
            <button
              *ngIf="row.groupMapId"
              (click)="lookCoverageMap(row.id)"
              type="button"
              class="btn icon-btn btn-sm hide-arrow tableActionButton"
              style="padding: 0px !important;"
              ngbTooltip="{{ 'GROUPS.VIEWCOVERAGEMAP' | translate }}"
              placement="left"
              rippleEffect>
              <span
                class="text-primary"
                [data-feather]="'map'">
              </span>
            </button>
            <div
              *ngIf="!row.groupMapId"
              style="margin-left: 5px;">
              -
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>
    </ng-container>
    <!-- ngx-datatable-footer -->
    <ngx-datatable-footer>
      <ng-template ngx-datatable-footer-template>
        <div class="col-12 d-flex align-items-start">
          <div
            class="page-count"
            style="padding: 0;">
            <span>{{pageCount}} total</span>
          </div>
          <div class="pager-vertical-center">
            <datatable-pager
              [pagerLeftArrowIcon]="'datatable-icon-left'"
              [pagerRightArrowIcon]="'datatable-icon-right'"
              [pagerPreviousIcon]="'datatable-icon-prev'"
              [pagerNextIcon]="'datatable-icon-skip'"
              [page]="queryParams.page"
              [size]="queryParams.size"
              [count]="pageCount"
              (change)="onFooterPage($event)">
            </datatable-pager>
          </div>
        </div>
      </ng-template>
    </ngx-datatable-footer>
  </ngx-datatable>
</core-card>
