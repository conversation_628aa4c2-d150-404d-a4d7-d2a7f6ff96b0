import { <PERSON>pe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'kpbsToSize'
})
export class KpbsToSizePipe implements PipeTransform {

  constructor(private sanitizer: DomSanitizer) { }


  transform(value: number): SafeHtml {
    if (value < 1000) {
      return this.sanitizer.bypassSecurityTrustHtml(value + ` <small>Kbps</small>`);
    } else if (value < 1000000) {
      return this.sanitizer.bypassSecurityTrustHtml((value / 1000).toFixed(2) + ` <small>Mbps</small>`);
    } else {
      return this.sanitizer.bypassSecurityTrustHtml((value / 1000000).toFixed(2) + ` <small>Gbps</small>`);
    }
  }

}
