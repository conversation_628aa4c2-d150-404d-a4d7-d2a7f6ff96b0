import { Component, OnInit, Input } from '@angular/core';
import { AnalysisSystemService } from '../analysis-system.service'
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { CalculatorWidthService } from 'app/main/commonService/calculator-width.service';

@Component({
  selector: 'app-data-pm-status',
  templateUrl: './data-pm-status.component.html',
  styleUrls: ['./data-pm-status.component.scss']
})
export class DataPmStatusComponent extends Unsubscribe implements OnInit {
  @Input() accessor: any;
  public blockUIStatus = false;
  public inservice = false;
  public pmInfoItems:any = []
  constructor(
    private _analysisSystemService: AnalysisSystemService,
    private _gridSystemService: GridSystemService,
    private _calculatorWidthService: CalculatorWidthService,
  ) {
    super();
  }
  public pmInfoNameMapping = {
    'version': 'Version',
    'uptime': 'Uptime',
    'freeSpace': 'Free Disk Space',
    'totalSpace': 'Total Disk Space',
    'cpuUtilize': 'CPU Utilize',
    'freemem': 'Free Memory',
    'totalmem': 'Total Memory',
    'date': 'Date',
 }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getPMInfoRes();
        break;
    }
  };

  getPMInfoRes(){
    this.blockUIStatus = true;
    this._analysisSystemService.getBasicPM().then(res => {
      this.blockUIStatus = false;
      this.inservice = res["inservice"];
      if (!res) {
        this.pmInfoItems = Object.keys(this.pmInfoNameMapping).map(item => {
          return {
            "name": item, "reportedValue": ''
          }
        })
      } else {
        this.pmInfoItems = []
        for (let i in res) {
          this.pmInfoItems.push({ "name": i, "reportedValue": res[i] })
        }
        const index = this.pmInfoItems.findIndex(item => item.name === "inservice");
        if (index > -1) {
          this.pmInfoItems.splice(index, 1);
        }
      }
    }).catch((err) => {
      this.blockUIStatus = false;
    })
  }
  
  public componentWidth: number = 0;
  getWidth() {
    return this._calculatorWidthService.calculateWidth(this.componentWidth, this.pmInfoItems.length);
  }
  ngAfterViewInit(): void {
    this.customSubscribe(this._gridSystemService.onGridsterItemComponentInfoChanged, res => {
      this.componentWidth = res['Analysis/DataPmStatusComponent'] ? res['Analysis/DataPmStatusComponent']['width'] : 0
    })
  }

  ngOnInit(): void {
    this.getPMInfoRes();
  }

}
