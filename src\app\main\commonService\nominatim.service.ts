import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class NominatimService {
  constructor(private http: HttpClient) { }

  // 地理编码（地址 -> 经纬度）
  geocode(address: string): Observable<any> {
    const url = `search`;
    const params = {
      q: address,
      format: 'json',
    };
    return this.http.get(url, { params });
  }

  // 反向地理编码（经纬度 -> 地址）
  reverseGeocode(lat: number, lon: number): Observable<any> {
    const url = `reverse`;
    const params = {
      lat: lat.toString(),
      lon: lon.toString(),
      format: 'json',
    };

    return this.http.get(url, { params }).pipe(
      retry(2),
      catchError((error) => {
        console.error('Error occurred while fetching geocode:', error);
        return throwError(() => new Error('Geocoding failed')); // 返回错误
      })
    );
  }
}