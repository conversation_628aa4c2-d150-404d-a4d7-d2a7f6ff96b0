import { Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation, AfterViewChecked, ChangeDetectorRef } from '@angular/core';
import { ColumnMode, DatatableComponent, SelectionType } from '@almaobservatory/ngx-datatable';
import { EventsService } from 'app/main/events/events.service';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { DatatableCustomizeService } from 'app/main/commonService/datatable-customize.service';
import cloneDeep from 'lodash/cloneDeep';
import { BehaviorSubject, Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { timer, Subscription } from 'rxjs';
import { ResizeObservableService } from 'app/main/commonService/resize-observable.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ProductDataService } from 'app/main/products/products.service';
import Swal from 'sweetalert2';
import { FindDeviceService } from 'app/main/commonService/find-device.service';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

export interface IqueryParam {
  filter: {},
  page: number;
  size: number,
  sort: string
}

@UntilDestroy()
@Component({
  selector: 'app-alarm-list',
  templateUrl: './alarm-list.component.html',
  styleUrls: ['./alarm-list.component.scss']
})
export class AlarmListComponent implements OnInit, AfterViewChecked, OnDestroy {
  @Input() accessor: any;
  @ViewChild(DatatableComponent) tableRowDetails: DatatableComponent;
  public blockUIStatus = false;
  public selected = [];
  // public selectedSeverity = [];
  // public selectedState = [];
  public selectedClearedTime = [];
  public selectedOption = 10;
  public ColumnMode = ColumnMode;
  public SelectionType = SelectionType;
  public deviceAlarms: any;
  public selectSeverity: any = [
    { name: this.translateService.instant('ALARMS.CRITICAL'), value: 'Critical' },
    { name: this.translateService.instant('ALARMS.MAJOR'), value: 'Major' },
    { name: this.translateService.instant('ALARMS.MINOR'), value: 'Minor' },
    { name: this.translateService.instant('ALARMS.WARNING'), value: 'Warning' },
    { name: this.translateService.instant('ALARMS.INDETERMINATE'), value: 'Indeterminate' },
  ];
  public selectState: any = [
    { name: this.translateService.instant('ALARMS.UNCLEAREDALARM'), value: 'UnCleared' },
    { name: this.translateService.instant('ALARMS.CLEAREDALARM'), value: 'Cleared' },
  ];
  public selectClearedTime: any = [
    { name: this.translateService.instant('ALARMS.CLEARED'), value: 'true' },
    { name: this.translateService.instant('ALARMS.NOTCLEARED'), value: 'false' },
  ];
  public tableOption = [
    { name: 'State', prop: 'flag', translate: 'DEVICES.STATE', width: 180, flexGrow: 180, columnStatus: true },
    { name: 'Severity', prop: 'PerceivedSeverity', translate: 'ALARMS.SEVERITY', width: 150, flexGrow: 150, columnStatus: true },
    { name: 'Serial', prop: 'serialNumber', translate: 'COMMON.SERIAL_NUMBER', width: 180, flexGrow: 180, columnStatus: true },
    { name: 'Probable Cause', prop: 'ProbableCause', translate: 'ALARMS.PROBABLECAUSE', width: 200, flexGrow: 200, columnStatus: true },
    { name: 'Event Time', prop: 'EventTime', translate: 'ALARMS.EVENTTIME', width: 200, flexGrow: 200, columnStatus: true },
    { name: 'Cleared Time', prop: 'clearedTime', translate: 'DEVICES.CLEAREDTIME', width: 200, flexGrow: 200, columnStatus: true },
    { name: 'Alarm ID', prop: 'AlarmIdentifier', translate: 'ALARMS.ALARMID', width: 180, flexGrow: 180, columnStatus: true },
    { name: 'Event Type', prop: 'EventType', translate: 'ALARMS.EVENTTYPE', width: 180, flexGrow: 180, columnStatus: true },
    { name: 'Groups', prop: 'devGroup', translate: 'GROUPS.TITLE', width: 180, flexGrow: 180, columnStatus: true },
    { name: 'Products', prop: 'productName', translate: 'COMMON.PRODUCTS', width: 180, flexGrow: 180, columnStatus: true },
    { name: 'Ack User', prop: 'ackUser', translate: 'ALARMS.ACKUSER', width: 120, flexGrow: 120, columnStatus: true },
    { name: 'Ack Time', prop: 'ackTime', translate: 'ALARMS.ACKTIME', width: 180, flexGrow: 180, columnStatus: true },
  ];
  public tableWidth = 0;
  public gridScrollHeight = 0;
  public scrollSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  public tableOptionStatus = false;

  public selectedScope = [];
  public selectGroup: any = [];
  // public selectedGroup = [];
  public deviceAlarmData: any;
  public editData = { stages: {}, target: {} };
  public tableOffsetX = 0;
  public selectProduct: any = [];


  constructor(
    private _eventsService: EventsService,
    private translateService: TranslateService,
    private _datatableCustomizeService: DatatableCustomizeService,
    private _groupListService: GroupListService,
    private cdr: ChangeDetectorRef,
    private _productDataService: ProductDataService,
    private _resizeObservableService: ResizeObservableService,
    private _findDeviceService: FindDeviceService,
    private _router: Router,
    private modalService: NgbModal,
  ) {
    this.filterChanged.pipe(
      debounceTime(1000),
      untilDestroyed(this))
      .subscribe(model => {
        // console.log(model);
        // console.log(this.queryParams);
        this.queryParams.page = 1
        this.getEventsDeviceListRes(this.cloneParams(this.queryParams))
      });

    this.getEventsDeviceListRes(this.cloneParams(this.queryParams))
    this.loadGroup()
    this.loadProduct()
  }

  public queryParams: IqueryParam = {
    filter: {
      'severity': '',
      'flag': 'UnCleared',
      'serialNumber': '',
      'alarmId': '',
      'eventType': '',
      'productName': '',
      'group': ''
    },
    page: 1,
    size: 10,
    sort: 'eventTime,desc'
  }
  public pageCount: number
  filterChanged: Subject<string> = new Subject<string>();
  public GOTDEVICEINFO = this.translateService.instant('PM.GOTDEVICEINFO');

  gotoDeviceIInfo(sn) {
    this._findDeviceService.findDeviceBySn(sn).pipe(untilDestroyed(this)).subscribe((res: any) => {
      if (res.data && res.data[0] && res.data[0].id) {
        this._router.navigate([`/devices/${res.data[0].id}/device-info`], { skipLocationChange: false });
      }
    })

  }

  loadProduct() {
    this._productDataService.getProductList().pipe(untilDestroyed(this)).subscribe(res => {
      this.sortOption(res)
    })
  };

  sortOption(list) {
    this.selectProduct = list.sort((a, b) => {
      if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
      return 1
    }).map((item) => {
      // this.provisioningTypeObj[item.name] = item.provisioningType;
      return {
        'name': item.name,
        'value': item.name
      }
    })
  }

  checkFilter(condition: string) {
    if (condition !== 'flag') {
      for (let key in this.queryParams.filter) {
        if (key !== condition && key !== 'flag') {
          this.queryParams.filter[key] = ''
        }
      }
    }
  }

  filterInputChange(text: string, condition: string) {
    this.checkFilter(condition);
    this.filterChanged.next(text);
  }

  filterSelectChange(event, prop) {
    this.queryParams.filter[prop] = event.target.value;
    this.queryParams.page = 1;
    this.checkFilter(prop);
    this.getEventsDeviceListRes(this.cloneParams(this.queryParams))
  }

  onSort({ sorts }) {
    // console.log('sorts', sorts)
    this.queryParams.sort = sorts[0].prop + ',' + sorts[0].dir;
    this.queryParams.page = 1
    this.getEventsDeviceListRes(this.cloneParams(this.queryParams));
  }
  onResize({ column, newValue }) {
    this.tableOption = this._datatableCustomizeService.resize(column, newValue, this.tableOption, this.accessor, this.tableRowDetails);
  }
  onSelect({ selected }) {
    // console.log(selected)
    this.selected = selected;
  }

  onFooterPage(e) {
    console.log(e)
    this.queryParams.page = e.page
    this.getEventsDeviceListRes(this.cloneParams(this.queryParams))
  }

  cloneParams(queryParams) {
    let cloneQueryParamsClone
    cloneQueryParamsClone = cloneDeep(queryParams)
    for (let key in cloneQueryParamsClone.filter) {
      if (cloneQueryParamsClone.filter[key] == '') {
        delete cloneQueryParamsClone.filter[key]
      }
    }
    cloneQueryParamsClone.filter = JSON.stringify(cloneQueryParamsClone.filter)
    cloneQueryParamsClone.page = cloneQueryParamsClone.page - 1
    // console.log('queryParamsClone', cloneQueryParamsClone)
    return cloneQueryParamsClone
  }

  /**
 * Core card event
 * @param event 
 */
  emittedEvents(event: string): void {
    // this.blockUIStatus = true;
    switch (event) {
      case 'reload':
        this.getEventsDeviceListRes(this.cloneParams(this.queryParams))
        this.loadGroup()
        break;
    }
  };
  changeColumn(event): void {
    if (event.cloumns) {
      this.tableOption = [...event.cloumns];
    }
  }

  resetOffset(ifFilter?) {
    if (ifFilter) {
      this.tableRowDetails.headerComponent.offsetX = this.tableOffsetX
      this.tableRowDetails.bodyComponent.offsetX = this.tableOffsetX
    } else {
      this.tableOffsetX = this.tableRowDetails?.bodyComponent?.offsetX || 0
    }
  }

  getEventsDeviceListRes(param) {
    this.blockUIStatus = true
    this.resetOffset(false)
    var params: any = {
      params: param
    };
    this._eventsService.getEventsDeviceList(params).then(res => {
      this.deviceAlarms = [...res['data']];
      this.deviceAlarms.forEach(item => {
        if (!item.flag) {
          item.flag = ''
        }
        if (!item.clearedTime) {
          item.clearedTime = ''
        }
      })
      // console.log(this.deviceAlarms)
      this.pageCount = res['num'];
    }).finally(() => {
      this.tableRowDetails.selected = []
      this.selected = []
      this.resetOffset(true)
      // this.tableRowDetails.headerComponent.offsetX = 0
      // this.tableRowDetails.bodyComponent.offsetX = 0
      this.blockUIStatus = false;
    });
  }

  loadGroup() {
    let group = []
    const groupListData = this._groupListService.onGroupsListChanged.getValue() || []
    if (groupListData && groupListData.length > 0) {
      groupListData.forEach(item => {
        group.push({
          'name': item.name,
          'id': item.id
        })
      })
      this.selectGroup = group.sort((a, b) => {
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
        return 1
      })
    } else {
      this._groupListService.getGroupNameList().pipe(untilDestroyed(this)).subscribe({
        next: res => {
          res.forEach(item => {
            group.push({
              'name': item.name,
              'id': item.id
            })
          })
          this.selectGroup = group.sort((a, b) => {
            if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
            return 1
          })
        },
        error: error => {
          console.log(error);
        },
        complete: () => {
          // this.blockUIStatus = false;
        }
      })
    }
  }

  /**
    * Row Details Toggle
    *
    * @param row
    */
  rowDetailsToggleExpand(row) {
    this.tableRowDetails.rowDetail.toggleExpandRow(row);
  }


  ngAfterViewChecked(): void {
    const { scrollHeight, innerWidth: tableWidth } = this.tableRowDetails.bodyComponent;
    const { scrollHeight: gridScrollHeight } = this.accessor.gridRef?.el;
    if (((gridScrollHeight && gridScrollHeight > this.gridScrollHeight) || (tableWidth && tableWidth !== this.tableWidth)) && scrollHeight) {
      this.tableWidth = tableWidth;
      this.gridScrollHeight = gridScrollHeight;
      this.scrollSubject.next({ gridScrollHeight, tableWidth });
    }
    this.cdr.detectChanges();
  }
  ngOnInit(): void {
    this.tableOptionStatus = !!this._datatableCustomizeService.getTableOption(this.accessor.componentId);
    this.tableOption = this._datatableCustomizeService.mergeOption(this.accessor.componentId, this.tableOption);
    const dueTime = this.tableOptionStatus ? 400 : 0;
    this.scrollSubject.pipe(untilDestroyed(this), debounceTime(dueTime)).subscribe(res => {
      if (res.gridScrollHeight && res.tableWidth && !this._resizeObservableService.windowResizeState) {
        this.tableOption = this._datatableCustomizeService.formatColumn(this.tableRowDetails, this.tableOption, this.tableWidth, this.accessor);
      }
    })
    this.queryParams.filter['flag'] = this.selectState[0].value
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getEventsDeviceListRes(this.cloneParams(this.queryParams))
      this.loadGroup()
      this.loadProduct()
    });
  }
  ngOnDestroy(): void {
    this.filterChanged.complete()
  }

}
