import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'labelNameMapping',
  // standalone: true
})
export class LabelNameMappingPipe implements PipeTransform {

  private paramInputNameMap: Record<string, string> = {
    AutoActivate: 'Auto Activate',
    // Username: 'User name',
    FileSize: 'File Size',
    CheckSumAlgorithm: 'Check Sum Algorithm',
    CheckSum: 'Check Sum',
    TargetFileName: 'Target File Name'
  };

  transform(value: string): string {
    return this.paramInputNameMap[value] || value;
  }

}
