import { SessionsDurationComponent } from './sessions-duration/sessions-duration.component';
import { SessionsRateComponent } from './sessions-rate/sessions-rate.component';
import { RequestsLatencyComponent } from './requests-latency/requests-latency.component';
import { RequestsRateComponent } from './requests-rate/requests-rate.component';
import { RequestsParsingComponent } from './requests-parsing/requests-parsing.component';
import { FreeMemoryComponent } from './free-memory/free-memory.component';
import { CpuUsageComponent } from './cpu-usage/cpu-usage.component';
import { FreeDiskComponent } from './free-disk/free-disk.component';
import { DataBaseStatusComponent } from '../analysis-system/data-base-status/data-base-status.component';
import { DataXmppStatusComponent } from '../analysis-system/data-xmpp-status/data-xmpp-status.component';
import { DataPmStatusComponent } from '../analysis-system/data-pm-status/data-pm-status.component';
import { MemoryUtilizationComponent } from './memory-utilization/memory-utilization.component';
import { DiskUtilizationComponent } from './disk-utilization/disk-utilization.component';
import { Widget } from 'app/layout/widget';


const widgets: Widget[] = [
    {
        componentId: 'Analysis/DataBaseStatusComponent',
        component: DataBaseStatusComponent,
        name: 'ANALYSIS.DBSTATUS',
        description: 'ANALYSIS.DBSTATUS_DESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 12,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'keyValue',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/DataXmppStatusComponent',
        component: DataXmppStatusComponent,
        name: 'ANALYSIS.XMPPSTATUS',
        description: 'ANALYSIS.XMPPSTATUS_DESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 12,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'keyValue',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/DataPmStatusComponent',
        component: DataPmStatusComponent,
        name: 'ANALYSIS.PMSTATUS',
        description: 'ANALYSIS.PMSTATUS_DESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 12,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'keyValue',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/SessionsDurationComponent',
        component: SessionsDurationComponent,
        name: 'ANALYSIS.SESSIONDURATION',
        description: 'ANALYSIS.SESSIONDURATIONDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/SessionsRateComponent',
        component: SessionsRateComponent,
        name: 'ANALYSIS.SESSIONRATE',
        description: 'ANALYSIS.SESSIONRATEDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/RequestsLatencyComponent',
        component: RequestsLatencyComponent,
        name: 'ANALYSIS.LATENCY',
        description: 'ANALYSIS.LATENCYDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/RequestsRateComponent',
        component: RequestsRateComponent,
        name: 'ANALYSIS.REQUESTRATE',
        description: 'ANALYSIS.REQUESTRATEDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/RequestsParsingComponent',
        component: RequestsParsingComponent,
        name: 'ANALYSIS.PARSING',
        description: 'ANALYSIS.PARSINGDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/CpuUsageComponent',
        component: CpuUsageComponent,
        name: 'ANALYSIS.CPUUSAGE',
        description: 'ANALYSIS.CPUUSAGEDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/FreeMemoryComponent',
        component: FreeMemoryComponent,
        name: 'ANALYSIS.FREEMEMORY',
        description: 'ANALYSIS.MEMORYDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/FreeDiskComponent',
        component: FreeDiskComponent,
        name: 'ANALYSIS.FREEDISK',
        description: 'ANALYSIS.FREEDISKDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/MemoryUtilizationComponent',
        component: MemoryUtilizationComponent,
        name: 'ANALYSIS.MEMORYUTILIZATION',
        description: 'ANALYSIS.MEMORYUTILIZATIONDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/DiskUtilizationComponent',
        component: DiskUtilizationComponent,
        name: 'ANALYSIS.DISKUTILIZATION',
        description: 'ANALYSIS.DISKUTILIZATIONDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'statistics',
        data: [],
        color: 'sceondary',
    },
]

export { widgets }