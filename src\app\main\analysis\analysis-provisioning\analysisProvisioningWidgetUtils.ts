import { TotalDeviceStatusComponent } from './total-device-status/total-device-status.component';
import { OnlineDeviceStatusComponent } from './online-device-status/online-device-status.component';
import { SoftwareVersionDistributionComponent } from './software-version-distribution/software-version-distribution.component';
import { ProvisioningCodeDistributionComponent } from './provisioning-code-distribution/provisioning-code-distribution.component';
import { XmppStatusComponent } from './xmpp-status/xmpp-status.component';
import { ImsRegistrationStatusComponent } from './ims-registration-status/ims-registration-status.component';
import { SimStatusComponent } from './sim-status/sim-status.component';
import { IpSecTunnelStatusComponent } from './ip-sec-tunnel-status/ip-sec-tunnel-status.component';
import { Widget } from 'app/layout/widget';

const widgets: Widget[] = [
    // {
    //     name: 'ANALYSIS.STATUSFORDEVICE',
    //     componentName: 'TotalDeviceStatus',
    //     cols: 36,
    //     rows: 3,
    //     y: 0,
    //     x: 0,
    //     minItemCols: 6,
    //     minItemRows: 2,
    //     authorized: true,
    //     component: TotalDeviceStatusComponent,
    // },
    {
        componentId: 'Analysis/OnlineDeviceStatusComponent',
        component: OnlineDeviceStatusComponent,
        name: 'ANALYSIS.STATUSFORDEVICES',
        description: 'ANALYSIS.STATUSFORDEVICES',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/SoftwareVersionDistributionComponent',
        component: SoftwareVersionDistributionComponent,
        name: 'ANALYSIS.VERSIONDISTRIBUTION',
        description: 'ANALYSIS.VERSIONDISTRIBUTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/ProvisioningCodeDistributionComponent',
        component: ProvisioningCodeDistributionComponent,
        name: 'PROVISIONING.CODEDISTRIBUTION',
        description: 'PROVISIONING.CODEDISTRIBUTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/XmppStatusComponent',
        component: XmppStatusComponent,
        name: 'ANALYSIS.XMPPSTATUS',
        description: 'ANALYSIS.XMPPSTATUS',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/ImsRegistrationStatusComponent',
        component: ImsRegistrationStatusComponent,
        name: 'ANALYSIS.IMSSTATUS',
        description: 'ANALYSIS.IMSSTATUS',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/SimStatusComponent',
        component: SimStatusComponent,
        name: 'ANALYSIS.SIMSTATUS',
        description: 'ANALYSIS.SIMSTATUS',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/IpSecTunnelStatusComponent',
        component: IpSecTunnelStatusComponent,
        name: 'ANALYSIS.IPSECSTATUS',
        description: 'ANALYSIS.IPSECSTATUS',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    }
]

export { widgets }