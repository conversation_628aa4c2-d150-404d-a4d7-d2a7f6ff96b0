import { Injectable } from '@angular/core';
import { filter } from 'rxjs/operators';
import { Router ,NavigationEnd} from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class RouterHistoryService {
  public history: string[] = [];
  constructor(
    private router:Router
  ) {
    this.router.events
    .pipe(filter(event => event instanceof NavigationEnd))
    .subscribe((event: NavigationEnd) => {
      const url = event.urlAfterRedirects;
      this.history.push(url);
      // console.log(this.history)
    });
  }

  getHistory() {
    // console.log(this.history)
    return this.history;
  }
}
