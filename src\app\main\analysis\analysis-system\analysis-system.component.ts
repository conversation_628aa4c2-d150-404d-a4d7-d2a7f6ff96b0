import { Component, ChangeDetectorRef } from '@angular/core';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { widgets } from './analysisSystemWidgetUtils';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from 'app/layout/components/base/BaseComponent';
import { AnalysisSystemService } from './analysis-system.service'
import { UserService } from 'app/auth/service/user.service';

@Component({
  selector: 'app-analysis-system',
  templateUrl: './analysis-system.component.html',
  styleUrls: ['./analysis-system.component.scss']
})
export class AnalysisSystemComponent extends BaseComponent {

  constructor(
    private _gridSystemService: GridSystemService,
    private cdr: ChangeDetectorRef,
    _genWidgetService: GenWidgetService,
    route: ActivatedRoute,
    private _analysisSystemService: AnalysisSystemService,
    _userService: UserService,
  ) {
    super(_gridSystemService, _genWidgetService, route, _userService.removeUnauthorizedComponents(widgets), _userService);
  }

  ngOnInit(): void {
    this._analysisSystemService.getServerStatus()
  }

  ngAfterViewInit() {
    setTimeout(() => { this.gridsterHeight = this._gridSystemService.gridsterHeight }, 0);
  };

  ngAfterViewChecked() {
    if (this._gridSystemService.pageResize) {
      this.gridsterHeight = this._gridSystemService.gridsterHeight
      this._gridSystemService.pageResize = false
      this.cdr.detectChanges()
    }
  }
}
