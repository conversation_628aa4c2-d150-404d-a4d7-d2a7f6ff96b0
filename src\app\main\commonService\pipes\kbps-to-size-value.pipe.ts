import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'kbpsToSizeValue'
})
export class KbpsToSizeValuePipe implements PipeTransform {

  constructor() { }


  transform(value: any): any {
    // 無效值保護
    if (value == null || value === '' || isNaN(Number(value))) {
      return '-';
    }
    if (value < 1000) {
      return value + ` Kbps`;
    } else if (value < 1000000) {
      return (value / 1000).toFixed(2) + ` Mbps`;
    } else {
      return (value / 1000000).toFixed(2) + ` Gbps`;
    }
  }

}
