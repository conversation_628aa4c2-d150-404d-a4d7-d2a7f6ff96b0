import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ChartTimePatchService {

  constructor() { }

  //如果資料的第一筆比起始時間還晚,補上起始時間點的 null 資料
  // @param dataArr //資料陣列
  //  @param days //天數
  patchStartTime(
    dataArr: { name: string; value: any[][] }[],
    days: number
  ): void {
    if (!Array.isArray(dataArr) || dataArr.length === 0) return;

    const now = new Date();
    const startTime = new Date(now.getTime() - ( days - 1 ) * 24 * 60 * 60 * 1000);//這裡days-1(配合nbi回傳)
    startTime.setMinutes(0, 0, 0); //整點
    const startTimeISO = startTime.toISOString();

    dataArr.forEach(item => {
      const firstRow = item.value?.[0];
      if (!firstRow) return;

      const firstTimeStr = firstRow[0];
      const firstTime = new Date(firstTimeStr);

      if (firstTime > startTime) {
        // 自動補對應 null 欄位
        const filler = [startTimeISO, ...new Array(firstRow.length - 1).fill(null)];
        item.value.unshift(filler);
      }
    });
  }
}
