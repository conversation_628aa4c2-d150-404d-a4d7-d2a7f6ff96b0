import { Component, ChangeDetectorRef } from '@angular/core';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { refurbishmentWidgets } from './refurbishmentWidgetsUtiles';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from 'app/layout/components/base/BaseComponent';
import { UserService } from 'app/auth/service/user.service';
@Component({
  selector: 'app-ananlysis-refurbishment',
  templateUrl: './ananlysis-refurbishment.component.html',
  styleUrls: ['./ananlysis-refurbishment.component.scss']
})
export class AnanlysisRefurbishmentComponent extends BaseComponent {

  constructor(
    private _gridSystemService: GridSystemService,
    private cdr: ChangeDetectorRef,
    _genWidgetService: GenWidgetService,
    route: ActivatedRoute,
    _userService: UserService,
  ) {
    super(_gridSystemService, _genWidgetService, route, refurbishmentWidgets, _userService)
  }

  ngAfterViewInit() {
    setTimeout(() => { this.gridsterHeight = this._gridSystemService.gridsterHeight }, 0);
  };

  ngAfterViewChecked() {
    if (this._gridSystemService.pageResize) {
      this.gridsterHeight = this._gridSystemService.gridsterHeight
      this._gridSystemService.pageResize = false
      this.cdr.detectChanges()
    }
  }

}
