import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { TranslateService } from '@ngx-translate/core';
import { CareService } from 'app/main/care/care.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import Swal from 'sweetalert2';
import { AuthenticationService } from 'app/auth/service/authentication.service';
@UntilDestroy()
@Component({
  selector: 'app-reboot-device',
  templateUrl: './reboot-device.component.html',
  styleUrls: ['./reboot-device.component.scss']
})
export class RebootDeviceComponent extends Unsubscribe implements OnInit {
  @Output() loadingEvt = new EventEmitter<string>();
  public loading: boolean = false
  public currentDeviceSN: string;
  public deviceId : string;
  @Input() editMode: boolean;
  public conReboot = this.translateService.instant('DEVICES.ACTION.CONFIRM_REBOOT');
  public doReboot = this.translateService.instant('DEVICES.ACTION.DOREBOOT');
  public isSuccess = this.translateService.instant('DEVICES.ACTION.REBOOT_SUCCESS');
  public REBOOTPENDING = this.translateService.instant('DEVICES.ACTION.REBOOTPENDING');
  public isFail = this.translateService.instant('DEVICES.ACTION.REBOOTFAIL');

  public assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
  public assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
  public assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
  public assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
  public assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
  public assignFailMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL_MESSAGE');
  public ACTION_CONFIRM = this.translateService.instant('DEVICES.ACTION.ACTION_CONFIRM');
  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'deviceAdmin', 'write');
  }
  constructor(
    private translateService: TranslateService,
    private  _careService: CareService,
    private _toastrUtilsService: ToastrUtilsService,
    private _authenticationService: AuthenticationService,
  ) {
    super();
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.conReboot = this.translateService.instant('DEVICES.ACTION.CONFIRM_REBOOT');
      this.doReboot = this.translateService.instant('DEVICES.ACTION.DOREBOOT');
      this.isSuccess = this.translateService.instant('DEVICES.ACTION.REBOOT_SUCCESS');
      this.REBOOTPENDING = this.translateService.instant('DEVICES.ACTION.REBOOTPENDING');
      this.isFail = this.translateService.instant('DEVICES.ACTION.REBOOTFAIL');
      this.ACTION_CONFIRM = this.translateService.instant('DEVICES.ACTION.ACTION_CONFIRM');
      this.assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
      this.assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
      this.assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
      this.assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
      this.assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
      this.assignFailMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL_MESSAGE');
    })
  }

  loadingChange(cal) {
    this.loadingEvt.emit(cal);
  }

  rebootDevice() {
    this.loading = true
    Swal.fire({
          title: this.conReboot + this.currentDeviceSN,
          html: `${this.doReboot}<br>${this.ACTION_CONFIRM}`,
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            this._careService.reboot(this.deviceId).then(() => {
              this.crq(this.deviceId, this.currentDeviceSN)
            }).catch((res) => {
              this.loading = false
              this._toastrUtilsService.showErrorMessage(this.assignOperation, `${this.currentDeviceSN} ${this.assignFailMsg} \n ${res.error}`);
            });
          } else {
            this.loading = false
          }
        });
    
  }

  crq(deviceId, sn) {
    this._careService.connectionRequest(deviceId).then((res) => {
      if (String(res) == 'true') {
        this.loading = false
        this.loadingChange('false')
        this._toastrUtilsService.showInfoMessage(this.assignOperationSucc, `${sn}  ${this.assignSuccessMsg}`)
      } else {
        this.loading = false
        this.loadingChange('false')
        this._toastrUtilsService.showWarningMessage(this.assignOperation, `${this.assignWarningMsg1} ${sn} ${this.assignWarningMsg2}`);
      }
    }).catch((res) => {
      this.loading = false
      this.loadingChange('false')
      this._toastrUtilsService.showWarningMessage(this.assignOperation, `${this.assignWarningMsg1} ${sn} ${this.assignWarningMsg2}`);
    });
  }



  getDeviceId(): void {
    this.customSubscribe(this._careService.careDeviceChanged, res => {
      if (res && res.id) {
        this.deviceId = res.id;
        if (res.serialNumber) {
          this.currentDeviceSN = res.serialNumber;
        } else {
          this.currentDeviceSN = res.deviceIdStruct.serialNumber;
        }
      } else {
        this.deviceId = '';
      }
    })
  }

  

  ngOnInit(): void {
    this.getDeviceId()
  }

}
