import { Component, Input, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { DashboardService } from '../dashboard.service';
import { timer, Subscription } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ColumnMode, DatatableComponent, SelectionType } from '@almaobservatory/ngx-datatable';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-wifi-total-clients',
  templateUrl: './wifi-total-clients.component.html',
  styleUrls: ['./wifi-total-clients.component.scss']
})
export class WifiTotalClientsComponent implements OnInit {
  @Input() accessor: any;
  @ViewChild(DatatableComponent) tableRowDetails: DatatableComponent;
  public loading: boolean = false;
  public totalClients: number
  public totalExcellent: number
  public totalGood: number
  public totalPoor: number
  public ColumnMode = ColumnMode;
  public SelectionType = SelectionType;
  public chartData
  public isShowNoData = true
  public selectedOption = 10;

  public colorArr = ['#3BB938', '#FFD700', '#FF3838']

  constructor(
    private _dashboardService: DashboardService,
    private modalService: NgbModal,
    private translateService: TranslateService,
    private cdr: ChangeDetectorRef,
  ) {
    this.getTotalClients();
  }

  public columnSelectOptions = [
    {
      name: 'Name', prop: 'name', translate: 'DASHBOARD.PRODUCTNAME', width:
        100, flexGrow: 100, columnStatus: true
    },
    { name: 'APs', prop: 'aps', translate: 'DASHBOARD.APS', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Online APs', prop: 'onlineAps', translate: 'DASHBOARD.ONLINEAPS', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Excellent clients', prop: 'excellent', translate: 'DASHBOARD.EXCELLENTCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Good clients', prop: 'good', translate: 'DASHBOARD.GOODCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Poor clients', prop: 'poor', translate: 'DASHBOARD.POORCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Total clients', prop: 'totalClients', translate: 'DASHBOARD.TOTALCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
  ]

  public tooltip = [
    {
      RSSI: 'DASHBOARD.EXCELLENT',
      description: 'DASHBOARD.EXCELLENT_DESCRIPTION',
      type: ''
    },
    {
      RSSI: 'DASHBOARD.GOOD',
      description: 'DASHBOARD.GOOD_DESCRIPTION',
      type: ''
    },
    {
      RSSI: 'DASHBOARD.POOR',
      description: 'DASHBOARD.POOR_DESCRIPTION',
      type: ''
    }
  ]

  public clientsTable
  public statusCount
  public tempData;

  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getTotalClients();
    });
  }

  ngOnDestroy(): void {
  }

  getTotalClients() {
    this.loading = true
    this.isShowNoData = false
    this._dashboardService.getTotalClients()
      .then(res => {

        let deviceClientsWithTimeSeries = [];
        res.forEach((item) => {
          deviceClientsWithTimeSeries.push(item)
        })
        // console.log(deviceClientsWithTimeSeries)

        let sum2g5gClientsWithTimeSeries = [];
        deviceClientsWithTimeSeries.forEach( (item) =>{
          if (item.hasOwnProperty('2G_associatedDevice') == false) {
            item['2G_associatedDevice'] = { excellentCount: 0, goodCount: 0, poorCount: 0 }
          };
          if (item.hasOwnProperty('5G_associatedDevice') == false) {
            item['5G_associatedDevice'] = { excellentCount: 0, goodCount: 0, poorCount: 0 }
          };
          if (item.hasOwnProperty('6G_associatedDevice') == false) {
            item['6G_associatedDevice'] = { excellentCount: 0, goodCount: 0, poorCount: 0 }
          };

          sum2g5gClientsWithTimeSeries.push(
            {
              totalExcellent:
                item['2G_associatedDevice'].excellentCount +
                item['5G_associatedDevice'].excellentCount +
                item['6G_associatedDevice'].excellentCount,
              totalGood:
                item['2G_associatedDevice'].goodCount +
                item['5G_associatedDevice'].goodCount +
                item['6G_associatedDevice'].goodCount,
              totalPoor:
                item['2G_associatedDevice'].poorCount +
                item['5G_associatedDevice'].poorCount +
                item['6G_associatedDevice'].poorCount,
              name: item.name,
              aps: item.aps,
              onlineAps: item.onlineAps
            }
          )
        })
        sum2g5gClientsWithTimeSeries =
          sum2g5gClientsWithTimeSeries.map( (item) =>{
            return {
              totalClients: { value: item.totalExcellent + item.totalGood + item.totalPoor },
              totalExcellent: { value: item.totalExcellent },
              totalGood: { value: item.totalGood },
              totalPoor: { value: item.totalPoor },
            }
          })
        this.totalClients = 0;
        this.totalExcellent = 0;
        this.totalGood = 0;
        this.totalPoor = 0;

        sum2g5gClientsWithTimeSeries.forEach(item => {
          this.totalClients += item.totalClients.value;
          this.totalExcellent += item.totalExcellent.value;
          this.totalGood += item.totalGood.value;
          this.totalPoor += item.totalPoor.value;
        })


        // console.log(sum2g5gClientsWithTimeSeries)
        this.chartData = [
          { name: 'Excellent RSSI', value: this.totalExcellent },
          { name: 'Good RSSI', value: this.totalGood },
          { name: 'Poor RSSI', value: this.totalPoor },
        ]

        // console.log(this.totalClients)

        this.tempData = res.map(item => {
          return {
            name: item.name || '',
            aps: item.aps || 0,
            onlineAps: item.onlineAps || 0,

            excellent:
              item['2G_associatedDevice'].excellentCount +
              item['5G_associatedDevice'].excellentCount +
              item['6G_associatedDevice'].excellentCount,
            good:
              item['2G_associatedDevice'].goodCount +
              item['5G_associatedDevice'].goodCount +
              item['6G_associatedDevice'].goodCount,
            poor:
              item['2G_associatedDevice'].poorCount +
              item['5G_associatedDevice'].poorCount +
              item['6G_associatedDevice'].poorCount,

            totalClients:
              item['2G_associatedDevice'].excellentCount +
              item['5G_associatedDevice'].excellentCount +
              item['6G_associatedDevice'].excellentCount +
              item['2G_associatedDevice'].goodCount +
              item['5G_associatedDevice'].goodCount +
              item['6G_associatedDevice'].goodCount +
              item['2G_associatedDevice'].poorCount +
              item['5G_associatedDevice'].poorCount +
              item['6G_associatedDevice'].poorCount,
            tags: item.tags
          }
        });
        this.clientsTable = this.tempData
        // console.log(this.tempData)
        // console.log(this.clientsTable)
        this.isShowNoData = this.chartData.length > 0 ? false : true
        this.cdr.detectChanges();
      })
      .catch((err) => {
        console.error(err);
        this.isShowNoData = true
      })
      .finally(() => this.loading = false)
  }

  // modal Open Backdrop Disabled
  modalOpenBD(modalBD) {

    this.modalService.open(modalBD, {
      backdrop: false,
      size: 'lg',
      scrollable: true
    });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getTotalClients();
        break;
      case 'download':
        this.triggerDownloadData()
        break;
      case 'maximize':
        this.triggerModal()
        break;
    }
  };
  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
      setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }

}
