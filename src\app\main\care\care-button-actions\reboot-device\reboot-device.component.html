<button
  *ngIf="!loading"
  [disabled]="editMode"
  (click)="rebootDevice();"
  type="button"
  class="btn btn-primary btn-sm mr-50"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.REBOOT' | translate }}"
  [disabled]="!deviceId"
  rippleEffect>
  <span><svg width="14px" height="14px"> <use href="./../assets/fonts/added-icon.svg#reboot-test"></use> </svg></span>
</button>
<button
  *ngIf="loading"
  [disabled]="editMode"
  type="button"
  class="btn btn-primary btn-sm mr-50"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.REBOOT' | translate }}"
  rippleEffect
  [disabled]="true">
  <div
    class="spinner-border spinner-border-sm"
    role="status">
  </div>
</button>
