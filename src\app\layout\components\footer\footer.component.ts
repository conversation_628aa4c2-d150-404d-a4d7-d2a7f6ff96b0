import { <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy, Component } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CoreConfigService } from '@core/services/config.service';
import { UserService } from 'app/auth/service/user.service';
import moment from 'moment';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import Swal from 'sweetalert2';
import { TranslateService } from '@ngx-translate/core';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { AuthenticationService } from 'app/auth/service';
import { ActivatedRoute, Router } from '@angular/router';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { LicenseService } from 'app/main/system/license/license.service';
import { User } from 'app/auth/models';

@Component({
  selector: 'footer',
  templateUrl: './footer.component.html'
})
export class FooterComponent implements OnInit, OnDestroy {
  public coreConfig: any;
  public year: number = new Date().getFullYear();
  public clock: any;
  public tickInterval = 1000;
  public startTime: any;
  public serverHour: any;
  public tickTimer: any;
  public _startTime = new Date().getTime();
  public count = 0;
  public intervaltask: any;
  public softWareVersion: string;
  public currentUser: User;

  // Private
  private _unsubscribeAll: Subject<void>;

  private urlMapping = {
    'dashboard': '/user-manual/getting-started',
    'devices': '/user-manual/getting-started',
    'analysis': '/user-manual/getting-started',
    'products': '/user-manual/getting-started',
    'groups': '/user-manual/grouping',
    'users': '/user-manual/user-management-and-acl',
    'events': '/user-manual/alarm-and-notification',
    'system': '/user-manual/system-preference',
    'user-manual': '/user-manual',
    'provisioning': '/user-manual/configuration-and-operation',
  };

  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   */
  constructor(
    public _coreConfigService: CoreConfigService,
    private _genWidgetService: GenWidgetService,
    private _translateService: TranslateService,
    private _toastrUtilsService: ToastrUtilsService,
    private _userService: UserService,
    private _authenticationService: AuthenticationService,
    private _router: Router,
    private _route: ActivatedRoute,
    private _gridService: GridSystemService,
    private _licenseService: LicenseService,
  ) {
    // Set the private defaults
    this._unsubscribeAll = new Subject();
  }

  // Lifecycle hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    this.currentUser = this._authenticationService.currentUserValue;
    // Subscribe to config changes
    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {
      this.coreConfig = config;
    });

    this.getTime()
    this.getACSVersion()
    // this.intervaltask = setInterval(this.getTime, 10 * 60 * 1000)

  }

  getACSVersion() {
    this._licenseService.getSoftWareVersion().then((res: any) => {
      const start = res.indexOf("ACS:") + 4; // 找到 "ACS:" 的索引，并加上 4 以跳过 "ACS:"
      const end = res.indexOf(" UI"); // 找到 " UI" 的索引
      this.softWareVersion = res.substring(start, end).trim(); // 截取字符串并去除多余空格
    })
  }

  save() {
    Swal.fire({
      title: this._translateService.instant('DEVICES.ACTION.CONFIRMSave'),
      text: this._translateService.instant('DEVICES.ACTION.SURESave'),
      showCancelButton: true,
      confirmButtonText: this._translateService.instant('COMMON.OK'),
      cancelButtonText: this._translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._genWidgetService.save().then(() => {
          this._coreConfigService.setConfig({ layout: { editMode: false } });
          this._genWidgetService.unEditMode();
          this._toastrUtilsService.showSuccessMessage(``, this._translateService.instant('DEVICES.ACTION.SAVESuccess'));
        });
      }
    });
  }

  cancel() {
    if (this._genWidgetService.notSave()) {
      Swal.fire({
        title: this._translateService.instant('CONFIRM.NOTSAVETITLE'),
        text: this._translateService.instant('CONFIRM.NOTSAVECONTENT'),
        icon: 'warning',
        showCancelButton: true,
        showCloseButton: true,
        confirmButtonText: this._translateService.instant('COMMON.SAVE'),
        cancelButtonText: this._translateService.instant('COMMON.DONTSAVE'),
        customClass: {
          confirmButton: 'btn btn-primary',
          cancelButton: 'btn btn-danger ml-1'
        }
      }).then((result) => {
        if (result.value) {
          // this.editMode = false;
          // this._genWidgetService.resetChange();
          // this._coreConfigService.setConfig({ layout: { editMode: this.editMode } });
          this._genWidgetService.save().then(() => {
            this._coreConfigService.setConfig({ layout: { editMode: false } });
            this._genWidgetService.unEditMode();
            this._toastrUtilsService.showSuccessMessage(``, this._translateService.instant('DEVICES.ACTION.SAVESuccess'));
            return result.value
          });
        } else {
          this._genWidgetService.resetChange();
          this._coreConfigService.setConfig({ layout: { editMode: false } });
        }
      });
    } else {
      this._coreConfigService.setConfig({ layout: { editMode: false } });
      this._genWidgetService.unEditMode();
    }
  }

  getTime() {
    var tick = () => {
      if (this.startTime) {
        this.count++;
        var offset = new Date().getTime() - (this._startTime + this.count * 1000);
        var nextTime = 1000 - offset;
        if (nextTime < 0) nextTime = 0;
        if (offset > 10000) {
          this.startTime.add(1, 'seconds');
          this.clock = '';
        } else {
          this.clock = this.startTime.add(1, 'seconds').format("YYYY/MM/DD HH:mm:ss");
        }
        this.tickTimer = setTimeout(tick, nextTime);
      }
    }
    if (this._authenticationService.currentUserValue) {
      this._userService.getServerTime().then((data: any) => {
        if (data) {
          this.startTime = moment(data);
          var time = new Date(data).toLocaleDateString();
          this.serverHour = new Date(time).getFullYear();
          clearTimeout(this.tickTimer)
          // $timeout.cancel(tickTimer);
          this._startTime = new Date().getTime();
          this.count = 0;
          this.tickTimer = setTimeout(tick, this.tickInterval);
        }
      }, (err) => { })
    }
  }

  helpDocument() {
    const urlManual = this._route.snapshot['_routerState'].url;
    let parts = urlManual.split('/');
    let firstPart = parts[1];
    if (this.urlMapping[firstPart]) {
      this._router.navigate([this.urlMapping[firstPart]]);
    }
  }



  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
    clearInterval(this.intervaltask)
  }
}
