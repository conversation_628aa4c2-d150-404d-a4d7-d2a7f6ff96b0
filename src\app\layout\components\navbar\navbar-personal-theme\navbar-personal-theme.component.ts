import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';

@UntilDestroy()
@Component({
  selector: 'app-navbar-personal-theme',
  templateUrl: './navbar-personal-theme.component.html',
  styleUrls: ['./navbar-personal-theme.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class NavbarPersonalThemeComponent implements OnInit {
  public editMode: boolean = false;
  public widgets: any = [];
  public formatWidgets: any = {};
  constructor(
    private _gridSystemService: GridSystemService,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService,
    private modalService: NgbModal,
    private _genWidgetService: GenWidgetService
  ) {
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CONFIRMReset = this.translateService.instant('DEVICES.ACTION.CONFIRMReset');
      this.SUREReset = this.translateService.instant('DEVICES.ACTION.SUREReset');
      this.RESETSuccess = this.translateService.instant('DEVICES.ACTION.RESETSuccess');
      this.CONFIRMSave = this.translateService.instant('DEVICES.ACTION.CONFIRMSave');
      this.SURESave = this.translateService.instant('DEVICES.ACTION.SURESave');
      this.SAVESuccess = this.translateService.instant('DEVICES.ACTION.SAVESuccess');
    })
  }

  public CONFIRMReset = this.translateService.instant('DEVICES.ACTION.CONFIRMReset');
  public SUREReset = this.translateService.instant('DEVICES.ACTION.SUREReset');
  public RESETSuccess = this.translateService.instant('DEVICES.ACTION.RESETSuccess');
  public CONFIRMSave = this.translateService.instant('DEVICES.ACTION.CONFIRMSave');
  public SURESave = this.translateService.instant('DEVICES.ACTION.SURESave');
  public SAVESuccess = this.translateService.instant('DEVICES.ACTION.SAVESuccess');


  public subClassMapping = {
    'GeneralData': 'General Data',
    'CellularSpecific': 'Cellular Specific',
    'WifiSpecific': 'Wi-Fi Specific',
    'StatisticalAnalysis': 'Statistical Analysis',
    'AppSpecific': 'APP Specific',
    'FapSpecific': 'FAP Specific',
    'AccountAdmin': 'Account Admin',
    'DeviceAlarm': 'Device Alarm',
    'GroupAdmin': 'Group Admin',
    'SystemEvents': 'System Events',
    'DeviceStatistics': 'Device Statistics',
    'DeviceAdmin': 'Device Admin',
    'SystemStatistics': 'System Statistics',
    'PmStatistics': 'PM Statistics',
  }

  /**
   * Change to Edit or Unedit mode
   */
  onChangeEditMode() {
    switch (this.editMode) {
      case true:
        // this._gridSystemService.editMode();
        this._genWidgetService.editMode();
        break;
      case false:
        // this._gridSystemService.unEditMode();
        this._genWidgetService.unEditMode();
        break;
    }
  };

  /**
   * Change Personal theme to default
   */
  reset() {
    Swal.fire({
      title: this.CONFIRMReset,
      text: this.SUREReset,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._gridSystemService.resetPersonalTheme().then(() => {
          this.editMode = false;
          this._gridSystemService.unEditMode();
          this._toastrUtilsService.showSuccessMessage(``, this.RESETSuccess);
          window.location.reload();
        });
      }
    });
  };

  capitalize(str) {
    return str && str.charAt(0).toUpperCase() + str.slice(1);
  }

  grouping(widgets) {
    this.formatWidgets = [];
    let temp = {};
    widgets.forEach(item => {
      let key = this.capitalize(item.subClass)
      // let key = this.capitalize(item.class) + ' - ' + this.capitalize(item.subClass)
      temp[key] ? temp[key].push(item) : temp[key] = [item];
    });
    for (var key in temp) {
      let widgets = temp[key];
      this.formatWidgets.push({ key, widgets })
    }
    console.log(this.formatWidgets)
    return this.formatWidgets
  }

  /**
   * Edit Component theme to default
   */
  editComponent(editModal) {
    this.widgets = this._genWidgetService.renderWidgets;
    this.grouping(this.widgets);
    this.modalService.open(editModal, {
      backdrop: 'static',
      size: 'lg',
      modalDialogClass: 'modal-custom',
      scrollable: true
    });
  }

  statusChange(item) {
    item.hidden = !item.hidden
    this.saveWidgets();
  }

  saveWidgets() {
    this._genWidgetService.widgetChanged(this.widgets);
  }

  /**
   * Save Personal theme
   */
  save() {
    Swal.fire({
      title: this.CONFIRMSave,
      text: this.SURESave,
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._gridSystemService.savePersonalTheme().then(() => {
          this.editMode = false;
          this._genWidgetService.unEditMode();
          this._toastrUtilsService.showSuccessMessage(``, this.SAVESuccess);
        });
      }
    });
  };
  ngOnInit(): void { }
}
