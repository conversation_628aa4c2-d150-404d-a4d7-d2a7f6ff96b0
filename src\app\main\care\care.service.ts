import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { map, catchError, tap, share } from "rxjs/operators";

@Injectable({
  providedIn: 'root'
})
export class CareService {
  public deviceDataObservable: Observable<any>;

  //device
  public careDeviceChanged: BehaviorSubject<any>;

  //client
  public clientCountObservable: Observable<any>;

  /**
   * Constructor
   *
   * @param {HttpClient} _httpClient
   */
  constructor(
    private _httpClient: HttpClient,
   
  ) {
    // Set the defaults
    this.careDeviceChanged = new BehaviorSubject({});
   
  }

   /**
  * Get deviceId  this._httpClient.get(`nbi/alarm/deviceAlarm/deviceActAlarmAllCount?deviceId=${deviceId}`)
  */
   getDeviceId(val,searchDuration): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`/nbi/external/device/query?${searchDuration}=${val}`).subscribe({
        next: (response: any) => {
          this.careDeviceChanged.next(response)
            resolve(response);
         },
        error: (error)=>{
            reject(error);
            this.careDeviceChanged.next([])
        }
      });
    });
  }


    /**
  * Get device-info
  */
  // getDeviceInfo(deviceId): Observable<any[]> {
  //     console.log('deviceId=',deviceId)
  //     if (this.deviceDataObservable) {
  //       return this.deviceDataObservable;
  //     } else {
  //       this.deviceDataObservable = this._httpClient.get(`nbi/device/generalData/${deviceId}/deviceInfo`).pipe(
  //         tap((resp: any) => {
  //           this.deviceInfoData = resp
  //           this.ondeviceInfoChanged.next(this.deviceInfoData);
  //         }),
  //         share(),
  //         catchError(error => throwError(() => error))
  //       );
  //       return this.deviceDataObservable;
  //     }
  //   }
  getDeviceInfo(deviceId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/generalData/${deviceId}/deviceInfo`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }
    

  /**
  * Get error-status
  */
  getErrorStatus(deviceId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/generalData/${deviceId}/errors`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

/**
  * Get map location
  */
getLocation(deviceId): Promise<any[]> {
  return new Promise((resolve, reject) => {
    this._httpClient.get(`nbi/device/networkLocation/${deviceId}/locationInfo`).subscribe({
      next: (response: any) => {
        resolve(response);
      },
      error: reject
    });
  });
}

  /**
  * Refresh device location
  */
  refreshLocation(deviceId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/networkLocation/${deviceId}/location`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

/**
  * Get clients List
  */
getClientsList(deviceId): Observable<any[]> {
  if (this.clientCountObservable) {
    return this.clientCountObservable;
  } else {
    this.clientCountObservable = this._httpClient.get(`nbi/device/wifiSpecific/${deviceId}/wifiStation`).pipe(
      map((resp: any) => resp),
      share(),
      catchError(error => throwError(() => error))
    );
    return this.clientCountObservable;
  }
}

getLinkRateHistory(deviceId, mac): Promise<any[]> {
  return new Promise((resolve, reject) => {
    this._httpClient.get(`nbi/device/wifiSpecific/${deviceId}/linkRateHistory?mac=${mac}`).subscribe({
      next: (response: any) => {
        resolve(response);
      },
      error: reject
    });
  });
}

/**
  * button
  * upgrade device
  */

/**
  * latestFirmware
  */
latestFirmware(deviceId: string): Promise<any[]> {
  return new Promise((resolve, reject) => {
    this._httpClient.get(`nbi/device/deviceAdmin/${deviceId}/firmware/latest`).subscribe({
      next: (response: any) => {
        resolve(response);
      },
      error: reject
    });
  });
}
/**
* upgradeFirmware
*/
upgradeFirmware(deviceId: string, firmwareId: string, status?): Promise<any[]> {
  let url = status ? `nbi/device/deviceAdmin/${deviceId}/download/${firmwareId}?force=1` : `nbi/device/deviceAdmin/${deviceId}/download/${firmwareId}`
  return new Promise((resolve, reject) => {
    this._httpClient.post(url, null).subscribe({
      next: (response: any) => {
        resolve(response);
      },
      error: reject
    });
  });
}

  /**
  * ConnectionRequest
  */
  connectionRequest(deviceId: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/deviceAdmin/${deviceId}/liveUpdate`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }


getUpgradeStatus(deviceId) {
  return new Promise((resolve, reject) => {
    this._httpClient.get(`nbi/device/generalData/${deviceId}/upgradeStatus`).subscribe({
      next: (response: any) => {
        resolve(response);
      },
      error: reject
    });
  });
}

  /**
  * Reboot
  */
  reboot(deviceId: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/deviceAdmin/${deviceId}/reboot`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

    /**
  * FactoryReset
  */
    factoryReset(deviceId: string): Promise<any[]> {
      return new Promise((resolve, reject) => {
        this._httpClient.post(`nbi/device/deviceAdmin/${deviceId}/factoryReset`, null).subscribe({
          next: (response: any) => {
            resolve(response);
          },
          error: reject
        });
      });
    }

      /**
  * UploadLog
  */
  uploadLog(deviceId: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/deviceAdmin/${deviceId}/logs/upload`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }
  

 
}


