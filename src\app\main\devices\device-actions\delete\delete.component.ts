import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { DeviceActionsService } from '../device-actions.service';
import { GroupInfoService } from 'app/main/groups/group-info/group-info.service';
import { GroupsResolveService } from 'app/main/groups/groups-resolve.service';
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';
import { AuthenticationService } from 'app/auth/service/authentication.service';

@UntilDestroy()
@Component({
  selector: 'app-delete',
  templateUrl: './delete.component.html',
  styleUrls: ['./delete.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DeleteComponent implements OnInit {
  public blockUIStatus = false;
  @Output() deleteDeviceEvt = new EventEmitter<object>();

  @Input() mode: string;
  @Input() row: string;
  @Input() selectedDevice?: string;
  @Input() selectedDevices?;
  @Input() deploymentMode?: number;

  public currentDevice: string;
  public currentDeviceSN: string;
  public selectSingleSN: string;
  public CONFIRMDelete = this.translateService.instant('DEVICES.ACTION.CONFIRM_DELETE');
  public DODELETE = this.translateService.instant('DEVICES.ACTION.DODELETE');
  public DELETESUCCESS = this.translateService.instant('DEVICES.ACTION.DELETESUCCESS');
  public DELETEFAIL = this.translateService.instant('DEVICES.ACTION.DELETEFAIL');
  public DO_DELETE = this.translateService.instant('DEVICES.ACTION.DO_DELETE');
  public PleaseSelect = this.translateService.instant('DEVICES.ACTION.PLESESELECT');
  public REMOVEFROMGROUP = this.translateService.instant('COMMON.REMOVEFROMGROUP');
  public CONFIRM = this.translateService.instant('GROUPS.CONFIRM');
  public isGroup: boolean;
  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'deviceAdmin', 'write');
  }
  public get noGroupPermission(): boolean {
    return !this._authenticationService.check('device', 'groupAdmin', 'write');
  }
  constructor(
    private _deviceActionsService: DeviceActionsService,
    private route: ActivatedRoute,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService,
    private _groupInfoService: GroupInfoService,
    private _groupsResolveService: GroupsResolveService,
    private _authenticationService: AuthenticationService,
  ) {
  }

  deleteChange(message: string, ids?: string[]) {
    this.deleteDeviceEvt.emit({ message, ids });
  }


  delete() {
    switch (this.mode) {
      case 'currentDevice':
        Swal.fire({
          title: this.CONFIRMDelete,
          text: this.DODELETE + this.currentDeviceSN + "?",
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            this._deviceActionsService.delete(this.currentDevice).then(() => {
              this._toastrUtilsService.showSuccessMessage(this.DELETESUCCESS, `S/N: ${this.currentDeviceSN}`)
            }).catch((err) => {
              this._toastrUtilsService.showErrorMessage(this.DELETEFAIL, err.error);
            });
          }
        });
        break;
      case 'singleDevice':
        this.selectSingleSN = this.row['deviceIdStruct'].serialNumber
        this.isGroup = this.route.snapshot['_routerState'].url.endsWith("group-info")
        Swal.fire({
          title: this.isGroup ? this.CONFIRM + ' ' + this.REMOVEFROMGROUP : this.CONFIRMDelete,
          text: this.DODELETE + this.selectSingleSN + "?",
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            if (this.isGroup) {
              this._groupInfoService.deleteManyGroupMember(this.currentDevice, [this.selectSingleSN]).then((res: any) => {
                this._toastrUtilsService.showSuccessMessage('Delete Group Member Success!', null);
                this.deleteChange(this.DELETESUCCESS)
                this._groupInfoService.informState(true)
              }).catch(err => {
                this._toastrUtilsService.showErrorMessage('Delete Group Member Fail!', err.error);
              })
            } else {
              this._deviceActionsService.delete(this.selectedDevice).then(() => {
                this._toastrUtilsService.showSuccessMessage(this.DELETESUCCESS, `S/N: ${this.selectSingleSN}`)
                this.deleteChange(this.DELETESUCCESS)
              }).catch((err) => {
                this._toastrUtilsService.showErrorMessage(this.DELETEFAIL, err.error);
              });
            }
          }
        });
        break;
      case 'multiDevices':
        this.isGroup = this.route.snapshot['_routerState'].url.endsWith("group-info")
        if (this.selectedDevices.length) {
          Swal.fire({
            title: this.isGroup ? this.CONFIRM + ' ' + this.REMOVEFROMGROUP : this.CONFIRMDelete,
            text: this.DO_DELETE,
            showCancelButton: true,
            confirmButtonText: this.translateService.instant('COMMON.OK'),
            cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
            customClass: {
              confirmButton: 'btn btn-primary',
              cancelButton: 'btn btn-danger ml-1'
            }
          }).then((result) => {
            if (result.value) {
              if (this.isGroup) {
                let memberList = this.selectedDevices.map(item => item.deviceIdStruct.serialNumber)
                this._groupInfoService.deleteManyGroupMember(this.currentDevice, memberList).then((res: any) => {
                  this._toastrUtilsService.showSuccessMessage('Delete Group Member Success!', null);
                  this.deleteChange(this.DELETESUCCESS, this.selectedDevices.map(item => item.id))
                  this._groupInfoService.informState(true)
                }).catch(err => {
                  this._toastrUtilsService.showErrorMessage('Delete Group Member Fail!', err.error);
                })
              } else {
                let deviceArr = []
                this.selectedDevices.forEach(d => {
                  deviceArr.push(d.id)
                })
                this._deviceActionsService.deleteMulti(deviceArr).then(() => {
                  this._toastrUtilsService.showSuccessMessage(this.DELETESUCCESS, null)
                  this.deleteChange(this.DELETESUCCESS, deviceArr)
                }).catch((err) => {
                  this._toastrUtilsService.showErrorMessage(this.DELETEFAIL, err.error);
                })
              }
            }
          });
        } else {
          Swal.fire({
            title: this.PleaseSelect,
            icon: "warning",
            showCancelButton: false,
            confirmButtonText: this.translateService.instant('COMMON.OK'),
            customClass: {
              confirmButton: 'btn btn-primary',
            }
          })
        }
        break;
    }
  }


  getDevice() {
    this.route.data.pipe(untilDestroyed(this)).subscribe(res => {
      // console.log(res)
      this.currentDeviceSN = res.basicInfo.deviceIdStruct.serialNumber;
    });
  }


  ngOnInit(): void {
    this.currentDevice = this.route.snapshot.paramMap.get('id')
    this.isGroup = this.route.snapshot['_routerState'].url.endsWith("group-info")
    if (this.currentDevice && !this.isGroup) {
      this.getDevice()
    }
  }

}
