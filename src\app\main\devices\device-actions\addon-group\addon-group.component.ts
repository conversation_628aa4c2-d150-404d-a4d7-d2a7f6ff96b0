import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute } from '@angular/router';
import { ColumnMode, SelectionType } from '@almaobservatory/ngx-datatable';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { DeviceActionsService } from '../device-actions.service';
import { TranslateService } from '@ngx-translate/core';
import { Observable, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { UserService } from 'app/auth/service/user.service';
import { SanityTestService } from 'app/main/commonService/sanity-test.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
@UntilDestroy()
@Component({
  selector: 'app-addon-group',
  templateUrl: './addon-group.component.html',
  styleUrls: ['./addon-group.component.scss']
})
export class AddonGroupComponent implements OnInit {
  @Input() mode: string;
  @Input() selectedDevice?: string;
  @Input() selectedDevices?;
  @Input() row?: any;
  @Output() addonGroupEvt = new EventEmitter<string>();
  public currentDevice: any;
  public selectedOption = 10;
  public ColumnMode = ColumnMode;
  public SelectionType = SelectionType;
  public groupList: any
  public groupNameList: any;
  public groupSelected;
  public deviceArr = []
  public serialArr = []
  public autoFiltering = false
  public currentDeviceInfo;
  public currentDeviceSN: string;
  public protocolOption = [{ name: "CWMP", value: "cwmp" }, { name: "USP", value: "usp" }, { name: "NETCONF", value: "netconf" }]
  public protocolSelected = null;
  public loading = false;
  public formError: any = {}
  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'groupAdmin', 'write');
  }
  constructor(
    private modalService: NgbModal,
    private route: ActivatedRoute,
    private _groupListService: GroupListService,
    private _deviceActionsService: DeviceActionsService,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService,
    private _userService: UserService,
    private st: SanityTestService,
    private _authenticationService: AuthenticationService,
  ) {
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.FAIL = this.translateService.instant('GROUPS.DIFFRENTPROTOCOLNOTALLOWED');
    })
  }

  public FAIL = this.translateService.instant('GROUPS.DIFFRENTPROTOCOLNOTALLOWED');

  /**
   * @description: Typeahead search
   * @param {*} text
   * @return {*}
   */
  search: OperatorFunction<string, readonly string[]> = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      map(term => term === '' ? []
        : this.groupNameList.filter(v => v.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10))
    )
  modalOpenSC(modalSC) {
    let protocolObj = {}
    this.modalService.open(modalSC, {
      backdrop: 'static',
      size: 'lg',
      scrollable: true
    });
    this.deviceArr = [];
    this.serialArr = [];
    switch (this.mode) {
      case 'singleDevice':
        this.deviceArr.push({
          serial: this.row.serial,
          oui: this.row.deviceIdStruct.oui,
          productClass: this.row.productClass,
          productName: this.row.productName,
          modelName: this.row.ModelName
        })
        this.serialArr.push(this.row.serial)
        this.protocolSelected = this.row.protocol
        break;
      case 'multiDevices':
        this.selectedDevices.forEach(item => {
          if (!protocolObj[item.protocol]) {
            protocolObj[item.protocol] = true
          }
        })
        if (Object.keys(protocolObj).length > 1) {
          this._toastrUtilsService.showErrorMessage('Device Group', this.FAIL);
          return false
        }
        this.selectedDevices.forEach(item => {
          this.deviceArr.push({
            serial: item.serial,
            oui: item?.deviceIdStruct?.oui,
            productClass: item.productClass,
            productName: item.productName,
            modelName: item.ModelName
          })
          this.serialArr.push(item.serial)
        })
        this.protocolSelected = Object.keys(protocolObj)[0]
        break;
      case 'currentDevice':
        this.deviceArr.push({
          serial: this.currentDeviceInfo.serial,
          oui: this.currentDeviceInfo.deviceIdStruct.oui,
          productClass: this.currentDeviceInfo.deviceIdStruct.productClass,
          productName: this.currentDeviceInfo.productName,
          modelName: this.currentDeviceInfo.ModelName
        })
        this.serialArr.push(this.currentDeviceInfo.serial)
        this.protocolSelected = this.currentDeviceInfo.protocol
        break;
      case 'multiPMDevices':
        let params = this.selectedDevices.map(item => item.serialNumber)
        this._deviceActionsService.getPMDeviceInfo(params).then((res: any) => {
          this.deviceArr = JSON.parse(res)
          this.serialArr = JSON.parse(res).map(item => item.serial)
          this.protocolSelected = JSON.parse(res)[0]?.protocol
        })
        break;
    }
    this.getGroupsDeviceListRes()
  }
  removeDevice(row) {
    this.deviceArr = this.deviceArr.filter(item => item.serial !== row.serial);
    this.serialArr = this.deviceArr.map(item => item.serial)
  }
  getGroupsDeviceListRes() {
    this._groupListService.getGroupNameList().pipe(untilDestroyed(this)).subscribe(res => {
      this.groupList = res.filter(item => this.protocolSelected == item.protocol)
      this.groupNameList = res.filter(item => this.protocolSelected == item.protocol).map(item => item.name);
    })
  }

  checkParams(value) {
    this.formError = {}
    let errorMsg: string;
    this.formError["groupName"] = this.st.check("groupName", value)
    for (let i in this.formError) {
      if (this.formError[i]) {
        errorMsg = `${i}:${this.formError[i]}`
        break
      }
    }
    if (!!errorMsg) {
      this._toastrUtilsService.showWarningMessage("Device Group", errorMsg);
    }
    return !!errorMsg
  }

  addDeviceToGroup(groupSelected, modal) {
    this.loading = true
    const groupItem = this.groupList.find(item => item.name === groupSelected);
    if (groupItem) {
      let groupId = groupItem.id;
      let groupName = groupSelected;
      this._deviceActionsService.addDeviceToGroupBysnFile(groupId, this.autoFiltering, this.serialArr).then(() => {
        this._toastrUtilsService.showSuccessMessage('Device Group', `Device Group ${groupName} was updated successfully`)
        modal.dismiss('Cross click')
      }).catch((res) => {
        this._toastrUtilsService.showErrorMessage('Device Group', res.error);
      }).finally(() => {

        this.loading = false
        this.addonGroupEvt.emit("true")
      });
    } else {
      let products = new Set<string>()
      this.deviceArr.forEach(item => {
        products.add(item.productName);
      })
      if (this.checkParams(groupSelected)) {
        this.loading = false
        return false
      }
      let params = {
        devices: this.serialArr,
        products: Array.from(products),
        protocol: this.protocolSelected
      }
      this._deviceActionsService.createNewDeviceGroupBySN(groupSelected, this.autoFiltering, params).then(() => {
        this._toastrUtilsService.showSuccessMessage('Device Group', `Device Group ${groupSelected} was created successfully`)
        modal.dismiss('Cross click')
      }).catch((res) => {
        this._toastrUtilsService.showErrorMessage('Device Group', res.error);
      }).finally(() => {
        this.loading = false
        this.addonGroupEvt.emit("true")
      });
    }
  }

  getDevice() {
    this.route.data.pipe(untilDestroyed(this)).subscribe(res => {
      // console.log(res)
      this.currentDeviceInfo = res.basicInfo
      this.currentDeviceSN = res.basicInfo.deviceIdStruct.serialNumber;
    });
  }

  onChangeType(event) {
    this.filterGroupList(event.value);
  }

  filterGroupList(type) {
    this.groupNameList = this.groupList.filter(item => item.protocol && item.protocol === type).map(item => item.name);
  }

  ngOnInit(): void {
    this.currentDevice = this.route.snapshot.paramMap.get('id')
    if (this.currentDevice) {
      this.getDevice()
    }
  }
}
