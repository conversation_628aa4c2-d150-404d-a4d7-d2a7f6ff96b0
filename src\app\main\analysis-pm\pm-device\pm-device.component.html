<div class="content-wrapper container-fluid p-0">
  <div class="content-body">
    <div *ngIf="resNum>0">
      <div
        class="d-flex flex-wrap"
        *blockUI="'chartBlock'">
        <div
          *ngIf="rateSeries.length>0"
          class="mb-1 "
          [ngClass]="getColumnClass(0)">
          <core-card 
            [actions]="['download']"
            (events)="emittedEvents($event,'rate')">
            <h4 class="card-title w-100">{{ rateSeries[0].title }}</h4>
            <div>
              <app-pm-line-chart
                [path]="'device'"
                [sn]="sn"
                [title]="sn+'_'+rateSeries[0].title"
                [chartData]="rateSeries"
                [unit]="rateSeries[0].unit"></app-pm-line-chart>
            </div>
          </core-card>
        </div>
        <div
          *ngIf="throughputSeries.length>0"
          class="mb-1"
          [ngClass]="getColumnClass(1)">
          <core-card 
            [actions]="['download']"
            (events)="emittedEvents($event,'throughput')">
            <h4 class="card-title w-100">{{ throughputSeries[0].title }}</h4>
            <div>
              <app-pm-line-chart
                [path]="'device'"
                [sn]="sn"
                [title]="sn+'_'+throughputSeries[0].title"
                [chartData]="throughputSeries"
                [unit]="throughputSeries[0].unit"></app-pm-line-chart>
            </div>
          </core-card>
        </div>
        <div
          *ngIf="fiveQISeries.length>0"
          class="mb-1 col-6"
          [ngClass]="getColumnClass(2)">
          <core-card 
            [actions]="['download']"
            (events)="emittedEvents($event,'fiveQI')">
            <h4 class="card-title w-100">{{ fiveQISeries[0].title }}</h4>
            <div>
              <app-pm-line-chart
                [path]="'device'"
                [sn]="sn"
                [title]="sn+'_'+fiveQISeries[0].title"
                [chartData]="fiveQISeries"
                [unit]="fiveQISeries[0].unit"></app-pm-line-chart>
            </div>
          </core-card>
        </div>
        <div
          *ngIf="countSeries.length>0"
          class="mb-1 col-6"
          [ngClass]="getColumnClass(3)">
          <core-card 
            [actions]="['download']"
            (events)="emittedEvents($event,'count')">
            <h4 class="card-title w-100">{{ countSeries[0].title }}</h4>
            <div>
              <app-pm-line-chart
                [path]="'device'"
                [sn]="sn"
                [title]="sn+'_'+countSeries[0].title"
                [chartData]="countSeries"
                [unit]="countSeries[0].unit"></app-pm-line-chart>
            </div>
          </core-card>
        </div>
      </div>
    </div>
    <div *ngIf="resNum==0">
      <div *blockUI="'chartBlock'">
        <ngb-alert
          [type]="'info'"
          [dismissible]="false">
          <div class="alert-body">No PM data.</div>
        </ngb-alert>
      </div>
    </div>
  </div>
</div>
