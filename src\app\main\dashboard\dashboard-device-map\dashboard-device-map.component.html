<core-card [actions]="['reload']" [componentId]="accessor.componentId" [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <!-- warning message -->
  <div *ngIf="ismapErr" class="map-overlay-warning">
    <div class="badge badge-warning" style="white-space: normal;font-size: 14px;">
      <i [data-feather]="'alert-triangle'"></i>
      {{mapType == '0' ? GOOGLEMAPERROR : OSMERROR}}
    </div>
  </div>
  <!-- Google Maps -->
  <div *ngIf="mapType == '0' && (deviceApiLoaded$ | async)" class="card-body h-100 w-100" style="cursor: default;">
    <google-map #googleMap height="100%" width="100%" [options]="mapOptions"[center]="mapCenter" [zoom]="mapZoom" >
      <map-info-window>
        <div [innerHTML]="infoContent"></div>
      </map-info-window>
    </google-map>
  </div>
  <!-- Open Street Map -->
  <div *ngIf="mapType =='1'" class="card-body h-100 w-100" style="cursor:default;">
    <div id='osmmap' #osmmap style="height: 100%;width: 100%;margin: 0;padding: 0;">
    </div>
  </div>
</core-card>