import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CareDataFormatService {

  constructor() { }
  dataFormat(res) {
    res.forEach(item => {
      if (item.reportedValue == null || item.reportedValue == undefined) {
        item.reportedValue = '-'
      } else {
        item.reportedValue = item.reportedValue.toString();
      }
      // find index
      item.index = item.name.split('.')[1]
    });
    // console.log(res)

    // regroup array by index
    let dataArr = [];
    res.map(mapItem => {
      if (dataArr.length == 0) {
        dataArr.push({ index: mapItem.index, List: [mapItem] })
      } else {
        let res2 = dataArr.some(item => {
          if (item.index == mapItem.index) {
            item.List.push(mapItem)
            return true
          }
        })
        if (!res2) {
          dataArr.push({ index: mapItem.index, List: [mapItem] })
        }
      }
    })
    // console.log(dataArr)
    // regroup objects
    let Arr = []
    dataArr.forEach(item => {
      var obj = {}
      item.List.forEach(element => {
        var key = element.name.split('.')[2]
        var value = element.reportedValue
        obj[key] = value
        // console.log('obj', obj)
      });
      Arr.push(obj)
    })
    // console.log(Arr)
    return Arr
  }

}
