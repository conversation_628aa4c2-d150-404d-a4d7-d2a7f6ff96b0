import { Component, Input } from '@angular/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { AnalysisProvisioningService } from 'app/main/analysis/analysis-provisioning/analysis-provisioning.service';
import { timer } from 'rxjs';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-ip-sec-tunnel-status',
  templateUrl: './ip-sec-tunnel-status.component.html',
  styleUrls: ['./ip-sec-tunnel-status.component.scss']
})
export class IpSecTunnelStatusComponent extends Unsubscribe {
  @Input() accessor: any;
  public blockUIStatus = false;
  public personalTheme;

  constructor(
    private _analysisProvisioningService: AnalysisProvisioningService,
    private route: ActivatedRoute,
  ) {
    super()
    this.customSubscribe(route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  public chartData = []

  public componentId = "Analysis/" + this.constructor.name;
  public productNameSelect: any
  public selectedProductName = 'All'

  changeFn(selected) {
    // console.log(selected.target.value)
    // if (selected === undefined) {
    //   selected = 'All';
    // }
    this.selectedProductName = selected.target.value
    // console.log(this.selectedProductName);
    this._analysisProvisioningService.saveSelectedProductName(this.componentId, { productName: selected.target.value });
    this.reportAnalysisProvisioningRes(this.selectedProductName)
  }

  /**
    * get AnalysisProvisioning product name
    */
  getAnalysisProvisioningProductName() {
    this.customSubscribe(this._analysisProvisioningService.onAvailStatisticsProductNamesChanged, res => {
      if (res === null) {
        this._analysisProvisioningService.getAnalysisProvisioningProductName().then()
      } else {
        this.productNameSelect = res;
      }
    })
  }
  /**
    * report AnalysisProvisioning
    */
  reportAnalysisProvisioningRes(productName) {
    this.blockUIStatus = true;
    this._analysisProvisioningService.reportAnalysisProvisioning(productName, 'ipsec').then((res) => {
      // console.log(res)
      this.parseOnlineProvisionStatusData(res);
    }).finally(() => {
      this.blockUIStatus = false;
    });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.reportAnalysisProvisioningRes(this.selectedProductName)
        break;
      case 'download':
        this.triggerDownloadData()
        break;
      case 'maximize':
        this.triggerModal()
        break;
    }
  };
  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }


  parseOnlineProvisionStatusData = function (data) {
    var seriesData = [];
    var total = data["Online Devices"];
    var online = data["IPSec Tunnel Status"];
    var offline = total - online;
    seriesData.push({ name: "IPSec Connected", value: online });
    seriesData.push({ name: "IPSec DisConnected", value: offline });
    this.chartData = seriesData;
  };

  takeTwoDecimalPercent = function (data, total) {
    if (total == 0) {
      return '0%'
    }
    return Math.round(data * 10000 / total) / 100 + '%';
  }



  ngOnInit(): void {
    this.componentId = this.accessor?.componentId;
    let data = this._analysisProvisioningService.getSelectedProductName(this.componentId);
    this.selectedProductName = data && data.productName || 'All';
    this.getAnalysisProvisioningProductName()
    this.reportAnalysisProvisioningRes(this.selectedProductName)
    this.customSubscribe(timer(600000, 600000), () => {
      this.reportAnalysisProvisioningRes(this.selectedProductName)
    });
  }

}
