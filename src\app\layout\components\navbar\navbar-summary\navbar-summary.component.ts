import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, ViewEncapsulation, ViewChild, ElementRef, ChangeDetectorRef, AfterViewChecked, Input } from '@angular/core';
import { SummarysService } from 'app/layout/components/navbar/navbar-summary/summarys.service';
import { FileDownloadService } from 'app/main/commonService/file-download.service';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { ProductDataService } from 'app/main/products/products.service';
import { UserService } from 'app/auth/service/user.service';
@UntilDestroy()
@Component({
  selector: 'app-navbar-summary',
  templateUrl: './navbar-summary.component.html',
  styleUrls: ['./navbar-summary.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class NavbarSummaryComponent implements OnInit, AfterViewChecked {
  @Input() editMode: boolean;

  public allProductNames: any = [];
  public summaryReportProductNames: any = [];
  public curProductName: any = ["All"];
  public curProtocol: any = 'cwmp';
  public summaryReportFields: any;
  public summaryReportConfig: any = {};
  public isSelectedAllFields: any = false;
  public timeRange: any = [{ key: 0, value: "Last 24 Hrs" }, { key: 1, value: "Last 12 Hrs" }, { key: 2, value: "Valid From" }]
  public formateMap: any = [{ key: 'txt', value: "TXT" }, { key: 'csv', value: "CSV" }, { key: 'xlsx', value: "XLSX" }]
  public protocolOption = [
    // { name: 'CWMP', value: 'cwmp' },
    // { name: 'USP', value: 'usp' },
    // { name: 'Netconf', value: 'netconf' },
  ];
  public basicDPdata: NgbDateStruct;
  public maxDate: NgbDateStruct;
  public curDuration: any = this.timeRange[0];
  public selectedFormat: any = "txt";
  public loading: any = false;
  public numberRegex = /^\d*$/;
  @ViewChild('menuClose') menuClose: ElementRef;
  constructor(
    private _summarysService: SummarysService,
    private _fileDownloadService: FileDownloadService,
    private toastr: ToastrUtilsService,
    private _productDataService: ProductDataService,
    private cdr: ChangeDetectorRef,
    private _userService: UserService,
  ) { 
    const today = new Date();
    this.maxDate = { year: today.getFullYear(), month: today.getMonth() + 1, day: today.getDate() };
  }


  ngOnInit(): void {
    this.getProductAndSummary()
    this.getProtocolLicense()
  }

  ngAfterViewChecked(): void {
    if (this._summarysService.informNavbar && this.menuClose['_open']) {
      this.getProductAndSummary()
      this._summarysService.informNavbar = false
    }
    this.cdr.detectChanges()
  }

  getProtocolLicense() {
    const protocolMap = {
      'cwmp': 'CWMP (TR-069)',
      'usp': 'USP (TR-369)',
      'netconf': 'NETCONF'
    };
    this._userService.getPageAuthority('protocol').then((data: any) => {
      this.protocolOption = data.map((item) => {
        return protocolMap[item] && {
          name: protocolMap[item],
          value: item
        }
      }).filter(Boolean);
    })
  }

  selectedFormateChange(e) {
    this.selectedFormat = e.target.value;
  }

  getProductAndSummary() {
    this._productDataService.getProductList().pipe(untilDestroyed(this)).subscribe({
      next: res => {
        this.allProductNames = res.map(item => {
          return {
            name: item.name,
            protocol: item.protocol ? item.protocol : 'cwmp'
          }
        })
        this.summaryReportProductNames = [{ "name": "All" }].concat(this.allProductNames.filter(item => item.protocol == this.curProtocol).sort((a, b) => {
          if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
          return 1
        }));
        // this.selectProductDisabled()
      },
      error: error => {
        console.log(error);
      },
      complete: () => {
        let cpn = this.curProductName ? (!this.curProductName || this.curProductName.length > 1 || this.curProductName.length == 0) ? 'All' : this.curProductName[0] : (this.summaryReportProductNames)[0]
        this._summarysService.getSummaryReportConfig(cpn).then(res => {
          this.summaryReportFields = res
          this.summaryReportConfig = {
            currentproduct: this.curProductName,
            entries: this.summaryReportFields.map(field => {
              return {
                key: field.name,
                label: field.labelName,
                enabled: field.include
              }
            })
          };
          this.isSelectedAllFields = true;
        })
      }
    })
  }

  selectSingleFields(e, obj) {
    obj.include = e.target.checked
    this.isSelectedAllFields = this.summaryReportFields.every(item => {
      return item.include === true;
    });
  }

  selectAllFields(e) {
    this.isSelectedAllFields = e.target.checked
    this.summaryReportFields.forEach(obj => {
      obj.include = this.isSelectedAllFields
    });
  }


  changeNameForProduct(productName) {
    // this.selectProductDisabled()
    if (this.curProductName.slice(-1)[0] === 'All') {
      this.curProductName = ['All'];
    } else if (this.curProductName.indexOf('All') === 0) {
      this.curProductName = this.curProductName.slice(1);
    }
    // this.curProductName = this.curProductName.includes("All") ? ["All"] : this.curProductName;
    var pn = (this.curProductName.length > 1 || !this.curProductName || this.curProductName.length == 0) ? 'All' : this.curProductName[0]
    this._summarysService.getSummaryReportConfig(pn, this.curProtocol).then(res => {
      this.summaryReportFields = res
      this.summaryReportConfig = {
        currentproduct: this.curProductName,
        entries: this.summaryReportFields.map(field => {
          return {
            key: field.name,
            label: field.labelName,
            enabled: field.include
          }
        })
      };
      this.isSelectedAllFields = true;
    })
  }

  changeProtocolForProduct(obj) {
    this.curProductName = ["All"]
    this.summaryReportProductNames = [{ "name": "All" }].concat(this.allProductNames.filter(item => item.protocol == obj.value).sort((a, b) => {
      if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
      return 1
    }));
    var pn = (this.curProductName.length > 1 || !this.curProductName || this.curProductName.length == 0) ? 'All' : this.curProductName[0]
    this._summarysService.getSummaryReportConfig(pn, this.curProtocol).then(res => {
      this.summaryReportFields = res
      this.summaryReportConfig = {
        currentproduct: this.curProductName,
        entries: this.summaryReportFields.map(field => {
          return {
            key: field.name,
            label: field.labelName,
            enabled: field.include
          }
        })
      };
      this.isSelectedAllFields = true;
    })
  }

  selectProductDisabled() {
    this.summaryReportProductNames = this.summaryReportProductNames.map((obj, index) => {
      obj.disabled = this.curProductName.includes("All") && index != 0
      return obj
    })
  }

  changeDuration(e) {
    if (e.key == 2) {
      this.basicDPdata = this.setDefaultDate(this.basicDPdata)
    }
  }

  setDefaultDate(basicDPdata) {
    if (!basicDPdata) {
      return { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() }
    }
    return basicDPdata
  }

  isNumber(obj) {
    if(obj){
      return this.numberRegex.test(obj.year) && this.numberRegex.test(obj.month) && this.numberRegex.test(obj.day)
    }
    return false
  }

  generate() {
    if (!this.isNumber(this.basicDPdata) && this.curDuration.key == 2) {
      this.toastr.showWarningMessage('Please select the correct date format.', '')
    } else {
      this.summaryReportConfig.entries = this.summaryReportFields.map(field => {
        return {
          key: field.name,
          label: field.labelName,
          enabled: field.include
        }
      })
      let params = this.summaryReportConfig;
      params.protocol = this.curProtocol
      let url = `nbi/addOn/reportExport/summaryReport/export?format=${this.selectedFormat}`
      if (this.curDuration.value.indexOf(12) > -1) {
        params.startFrom = new Date(new Date(new Date().getTime() - 43200000)).toJSON()
      } else if (this.curDuration.value.indexOf(24) > -1) {
        params.startFrom = new Date(new Date(new Date().getTime() - 86400000)).toJSON()
      } else {
        let startFrom = `${this.basicDPdata.year}-${this.basicDPdata.month}-${this.basicDPdata.day}`
        params.startFrom = new Date(new Date(new Date(startFrom)).toJSON())
      }
      this.loading = true
      this._fileDownloadService.createRequest(url, params, "blob", "SummaryReport", this.selectedFormat).then(res => {
        this.toastr.showSuccessMessage('Generate success.', '');
      }).finally(() => {
        this.loading = false
        this.menuClose['_open'] = false
      })
    }
  }
}
