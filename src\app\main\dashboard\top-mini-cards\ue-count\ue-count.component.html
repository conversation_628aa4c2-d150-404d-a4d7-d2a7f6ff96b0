<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title"></h4>
  <div class="card-body">
    <main class="w-100 h-100 d-flex justify-content-between align-items-center">
      <div>
        <h2 class="font-weight-bolder mb-0">{{UEcount}}</h2>
        <p class="card-text">{{ title }}</p>
      </div>
      <div
        class="avatar bg-light-success p-50 m-0"
        (click)="modalOpenBD(modalBD)">
        <div class="avatar-content">
          <i
            data-feather="smartphone"
            class="font-medium-5"></i>
        </div>
      </div>
    </main>
  </div>
</core-card>
<!-- Modal -->
<ng-template
  #modalBD
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel4">
      {{ 'PRODUCTS.UEFORPRODUCT' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <div aria-hidden="true">&times;</div>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <div
      class="w-100 h-100"
      #ueCountRef>
      <app-pie-echarts
        [chartRef]="ueCountRef"
        [chartData]="chartData"
        [labelShow]="true"
        [title]="'UE Count'">
      </app-pie-echarts>
    </div>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Cross click')">
      {{ 'COMMON.CLOSE' | translate }}
    </button>
  </div>
</ng-template>
<!-- / Modal -->
