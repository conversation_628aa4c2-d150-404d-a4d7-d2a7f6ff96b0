import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class FindDeviceService {
  constructor(private _http: HttpClient) { }
  private endpoint = 'nbi/device/generalData/deviceList';
  findDeviceBySn(serial: string) {
    const queryParams = {
      params: {
        filter: JSON.stringify({
          'deviceIdStruct.serialNumber': serial,
        })
      }
    };
    return this._http.get(this.endpoint, queryParams)
  }

  findDeviceByMac(mac: string) {
    const queryParams = {
      params: {
        filter: JSON.stringify({
          'mac': mac,
        })
      }
    };
    return this._http.get(this.endpoint, queryParams)
  }

  findDeviceByIp(ip: string) {
    const queryParams = {
      params: {
        filter: JSON.stringify({
          'lastIp': ip,
        })
      }
    };
    return this._http.get(this.endpoint, queryParams)
  }

  findDeviceByProduct(product: string) {
    const queryParams = {
      params: {
        filter: JSON.stringify({
          'productName': product,
        })
      }
    };
    return this._http.get(this.endpoint, queryParams)
  }

  findDeviceByLabel(label: string) {
    const queryParams = {
      params: {
        filter: JSON.stringify({
          'label': label,
        })
      }
    };
    return this._http.get(this.endpoint, queryParams)
  }

  findDeviceByFirmware(firmWare: string) {
    const queryParams = {
      params: {
        filter: JSON.stringify({
          'firmwareVersion': firmWare,
        })
      }
    };
    return this._http.get(this.endpoint, queryParams)
  }
}
