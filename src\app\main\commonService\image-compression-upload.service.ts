import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ImageCompressionUploadService {
  private maxImgSize = 100 * 1024;
  private maxLength = 500;
  constructor() { }

  fileToDataURL = (file: Blob): Promise<any> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.onloadend = (e) => resolve((e.target as FileReader).result)
      reader.readAsDataURL(file)
    })
  }

  dataURLToImage = (dataURL: string): Promise<HTMLImageElement> => {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.src = dataURL
    })
  }

  canvasToBlob = (canvas: HTMLCanvasElement, type: string, quality: number): Promise<Blob | null> => {
    return new Promise((resolve) => canvas.toBlob((blob) => resolve(blob), type, quality))
  }

  formatSize(width, height) {
    if (width > this.maxLength || height > this.maxLength) {
      if (width === height) {
        width = height = this.maxLength;
      } else if (width > height) {
        height = this.maxLength * height / width;
        width = this.maxLength;
      } else {
        width = this.maxLength * width / height;
        height = this.maxLength;
      }
    }
    return {width, height}
  }
  /**
   * 图片压缩方法
   * @param {Object}  file 图片文件
   * @param {String} type 想压缩成的文件类型
   * @param {Nubmber} quality 压缩质量参数
   * @returns 压缩后的新图片
   */
  compressionFile = async (file, quality = 0.92, type = 'image/jpeg') => {
    const fileName = file.name
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d') as CanvasRenderingContext2D
    const base64 = await this.fileToDataURL(file)
    const img = await this.dataURLToImage(base64)
    const formatSize = this.formatSize(img.width, img.height);
    canvas.width = formatSize.width
    canvas.height = formatSize.height
    context.clearRect(0, 0, formatSize.width, formatSize.height)
    context.drawImage(img, 0, 0, formatSize.width, formatSize.height)
    const blob = (await this.canvasToBlob(canvas, type, quality)) as Blob // quality:0.5可根据实际情况计算
    // const newFile = await new File([blob], fileName, {
    //   type: type
    // })
    return blob
  }
}
