import { Component, Input, OnInit } from '@angular/core';
import { DashboardService } from '../../dashboard.service';
import { Router } from '@angular/router';
import { timer, Subscription } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-device-numbers',
  templateUrl: './device-numbers.component.html',
  styleUrls: ['./device-numbers.component.scss']
})
export class DeviceNumbersComponent implements OnInit {
  @Input() accessor: any;
  public totalDevices: number;
  public loading: boolean = true;
  public iconloading: boolean = false;

  constructor(
    private _dashboardService: DashboardService,
    private _router: Router,
  ) {
    this.getTotalDevices();
  }
  /**
   * Core card event
   * @param event 
   */
  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getTotalDevices();
        break;
    }
  };

  viewDeviceList() {
    this.iconloading = true
    this._router.navigate(['/devices']);
  }

  /**
   * Get total device numbers
   */
  getTotalDevices(): void {
    this.loading = true;
    this._dashboardService.getTotalDevices()
      .then(res => this.totalDevices = res['online'])
      .catch(err => console.error(err))
      .finally(() => this.loading = false)
  }

  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getTotalDevices();
    });
  }

  ngOnDestroy(): void {
  }


}
