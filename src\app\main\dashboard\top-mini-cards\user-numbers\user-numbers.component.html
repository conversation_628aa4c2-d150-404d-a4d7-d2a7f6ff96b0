<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="loading"
  (events)="emittedEvents($event);">
  <h4 class="card-title"></h4>
  <div class="card-body">
    <main class="w-100 h-100 d-flex justify-content-between align-items-center">
      <div>
        <h2 class="font-weight-bolder mb-0">{{totalUsers}}</h2>
        <p class="card-text">{{ 'USERS.ONLINEUSERS' | translate }}</p>
      </div>
      <div
        class="avatar bg-light-info p-50 m-0"
        (click)="viewUserList()">
        <div
          class="avatar-content"
          *ngIf="!iconloading">
          <i
            data-feather="users"
            class="font-medium-5"></i>
        </div>
        <div
          *ngIf="iconloading"
          class="spinner-border text-info"
          role="status">
        </div>
      </div>
    </main>
  </div>
</core-card>
