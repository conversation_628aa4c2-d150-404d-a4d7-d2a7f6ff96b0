import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, ViewChild, OnInit, Input, AfterViewInit, ElementRef, ChangeDetectorRef, ViewChildren, QueryList, OnDestroy, NgZone } from '@angular/core';
import { StorageService } from 'app/main/commonService/storage.service';
import { Subscription, BehaviorSubject, timer } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { GoogleMap, MapInfoWindow, MapMarker } from '@angular/google-maps';
import { UserService } from 'app/auth/service/user.service';
import * as L from 'leaflet';
import 'leaflet.markercluster';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { Router, ActivatedRoute } from '@angular/router';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { GroupDataService } from 'app/main/groups/group-data.service';
import { GoogleMapService } from 'app/main/commonService/google-map.service';
import { MarkerClusterer } from '@googlemaps/markerclusterer';
import { DashboardService } from '../dashboard.service';
import { GroupsService } from 'app/main/groups/groups.service';
export interface IqueryParam {
  filter: {},
  page: number;
  size: number,
  sort: string
}

@UntilDestroy()
@Component({
  selector: 'app-groups-location',
  templateUrl: './groups-location.component.html',
  styleUrls: ['./groups-location.component.scss']
})
export class GroupsLocationComponent extends Unsubscribe implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('group_osmmap', { static: true }) osmmap: ElementRef;
  @ViewChild('googlemap', { static: false }) googlemap!: GoogleMap;
  @ViewChildren(MapInfoWindow) infoWindows!: QueryList<MapInfoWindow>;
  @ViewChildren(MapMarker) markerElem!: QueryList<MapMarker>;
  @Input() accessor: any

  private fullscreenChangeListener: () => void;
  constructor(
    private _storageService: StorageService,
    private translateService: TranslateService,
    private _userService: UserService,
    private cdr: ChangeDetectorRef,
    private _groupListService: GroupListService,
    private router: Router,
    private ngZone: NgZone,
    private _groupDataService: GroupDataService,
    private googleMapsService: GoogleMapService,
    private _dashboardService: DashboardService,
    private _groupsService: GroupsService,

  ) {
    super();
    this.mapType = this._storageService.get('locationType') ? this._storageService.get('locationType').toString() : this._userService.getInformation().pipe(untilDestroyed(this)).subscribe((response: any) => { return this._storageService.get('locationType') })
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CONFIRMSAVELOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMSAVELOCA');
      this.DOSAVELOCA = this.translateService.instant('DEVICES.ACTION.DOSAVELOCA');
      this.CONFIRMRESETLOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMRESETLOCA');
      this.DORESETLOCA = this.translateService.instant('DEVICES.ACTION.DORESETLOCA');
      this.SAVESUCC = this.translateService.instant('DEVICES.ACTION.SAVESUCC');
      this.SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
      this.SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
      this.OSMERROR = this.translateService.instant('DEVICES.OSMERROR');
      this.GOOGLEMAPERROR = this.translateService.instant('DEVICES.GOOGLEMAPERROR');
    })
  }

  public mapOptions = {
    mapTypeControl: false,
    scaleControl: false,
    streetViewControl: false,
    rotateControl: false,
    fullscreenControl: true,
    icon: null,
    scrollwheel: false,
    zoomControl: true,
    gestureHandling: "auto",
    mapTypeId: 'roadmap',
  }
  public personalTheme: any
  public mapType: any
  public groupItems = []
  public mapCenter: google.maps.LatLngLiteral = { lat: 27.835577, lng: 20.752790 }
  public mapZoom = 2
  public markerZoom = 18;
  public streetZoom = 12;
  public lat: any;
  public lng: any;
  public LocationCenter: google.maps.LatLngLiteral;
  public worldBound: google.maps.LatLngBoundsLiteral;
  currentInfoWindow: MapInfoWindow | null = null
  public updateBtnDisabled = true
  public blockUIStatus = false
  public cloneRes: any
  public mapDraggable = false
  public groupData: any;
  public CONFIRMSAVELOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMSAVELOCA');
  public DOSAVELOCA = this.translateService.instant('DEVICES.ACTION.DOSAVELOCA');
  public CONFIRMRESETLOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMRESETLOCA');
  public DORESETLOCA = this.translateService.instant('DEVICES.ACTION.DORESETLOCA');
  public SAVESUCC = this.translateService.instant('DEVICES.ACTION.SAVESUCC');
  public SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
  public OSMERROR = this.translateService.instant('DEVICES.OSMERROR');
  public GOOGLEMAPERROR = this.translateService.instant('DEVICES.GOOGLEMAPERROR');
  public ismapErr: boolean = false


  public apiLoaded = new BehaviorSubject<boolean>(false);
  ngOnInit() {
    this.fullscreenChangeListener = () => {
      if (document.fullscreenElement) {
        this.googlemap.googleMap.setOptions({ scrollwheel: true });
      } else {
        this.googlemap.googleMap.setOptions({ scrollwheel: false });
      }
    };

    if (this.mapType === '0') {
      this.googleMapsService.loadGoogleMapsApi()
        .then(() => {
          Promise.resolve().then(() => {
            this.apiLoaded.next(true);
            this.getallGroupAddress();
          });
          this.ismapErr = false
          this.cdr.detectChanges();
        })
        .catch(error => {
          Promise.resolve().then(() => {
            this.apiLoaded.next(false);
          });
          console.error('Google Maps API 加載失敗:', error);
          this.ismapErr = true
          this.cdr.detectChanges();
        });
    }
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getallGroupAddress();
    });
  }


  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getallGroupAddress()
        break;
    }
  };

  onZoomChange(): void {
    // 獲取地圖縮放級別
    if (this.googlemap && this.googlemap.googleMap) {
      const currentZoom = this.googlemap.googleMap.getZoom();
      this.mapZoom = currentZoom ?? 2;
      // console.log('Zoom level changed to:', this.mapZoom);
    }
  }

  getallGroupAddress() {
    this.groupItems = []
    this.blockUIStatus = true
    this.groupItems = []
    this._dashboardService.getTotalGroups().pipe(untilDestroyed(this)).subscribe({
      next: res => {
        let param = {
          page: '',
          size: res,
        }
        let params = {
          params: param
        };
        this._groupListService.getDeviceGroupsList(params).pipe(untilDestroyed(this)).subscribe({
          next: (res: any) => {
            // console.log(res) 
            if (res && res.data) {
              res.data.forEach(item => {
                if (!item.protocol) {
                  item.protocol = ''
                }
                if (!item.hasOwnProperty("alarmCount")) {
                  item.alarmCount = 'N/A';
                }
                if (!item.address) {
                  // console.log(item)
                  item.address = {
                    country: '',
                    state: '',
                    city: '',
                    lat: 0,
                    lng: 0,
                    zipCode: '',
                  }
                } else if (!item.address.lat || !item.address.lng) {
                  item.address.lat = 0;
                  item.address.lng = 0
                }
                this.groupItems.push(item)
              });
              // console.log(this.groupItems)
            }

            if (this.mapType === '0') {
              this.initGoogleMap(this.groupItems)
            } else {
              if (!this.osmMap) {
                this.osmInitMap(this.groupItems)
              } else {
                this.osmMap.setView([27.835577, 20.752790], 2);
              }
            }
          },
          error: error => {
            console.log(error);
            this.blockUIStatus = false
          },
          complete: () => {
            this.blockUIStatus = false
          }
        })
      },
      error: error => {
        console.log(error);
        this.blockUIStatus = false
      },
      complete: () => {
        this.blockUIStatus = false
      }
    })
  }

  public groupId: any
  public groupAlarm
  public locations = []
  public alarmIconObj;
  public timer: any;

  markers: any[] = [];
  markerCluster: MarkerClusterer | undefined;
  private projectionReady = false;

  initGoogleMap(locations) {
    const OverlappingMarkerSpiderfier = require('overlapping-marker-spiderfier');
    // 確認 Google Map 是否初始化
    if (!this.googlemap || !this.googlemap.googleMap) {
      console.error('Google Map is not initialized.');
      return;
    }

    const mapInstance = this.googlemap.googleMap;

    // 驗證 mapInstance 類型
    if (!(mapInstance instanceof google.maps.Map)) {
      console.error('Invalid Google Map instance.');
      return;
    }

    // 確保地圖的投影已就緒
    const ensureProjectionReady = (): Promise<void> => {
      return new Promise<void>((resolve) => {
        if (this.projectionReady) {
          resolve();
        } else {
          google.maps.event.addListenerOnce(mapInstance, 'idle', () => {
            this.projectionReady = true;
            resolve();
          });
        }
      });
    };

    // 監聽全螢幕變化事件
    document.addEventListener("fullscreenchange", this.fullscreenChangeListener);

    // 初始化標記和 OverlappingMarkerSpiderfier
    ensureProjectionReady()
      .then(() => {
        this.setupOMSAndMarkers(mapInstance, locations, OverlappingMarkerSpiderfier);
      })
      .catch((error) => {
        console.error('Error ensuring projection readiness:', error);
      });
  }

  private setupOMSAndMarkers(
    mapInstance: google.maps.Map,
    mergedData: any[],
    OverlappingMarkerSpiderfier: any
  ) {
    const markerObjects: google.maps.Marker[] = [];
    const bounds = new google.maps.LatLngBounds();

    // 初始化OMS
    const oms = new OverlappingMarkerSpiderfier(mapInstance, {
      markersWontMove: true,
      markersWontHide: true,
      keepSpiderfied: true,
      circleSpiralSwitchover: 100,
      nearbyDistance: 80,
    });

    oms.addListener('click', (marker, event) => {
      const group = this.markers.find(m => m.markerObject === marker)?.groupData;
      if (group) {
        this.openInfoWindow(marker, group);
      }
    });

    this.markers = [];

    mergedData.forEach((group) => {
      if (location) {
        const marker = new google.maps.Marker({
          position: {
            lat: group.address && group.address.lat ? parseFloat(group.address.lat) : 0,
            lng: group.address && group.address.lng ? parseFloat(group.address.lng) : 0,
          },
          title: group.name,
          icon: {
            url: group.hasOwnProperty('alarmList') && group.alarmList.Critical ? 'assets/images/group_criticalAlarm.gif' : group.hasOwnProperty('alarmList') && !group.alarmList.Critical && group.alarmList.Major ? 'assets/images/group_majorAlarm.png' : 'assets/images/group_noAlarm.png',
          },
        });

        oms.addMarker(marker);

        markerObjects.push(marker);
        this.markers.push({
          markerObject: marker,
          groupData: group,
        });
        bounds.extend(new google.maps.LatLng(group.address && group.address.lat ? parseFloat(group.address.lat) : 0, group.address && group.address.lng ? parseFloat(group.address.lng) : 0));
      }
    });

    if (this.markers.length > 0) {
      mapInstance.fitBounds(bounds);
      const minZoom = 2;
      if (mapInstance.getZoom() < minZoom) {
        mapInstance.setZoom(2);
      }
    } else {
      mapInstance.setCenter({ lat: 0, lng: 0 });
      mapInstance.setZoom(2); // Default zoom level if no markers
    }

    this.reloadMarkers(markerObjects);
  }

  private reloadMarkers(markerObjects: google.maps.Marker[]): void {
    if (this.markerCluster) {
      this.markerCluster.clearMarkers();
    }

    // Define the cluster renderer
    const customRenderer = {
      render: (cluster: any, stats: any, map: google.maps.Map) => {
        // Determine if the cluster contains critical alarms
        const hasCriticalAlarm = cluster.markers.some((marker: google.maps.Marker) => {
          const group = this.markers.find((m) => m.markerObject === marker)?.groupData;
          return group?.alarmList?.Critical;
        });

        // Create a marker for the cluster
        return new google.maps.Marker({
          position: cluster.position,
          icon: {
            url: hasCriticalAlarm
              ? 'assets/images/cluster_red.png'
              : 'assets/images/cluster_blue.png',
            scaledSize: new google.maps.Size(40, 40),
          },
          label: {
            text: String(cluster.markers.length),
            color: '#ffffff',
            fontSize: '12px',
          },
          map: map,
        });
      },
    };

    // Initialize or update MarkerClusterer
    this.markerCluster = new MarkerClusterer({
      map: this.googlemap.googleMap,
      markers: markerObjects,
      renderer: customRenderer,
      onClusterClick: (event, cluster) => {
        const map = this.googlemap.googleMap;
        const clusterPosition = cluster.position;
        const currentZoom = map.getZoom() || 0;
        const maxZoom = 21;

        if (clusterPosition) {
          map.setCenter(clusterPosition)
        }

        if (currentZoom < 10 && cluster.bounds) {
          map.fitBounds(cluster.bounds);
        } else if (currentZoom < maxZoom) {
          map.setZoom(currentZoom + 2);
        } else {
          console.log("Reached max zoom level.");
        }
      }
    });
  }


  private infowindow: google.maps.InfoWindow | null = null;
  openInfoWindow(marker: google.maps.Marker, group: any): void {
    // console.log(marker)
    // console.log(group)
    if (!this.infowindow) {
      this.infowindow = new google.maps.InfoWindow();
    } else {
      // 如果已存在，就先關閉，避免殘留
      this.infowindow.close();
    }

    const productContent = group.productName.map(product => `<span class="badge badge-light-info mr-50">${product}</span>`).join(' ');
    const tagsContent = group.tags?.length
      ? group.tags.map(tag => `<span class="badge badge-light-success mr-50">${tag}</span>`).join(' ')
      : '-';
    const alarmContent = (group.alarmCount && group.alarmList)
      ? Object.entries(group.alarmList)
        .map(([key, value]) => {
          let badgeClass = '';
          switch (key) {
            case 'Critical':
              badgeClass = 'badge badge-light-danger';
              break;
            case 'Major':
              badgeClass = 'badge badge-light-warning';
              break;
            case 'Minor':
            case 'Warning':
              badgeClass = 'badge badge-light-secondary';
              break;
          }
          return `<span class="${badgeClass} mr-50">${key}: ${value}</span>`;
        })
        .join(' ')
      : `<span>-</span>`;
    const location =
      [
        group.address.country,
        group.address.city,
        group.address.zipCode
      ]
        .filter(Boolean)
        .join(' ')
        .trim()
      || '-';



    const infoWindowContent = `
      <div style="color:black">
        <span>Name : <span class="cursor-pointer text-primary" id="groupName">${group.name || '-'}</span></span><br>
        <span>Devices : <span>${group.number || 0}</span></span><br>
        <span>Online Rate : <span>${group.number ? Math.floor((group.onlineCount / group.number) * 100) : 0}% (${group.onlineCount}/${group.number})</span></span><br>
        <span>Service Availability : 
        ${group.inService ? `<span>${group.onlineCount ? Math.floor((group.inService / group.onlineCount) * 100) : 0}% (${group.inService}/${group.onlineCount})</span>` : '-'}</span><br>
        <span>Product : <span>${group.productName.length != 0 ? productContent : '-'}</span></span><br>
        <span>Location : <span>${location}</span></span><br>
        <span>Tags : <span>${tagsContent}</span></span><br>
        <span>Alarm : <span class="cursor-pointer" id="groupAlarm">${alarmContent}</span></span><br>
      </div>
    `;

    // console.log(group)
    this._groupsService.clearRowData();
    this._groupsService.setRowData(group);

    this.infowindow.setContent(infoWindowContent);
    this.infowindow.open(this.googlemap.googleMap, marker);

    // 使用 google.maps.event.addListener 來處理 InfoWindow 內部的 click 事件
    google.maps.event.addListener(this.infowindow, 'domready', () => {
      const groupNameElement = document.getElementById('groupName');
      if (groupNameElement) {
        groupNameElement.addEventListener('click', () => {
          this.navigateToGroupInfo(group.id);
        });
      }
      const groupAlarmElement = document.getElementById('groupAlarm');
      if (groupAlarmElement) {
        groupAlarmElement.addEventListener('click', () => {
          this.navigateToGroupAlarm(group.id);
        });
      }
    });
  }

  ngAfterViewInit() {
    if (this.mapType == '1') {
      this.getallGroupAddress()
      this.cdr.detectChanges();
    }
    this._groupDataService.getGroupId().pipe(untilDestroyed(this)).subscribe((groupId) => {
      this.showSelectedGroup(groupId)
    });
  }

  showSelectedGroup(groupId: string) {
    if (this.mapType === '0') {
      const found = this.markers.find(item => item.groupData.id === groupId);
      if (!found) {
        console.warn('No marker found for groupId:', groupId);
        return;
      }
      const { markerObject, groupData } = found;
      const position = markerObject.getPosition();
      if (position) {
        this.googlemap.googleMap.setCenter(position);
        this.googlemap.googleMap.setZoom(18);
      }
      this.openInfoWindow(markerObject, groupData);
    }
    else {
      const osmSelectedMarker = this.osmMarkers.find(
        (marker: L.Marker) => (marker.options as any).id === groupId
      );
      if (!osmSelectedMarker) {
        console.warn(`No marker found for groupId: ${groupId}`);
        return;
      }
      this.markerClusterGroup.zoomToShowLayer(osmSelectedMarker, () => {
        this.osmMap.setView(osmSelectedMarker.getLatLng(), 18);
        osmSelectedMarker.openPopup();
      });
    }

    // this.cdr.detectChanges();
  }

  // OSM
  osmMap: any
  osmMarkers: any
  markerClusterGroup: any
  osmInitMap(osmLocation): void {
    // console.log(osmLocation)
    this.ismapErr = false
    this.osmMarkers = [];
    this.osmMap = L.map('group_osmmap', {
      //center 為範圍歐亞非中心點
      center: [27.835577, 20.752790],
      zoom: 1,
      zoomControl: false,
      scrollWheelZoom: false,
    });

    const tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 18,
      minZoom: 1,
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).on('tileerror', (error) => {
      this.ismapErr = true
    }).addTo(this.osmMap);

    L.control.zoom({
      position: 'bottomright',
    }).addTo(this.osmMap);

    tiles.addTo(this.osmMap);

    this.markerClusterGroup = L.markerClusterGroup({
      spiderfyOnMaxZoom: true,
      showCoverageOnHover: false,
      zoomToBoundsOnClick: true,
      spiderLegPolylineOptions: { weight: 1.5, color: '#222', opacity: 0.5 },

      iconCreateFunction: function (cluster) {
        const markers = cluster.getAllChildMarkers();
        let hasCriticalAlarm = false;

        markers.forEach(marker => {
          const group = marker.options.groupData; // 獲取標記的設備數據
          if (group?.alarmList?.Critical) {
            hasCriticalAlarm = true;
          }
        });

        // 根據 Critical Alarm 狀態決定群集圖示顏色
        const clusterIconUrl = hasCriticalAlarm
          ? 'assets/images/cluster_red.png'
          : 'assets/images/cluster_blue.png';

        return L.divIcon({
          html: `<div style="background-image: url('${clusterIconUrl}'); width: 40px; height: 40px; display: flex; justify-content: center; align-items: center; font-size: 14px; color: #fff;">${cluster.getChildCount()}</div>`,
          className: 'custom-cluster-icon',
          iconSize: [40, 40],
        });
      },
    });


    const bounds = L.latLngBounds();
    osmLocation.forEach((group) => {
      const customIcon = L.icon({
        iconUrl: group.hasOwnProperty('alarmList') && group.alarmList.Critical ? 'assets/images/group_criticalAlarm.gif' : group.hasOwnProperty('alarmList') && !group.alarmList.Critical && group.alarmList.Major ? 'assets/images/group_majorAlarm.png' : 'assets/images/group_noAlarm.png',
        iconAnchor: [18, 0],
      });

      const marker = L.marker(
        [parseFloat(group.address.lat), parseFloat(group.address.lng)],
        {
          icon: customIcon,
          id: group.id,
          groupData: group
        }
      );

      const productContent = group.productName.map(product => `<span class="badge badge-light-info mr-50">${product}</span>`).join(' ');
      const tagsContent = group.tags?.length
        ? group.tags.map(tag => `<span class="badge badge-light-success mr-50">${tag}</span>`).join(' ')
        : '-';
      const alarmContent = (group.alarmCount && group.alarmList)
        ? Object.entries(group.alarmList)
          .map(([key, value]) => {
            let badgeClass = '';
            switch (key) {
              case 'Critical':
                badgeClass = 'badge badge-light-danger';
                break;
              case 'Major':
                badgeClass = 'badge badge-light-warning';
                break;
              case 'Minor':
              case 'Warning':
                badgeClass = 'badge badge-light-secondary';
                break;
            }
            return `<span class="${badgeClass} mr-50">${key}: ${value}</span>`;
          })
          .join(' ')
        : `<span>-</span>`;
      const location =
        [
          group.address.country,
          group.address.city,
          group.address.zipCode
        ]
          .filter(Boolean)
          .join(' ')
          .trim()
        || '-';
      const infoWindowContent = `
          <div style="color:black">
            <span>Name : <span class="cursor-pointer text-primary" id="groupName">${group.name || '-'}</span></span><br>
            <span>Devices : <span>${group.number || 0}</span></span><br>
            <span>Online Rate : <span>${group.number ? Math.floor((group.onlineCount / group.number) * 100) : 0}% (${group.onlineCount}/${group.number})</span></span><br>
            <span>Service Availability : 
            ${group.inService ? `<span>${group.onlineCount ? Math.floor((group.inService / group.onlineCount) * 100) : 0}% (${group.inService}/${group.onlineCount})</span>` : '-'}</span><br>
            <span>Product : <span>${group.productName.length != 0 ? productContent : '-'}</span></span><br>
            <span>Location : <span>${location}</span></span><br>
            <span>Tags : <span>${tagsContent}</span></span><br>
            <span>Alarm : <span class="cursor-pointer" id="groupAlarm">${alarmContent}</span></span><br>
          </div>
        `;

      this._groupsService.clearRowData();
      this._groupsService.setRowData(group);
      marker.on('popupopen', () => {
        const groupNameElement = document.getElementById('groupName');
        if (groupNameElement) {
          groupNameElement.addEventListener('click', () => {
            this.navigateToGroupInfo(group.id);
          });
        }
        const groupAlarmElement = document.getElementById('groupAlarm');
        if (groupAlarmElement) {
          groupAlarmElement.addEventListener('click', () => {
            this.navigateToGroupAlarm(group.id);
          });
        }
      });

      marker.bindPopup(infoWindowContent);
      marker.on('click', () => {
        this.osmMap.setView([parseFloat(group.address.lat), parseFloat(group.address.lng)], 18);
      });

      this.markerClusterGroup.addLayer(marker); // 將標記添加到群集
      this.osmMarkers.push(marker);
      const latLng = marker.getLatLng();
      bounds.extend(latLng);
    });

    tiles.on('load', () => {
      this.osmMap.addLayer(this.markerClusterGroup); // 等地圖瓦片載入完成後再添加群集
    });

    if (osmLocation.length > 0) {
      this.osmMap.fitBounds(bounds);
      const minZoom = 2;
      if (this.osmMap.getZoom() < minZoom) {
        this.osmMap.setZoom(minZoom);
      }
    }

    setTimeout(() => {
      this.osmMap.invalidateSize();
    }, 0);
  }

  navigateToGroupInfo(groupId) {
    this.router.navigate([`/groups/${groupId}/group-info`]);
  }
  navigateToGroupAlarm(groupId) {
    this.router.navigate([`/groups/${groupId}/group-events`])
  }

  ngOnDestroy() {
    if (this.fullscreenChangeListener) {
      document.removeEventListener("fullscreenchange", this.fullscreenChangeListener);
    }
    if (this.osmMap) {
      this.osmMap.remove();  // 这将删除地图实例
    }
    clearInterval(this.timer);
    this.timer = null;
  }

}