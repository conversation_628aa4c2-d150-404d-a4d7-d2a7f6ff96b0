import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LogParsingService {

  constructor() { }


  formatDeviceOperation(item) {
    if (!item.tag) {
      item.tag = "";
    }
    let statusText = [];
    statusText['PENDING'] = "Pending";
    statusText['IN_PROGRESS'] = "In Progress";
    statusText['COMPLETED'] = "Completed";
    statusText['ACCEPTED'] = "Accepted";
    statusText['COMPLETED_WITH_ERRORS'] = "Completed With Errors";
    statusText['FAILED'] = "Failed";
    statusText['CANCELED'] = "Canceled";
    statusText['SUCCESSFULLY'] = "Successfully";
    statusText['EXPIRED'] = "Expired";
    let stateClass = [];
    stateClass['PENDING'] = "label-default";
    stateClass['IN_PROGRESS'] = "label-info";
    stateClass['ACCEPTED'] = "label-info";
    stateClass['COMPLETED'] = "label-success";
    stateClass['COMPLETED_WITH_ERRORS'] = stateClass['CANCELED'] = stateClass['EXPIRED'] = "label-warning";
    stateClass['FAILED'] = "label-danger";
    stateClass['SUCCESSFULLY'] = "label-primary";
    // item.status = '<span class="label ' + stateClass[item.state] + '">' + statusText[item.state] + '</span>';
    item.entriesJson = JSON.stringify(item.entries);
    item.details = "<ul>";
    item.entries.forEach(entry => {
      // console.log(entry)
      item.details += this.formatCwmpMessage(entry.message, entry.status);

      if (entry.fault) {
        let faultStruct = entry.fault.faultDetails.faultStruct;
        item.details += `<b>Fault Code: </b>${faultStruct.faultCode}`;
        item.details += `<br/><b>Fault String: </b>${faultStruct.faultString}`;
      }
    });
    item.details += "</ul>";
    item.operation = item.entries[0].message.messageType
    return item;
  };

  formatCwmpMessage(cwmpMessage, status?) {
    let res = "",
      commandKey = cwmpMessage.commandKey ? cwmpMessage.commandKey : "empty";

    if (status) {
      res += `<li><b>${cwmpMessage.messageType}</b> <span class="${this.stateColorShow(status)} ml-50"> ${status} </span>`;
    } else {
      res += `<li><b>${cwmpMessage.messageType}</b>`;
    }
    res += "<ul>";
    switch (cwmpMessage.messageType) {
      case 'AddObject':
      case 'DeleteObject':
        res += this.wrapWithLi(cwmpMessage, "objectName");
        break;
      case 'GetParameterValues':
      case 'GetParameterAttributes':
        if (cwmpMessage.parameterNames && Object.prototype.toString.call(cwmpMessage.parameterNames) === "[object String]") {
          res += this.wrapWithLi(cwmpMessage.parameterNames);
        } else if (cwmpMessage.parameterNames && Object.prototype.toString.call(cwmpMessage.parameterNames) === "[object Array]") {
          cwmpMessage.parameterNames.forEach(param => {
            res += this.wrapWithLi(param);
          });
        }
        break;
      case 'GetParameterNamesResponse':
        if (cwmpMessage.parameterList) {
          cwmpMessage.parameterList.forEach(param => {
            res += this.wrapWithLi(param.parameterName + " Writable: " + param.writable);
          });
        }
        break;
      case 'SetParameterValues':
      case 'GetParameterValuesResponse':
        if (cwmpMessage.parameterList) {
          cwmpMessage.parameterList.forEach(param => {
            res += this.wrapWithLi(param.name + " = " + param.value);
          });
        }
        break;
      case 'SetParameterAttributes':
        if (cwmpMessage.parameterList) {
          cwmpMessage.parameterList.forEach(param => {
            res += "<li>" + param.name + " - ";
            if (param.notificationChange) {
              res += "Notification: " + param.notification + " ";
            }
            if (param.accessListChange && param.accessList) {
              res += "Access List: " + param.accessList.join(", ");
            }
            res += "</li>";
          });
        }
        break;
      case 'GetParameterAttributesResponse':
        if (cwmpMessage.parameterList) {
          cwmpMessage.parameterList.forEach(param => {
            res += "<li>" + param.name + " - ";
            if (param.notification != null) {
              res += "Notification: " + param.notification + " ";
            }
            if (param.accessList) {
              res += "Access List: " + param.accessList.join(", ");
            }
            res += "</li>";
          });
        }
        break;
      case 'GetParameterNames':
        res += this.wrapWithLi(cwmpMessage.parameterPath + " NextLevel: " + cwmpMessage.nextLevel);
        break;
      case 'ScheduleInform':
        res += this.wrapWithLi(cwmpMessage, "delaySeconds", "Delay (sec)") + this.wrapWithLi("CommandKey : " + commandKey);
        break;
      case 'Reboot':
      case 'CancelTransfer':
        res += this.wrapWithLi("CommandKey : " + commandKey);
        break;
      case 'Download':
      case 'Upload':
      case 'ScheduleDownload':
        res += this.wrapWithLi("CommandKey : " + commandKey);
        res += this.wrapWithLi(cwmpMessage, "fileType", "File Type");
        res += this.wrapWithLi(cwmpMessage, "url", "URL");
        res += this.wrapWithLi(cwmpMessage, "targetFileName", "Target File Name");
        res += this.wrapWithLi(cwmpMessage, "delaySeconds", "Delay (sec)");
        res += this.wrapWithLi(cwmpMessage, "username", "Username");
        res += this.wrapWithLi(cwmpMessage, "password", "Password");
        res += this.wrapWithLi(cwmpMessage, "fileSize", "File Size");
        res += this.wrapWithLi(cwmpMessage, "successURL", "Success URL");
        res += this.wrapWithLi(cwmpMessage, "failureURL", "Failure URL");
        if (cwmpMessage.timeWindowList) {
          res += "<li>Time Windows:</li>";
          res += '<table class="table table-striped table-bordered table-hover"><thead><th>Window Start</th><th>Window End</th><th>Window Mode</th><th>Max Retries</th><th>User Message</th></thead><tbody>';
          cwmpMessage.timeWindowList.forEach(tw => {
            let tds = "";
            let fields = ["windowStart", "windowEnd", "windowMode", "maxRetries", "userMessage"];
            for (let idx in fields) {
              tds += this.wrapWithTag(tw[fields[idx]], "td");
            }
            res += this.wrapWithTag(tds, "tr");
          });
          res += '</tbody></table>';
        }
        break;
      case 'ChangeDUState':
        res += this.wrapWithLi("CommandKey : " + commandKey);
        if (cwmpMessage.operations) {
          res += "<li>Operations:</li>";
          cwmpMessage.operations.forEach(op => {
            res += "<ul>";
            res += this.wrapWithLi(op, "operationType", "Operation Type");
            res += this.wrapWithLi(op, "uuid", "UUID");
            res += this.wrapWithLi(op, "url", "URL");
            res += this.wrapWithLi(op, "username", "Username");
            res += this.wrapWithLi(op, "password", "Password");
            res += this.wrapWithLi(op, "executionEnvRef", "ExecutionEnvRef");
            res += this.wrapWithLi(op, "version", "version");
            res += "</ul><br/>";
          });
        }
        break;
      case 'GetOptions':
        if (cwmpMessage.optionName) {
          cwmpMessage.optionName.forEach(opt => {
            if (opt) {
              res += this.wrapWithLi(opt);
            }
          });
        }
        break;
      case 'SetVouchers':
        if (cwmpMessage.voucherList) {
          cwmpMessage.voucherList.forEach(voucher => {
            res += this.wrapWithLi(voucher);
          });
        }
        break;
      case 'CustomRPC':
        res += this.wrapWithLi(cwmpMessage, "body");
        break;
      case 'Inform':
        res += "<li> Events: <ul>";
        cwmpMessage.events.forEach(e => {
          res += this.wrapWithTag(e.eventCode, "li");
        });
        res += "</ul></li>";
        res += "<li> Parameters: <ul>";
        cwmpMessage.parameterList.forEach(pvs => {
          res += this.wrapWithTag(pvs.name + " = " + (pvs.value || pvs.value === false ? pvs.value : ''), "li");
        });
        res += "</ul></li>";
        res += this.wrapWithLi(cwmpMessage, "maxEnvelopes", "Max Envelopes");
        res += this.wrapWithLi(cwmpMessage, "retryCount", "Retry Count");
        res += this.wrapWithLi(cwmpMessage, "currentTime", "Current Time");
        break;
      case 'InformResponse':
        res += this.wrapWithLi(cwmpMessage, "maxEnvelopes", "Max Envelopes");
        break;
      case 'Fault':
        res += this.wrapWithLi(cwmpMessage, "faultCode", "Fault Code");
        res += this.wrapWithLi(cwmpMessage, "faultString", "Fault String");
        if (cwmpMessage.faultDetails) {
          res += "<li> Fault Details:";
          if (cwmpMessage.faultDetails.faultStruct) {
            res += "<ul>Fault Struct:";
            res += this.wrapWithLi(cwmpMessage.faultDetails.faultStruct, "faultCode", "Fault Code");
            res += this.wrapWithLi(cwmpMessage.faultDetails.faultStruct, "faultString", "Fault String");
            res += "</ul>";
          }
          if (cwmpMessage.faultDetails.setParameterValuesFault) {
            res += "<ul>SetParameterValuesFault:";
            res += this.wrapWithLi(cwmpMessage.faultDetails.setParameterValuesFault, "faultCode", "Fault Code");
            res += this.wrapWithLi(cwmpMessage.faultDetails.setParameterValuesFault, "faultString", "Fault String");
            res += this.wrapWithLi(cwmpMessage.faultDetails.setParameterValuesFault, "parameterName", "Parameter Name");
            res += "</ul>";
          }
          res += "</li>"
        }
        break;

      case "GetRPCMethodsResponse":
        if (cwmpMessage.methodList) {
          res += "<li> Methods: <ul>";
          cwmpMessage.methodList.forEach(method => {
            res += this.wrapWithTag(method, "li");
          });
          res += "</ul></li>";
        }
        break;
      case "AutonomousTransferComplete":
        res += this.wrapWithLi(cwmpMessage, "startTime", "Start Time");
        res += this.wrapWithLi(cwmpMessage, "completeTime", "Complete Time");
        res += this.wrapWithLi(cwmpMessage, "AnnounceURL", "Announce URL");
        res += this.wrapWithLi(cwmpMessage, "TransferURL", "Transfer URL");
        res += this.wrapWithLi(cwmpMessage, "IsDownload", "Is Download");
        res += this.wrapWithLi(cwmpMessage, "FileType", "File Type");
        res += this.wrapWithLi(cwmpMessage, "FileSize", "File Size");
        res += this.wrapWithLi(cwmpMessage, "TargetFileName", "Target File Name");
        if (cwmpMessage.faultStruct) {
          res += "<li> Fault struct: <ul>";
          res += this.wrapWithLi(cwmpMessage.faultStruct, "faultCode", "Fault Code");
          res += this.wrapWithLi(cwmpMessage.faultStruct, "faultString", "Fault String");
          res += "</ul></li>";
        }
        break;
      case "AutonomousDUStateChangeComplete":
        if (cwmpMessage.results) {
          res += "<li> Results: <ul>";
          cwmpMessage.results.forEach(r => {
            res += this.wrapWithLi(r, "operationPerformed", "Operation Performed");

            res += this.wrapWithLi(r, "uuid", "UUID");
            res += this.wrapWithLi(r, "deploymentUnitRef", "Deployment Unit Ref");
            res += this.wrapWithLi(r, "version", "Version");
            res += this.wrapWithLi(r, "currentState", "Current State");
            res += this.wrapWithLi(r, "resolved", "Resolved");
            res += this.wrapWithLi(r, "startTime", "Start Time");
            res += this.wrapWithLi(r, "completeTime", "Complete Time");

            if (cwmpMessage.executionUnitRefList) {
              res += "<li> Execution Unit Ref: <ul>";
              cwmpMessage.executionUnitRefList.forEach(ref => {
                res += this.wrapWithTag(ref, "li");
              });
              res += "</ul></li>";
            }

            if (r.fault) {
              res += "<li> Fault struct: <ul>";
              res += this.wrapWithLi(r.fault, "faultCode", "Fault Code");
              res += this.wrapWithLi(r.fault, "faultString", "Fault String");
              res += "</ul></li>";
            }

          });
          res += "</ul></li>";
        }
        break;
      case "DUStateChangeComplete":
        res += this.wrapWithLi("Command key : " + commandKey);
        if (cwmpMessage.results) {
          res += "<li> Results: <ul>";
          cwmpMessage.results.forEach(r => {
            res += this.wrapWithLi(r, "uuid", "UUID");
            res += this.wrapWithLi(r, "deploymentUnitRef", "Deployment Unit Ref");
            res += this.wrapWithLi(r, "version", "Version");
            res += this.wrapWithLi(r, "currentState", "Current State");
            res += this.wrapWithLi(r, "resolved", "Resolved");
            res += this.wrapWithLi(r, "startTime", "Start time");
            res += this.wrapWithLi(r, "completeTime", "Complete time");

            res += "<li> Execution Unit Ref: <ul>";
            r.executionUnitRefList.forEach(r => {
              res += this.wrapWithTag(r, "li");
            });
            res += "</ul></li>";

            if (r.fault) {
              res += "<li> Fault: <ul>";
              res += this.wrapWithLi(r.fault, "faultCode", "Fault Code");
              res += this.wrapWithLi(r.fault, "faultString", "Fault String");
              res += "</ul></li>";
            }
            res += "</br>";
          });
          res += "</ul></li>";
        }
        break;
      case "Kicked":
        res += this.wrapWithLi(cwmpMessage, "command", "Command");
        res += this.wrapWithLi(cwmpMessage, "referer", "Referer");
        res += this.wrapWithLi(cwmpMessage, "arg", "Arg");
        res += this.wrapWithLi(cwmpMessage, "next", "Next");
        break;
      case "RequestDownload":
        res += this.wrapWithLi(cwmpMessage, "fileType", "File type");
        if (cwmpMessage.fileTypeArg) {
          res += "<li> File Type Arguments: <ul>";
          cwmpMessage.fileTypeArg.forEach(a => {
            res += this.wrapWithLi(a, "name", "Name");
            res += this.wrapWithLi(a, "value", "Value");
          });
          res += "</ul></li>";
        }
        break;
      case "TransferComplete":
        res += this.wrapWithLi("Command Key : " + commandKey);
        res += this.wrapWithLi(cwmpMessage, "startTime", "Start Time");
        res += this.wrapWithLi(cwmpMessage, "completeTime", "Complete Time");
        if (cwmpMessage.faultStruct) {
          res += "<li> Fault Struct: <ul>";
          res += this.wrapWithLi(cwmpMessage.faultStruct, "faultCode", "Fault Code");
          res += this.wrapWithLi(cwmpMessage.faultStruct, "faultString", "Fault String");
          res += "</ul></li>";
        }
        break;
      case "AddObjectResponse":
        res += this.wrapWithLi(cwmpMessage, "instanceNumber", "Instance Number");
        res += this.wrapWithLi(cwmpMessage, "status", "Status");
        break;
      case "CustomRPCResponse":
        res += this.wrapWithLi(cwmpMessage, "name", "Name");
        res += this.wrapWithLi(cwmpMessage, "body", "Body");
        break;
      case "SetParameterValuesResponse":
      case "DeleteObjectResponse":
        res += this.wrapWithLi(cwmpMessage, "status", "Status");
        break;
      case "UploadResponse":
      case "DownloadResponse":
        res += this.wrapWithLi(cwmpMessage, "status", "Status");
        res += this.wrapWithLi(cwmpMessage, "startTime", "Start time");
        res += this.wrapWithLi(cwmpMessage, "completeTime", "Complete time");
        break;
      case "GetAllQueuedTransfersResponse":
        if (cwmpMessage.transferList) {
          res += "<li> Transfer List: <ul>";
          cwmpMessage.transferList.forEach(tr => {
            res += "<li><ul>";
            res += this.wrapWithLi(tr, "commandKey", "Command Key");
            res += this.wrapWithLi(tr, "state", "State");
            res += this.wrapWithLi(tr, "isDownload", "Is Download");
            res += this.wrapWithLi(tr, "fileType", "File Type");
            res += this.wrapWithLi(tr, "fileSize", "File Size");
            res += this.wrapWithLi(tr, "targetFileName", "Target File Name");
            res += "</ul></li></br>";
          });
          res += "</ul></li>";
        }
        break;
      case "GetOptionsResponse":
        if (cwmpMessage.optionList) {
          res += "<li> Option List: <ul>";
          cwmpMessage.optionList.forEach(op => {
            res += "<li><ul>";
            res += this.wrapWithLi(op, "name", "Name");
            res += this.wrapWithLi(op, "voucherSN", "Voucher SN");

            if (op.state != null) {
              res += "<li> Option State: <ul>";
              res += this.wrapWithLi(op.state, "enabled", "Enabled");
              res += this.wrapWithLi(op.state, "setup", "Setup");
              res += "</ul></li>";
            }

            res += this.wrapWithLi(op, "mode", "Option Mode");
            res += this.wrapWithLi(op, "startDate", "Start Date");
            res += this.wrapWithLi(op, "expirationDate", "Expiration Date");
            res += this.wrapWithLi(op, "isTransferable", "Is Transferable");
            res += "</ul></li></br>";
          });
          res += "</ul></li>";
        }
        break;
      case "GetQueuedTransfersResponse":
        if (cwmpMessage.transferList) {
          res += "<li> Transfer List: <ul>";
          cwmpMessage.transferList.forEach(tl => {
            res += this.wrapWithLi(tl, "commandKey", "Command key");
            res += this.wrapWithLi(tl, "transferState", "Transfer State");
          });
          res += "</ul></li></br>";
        }
        break;
      case "KickedResponse":
        res += this.wrapWithLi(cwmpMessage, "nextUrlState", "Next URL State");
        break;
      case "GetRPCMethods":
      case "RebootResponse":
      case "RequestDownloadResponse":
      case "ScheduleDownloadResponse":
      case "ScheduleInformResponse":
      case "SetParameterAttributesResponse":
      case "SetVouchersResponse":
      case "TransferCompleteResponse":
      case "DUStateChangeCompleteResponse":
      case "FactoryResetResponse":
      case "FactoryReset":
      case "GetAllQueuedTransfers":
      case "GetQueuedTransfers":
      case "AutonomousDUStateChangeCompleteResponse":
      case "AutonomousTransferCompleteResponse":
      case "CancelTransferResponse":
      case "ChangeDUStateResponse":
        break;
      case 'RefreshObject':
        if (cwmpMessage.path) {
          res += this.wrapWithLi(cwmpMessage.path);
        }
        break;
      default:
        res += JSON.stringify(cwmpMessage);
        break;
    }
    res += "</ul>";
    res += "</li>";

    return res;
  }

  wrapWithLi(obj, field?, label?) {
    if (field) {
      if (typeof obj[field] !== 'undefined') {
        if (field == 'status') {
          let s = "<li>";
          if (label) {
            s += label + ": ";
          }
          s += `<span class="${this.stateColorShow(obj[field])}"> ${obj[field]} </span>`;
          s += "</li>";
          return s;
        } else {
          let s = "<li>";
          if (label) {
            s += label + ": ";
          }
          s += obj[field];
          s += "</li>";
          return s;
        }
      } else {
        return "";
      }

    } else {
      if (Object.prototype.toString.call(obj) == "[object Object]") {
        return this.innerOfObjPaths(obj)
      } else {
        return "<li>" + obj + "</li>";
      }
    }
  }

  innerOfObjPaths(obj) {
    let html = ""
    for (let i in obj) {
      if (i == "path") {
        html += "<li>" + obj[i] + "</li>"
      } else if (i == "status") {
        html += "<div> ● " + i + ": " + "<span style=" + this.statusColor(obj[i]) + ">" + this.innerOfStatusText(obj[i]) + "</span>" + "</div>"
      } else {
        html += "<div> ● " + i + ": " + obj[i] + "</div>"
      }
    }
    return html
  }

  statusColor(v) {
    switch (v) {
      case 'COMPLETED':
        return 'color:#739e73;font-weight:bold;'
      case 'FAILED':
        return 'color:#a90329;font-weight:bold;'
      case 'CANCELED':
        return 'color:#c79121;font-weight:bold;'
      default:
        return ''
    }
  }

  innerOfStatusText(t) {
    switch (t) {
      case 'COMPLETED':
        return 'Completed'
      case 'FAILED':
        return 'Failed'
      case 'CANCELED':
        return 'Canceled'
      default:
        return t
    }
  }

  wrapWithTag(obj, tag) {
    return "<" + tag + ">" + obj + "</" + tag + ">";
  }

  stateColorShow(state) {
    if (state == 'COMPLETED') {
      return 'badge badge-glow badge-light-success'
    } else if (state == 'PARTIAL_FAILED') {
      return 'badge badge-glow badge-light-warning'
    }  else if (state == 'FAILED') {
      return 'badge badge-glow badge-light-danger'
    } else if (state == 'PENDING' || state == 'INPROGRESS' || state == 'CANCELED') {
      return 'badge badge-glow badge-light-secondary'
    } else {
      return 'badge badge-glow badge-light-secondary'
    }
  }

}
