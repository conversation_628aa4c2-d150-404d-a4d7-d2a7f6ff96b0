export interface Widget {
    componentId: string;
    component: any;
    name: string;
    description: string;
    class: string;
    subClass: string;
    render: boolean;
    hidden: boolean;
    cols: number;
    rows: number;
    x: number;
    y: number;
    maxItemCols?: number;
    minItemCols: number;
    minItemRows: number;
    copy?: boolean;
    copyed?: boolean;
    toggled?: boolean;
    grade?: string;
    dmsType?: string;
    protocalType?: string;
    extension?: string;
    data?: any[];
    color?: string;
    src?: any
}
