import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { DeviceActionsService } from '../device-actions.service';
import { DeviceInfoService } from 'app/main/devices/device-info/device-info.service'
import { DataStoreService } from 'app/main/commonService/data-store.service';
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';
import { AuthenticationService } from 'app/auth/service';

@UntilDestroy()
@Component({
  selector: 'app-connection-request-action',
  templateUrl: './connection-request-action.component.html',
  styleUrls: ['./connection-request-action.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ConnectionRequestActionComponent implements OnInit {
  @Output() loadingEvt = new EventEmitter<string>();
  @Input() mode: string;
  @Input() row;
  @Input() selectedDevice?: string;
  @Input() selectedDevices?;
  @Input() iconButton?: boolean = false;
  @Input() editMode: boolean;
  public loading: boolean = false
  public currentDevice: string;
  public currentDeviceSN: string;
  public selectSingleSN: string;
  public protocol: string;
  public confirmLive = this.translateService.instant('DEVICES.ACTION.CONFIRM_LIVE_UPDATE');
  public doUpdate = this.translateService.instant('DEVICES.ACTION.DO_LIVE');
  public liveSuccess = this.translateService.instant('DEVICES.ACTION.LIVESUCCESS');
  public liveFail = this.translateService.instant('DEVICES.ACTION.LIVEFAIL');
  public doSelect = this.translateService.instant('DEVICES.ACTION.DO_LIVE_UPDATE');

  public assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
  public assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
  public assignOperationFail = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL');
  public assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
  public assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
  public assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
  public assignFailMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL_MESSAGE');
  constructor(
    private _deviceActionsService: DeviceActionsService,
    private _deviceInfoService: DeviceInfoService,
    private _dataStore: DataStoreService,
    private route: ActivatedRoute,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService,
    private _authenticationService: AuthenticationService,
  ) {
  }

  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'deviceAdmin', 'read');
  }

  loadingChange(cal) {
    this.loadingEvt.emit(cal);
  }

  connectionRequest() {
    switch (this.mode) {
      case 'currentDevice':
        // this.getDevice(this.currentDevice);
        this.loading = true
        this._deviceActionsService.connectionRequest(this.currentDevice).then((res) => {
          if (String(res) == 'true') {
            if (['usp', 'netconf'].includes(this.protocol) && ['Inactive', 'Offline'].includes(this._dataStore.getDeviceInfo('status'))) {
              this._dataStore.updateDeviceInfo({ status: 'Online' });
            }
            this.loading = false
            this._toastrUtilsService.showSuccessMessage(this.liveSuccess, `S/N: ${this.currentDeviceSN}`)
          } else {
            if (['usp', 'netconf'].includes(this.protocol) && ['Active', 'Online'].includes(this._dataStore.getDeviceInfo('status'))) {
              this._dataStore.updateDeviceInfo({ status: 'Offline' });
            }
            this.loading = false
            this._toastrUtilsService.showErrorMessage(this.liveFail, `S/N: ${this.currentDeviceSN}`);
          }
        }).catch((res) => {
          this.loading = false
          this._toastrUtilsService.showErrorMessage(this.liveFail, res.error);
        });
        break;
      case 'singleDevice':
        this.loading = true
        this.loadingChange('true')
        this.selectSingleSN = this.row['deviceIdStruct'].serialNumber
        this._deviceActionsService.connectionRequest(this.selectedDevice).then((res) => {
          if (String(res) == 'true') {
            this.loading = false
            this.loadingChange('false')
            this._toastrUtilsService.showSuccessMessage(this.liveSuccess, `S/N: ${this.selectSingleSN}`)
          } else {
            this.loading = false
            this.loadingChange('false')
            this._toastrUtilsService.showErrorMessage(this.liveFail, `S/N: ${this.selectSingleSN}`);
          }
        }).catch((res) => {
          this.loading = false
          this.loadingChange('false')
          this._toastrUtilsService.showErrorMessage(this.liveFail, res.error);
        });
        break;
      case 'multiDevices':
        if (this.selectedDevices.length) {
          this.loading = true
          this.loadingChange('true')
          const observableList = this.selectedDevices.map(item => this._deviceActionsService.connectionRequest(item.id));
          forkJoin(observableList).pipe(untilDestroyed(this)).subscribe({
            next: (res: []) => {
            let trueIndexes = []
            let falseIndexes = []
            for (let i = 0; i < res.length; i++) {
              if (!res[i]) {
                falseIndexes.push(i);
              } else {
                trueIndexes.push(i);
              }
            }
            if (trueIndexes) {
              trueIndexes.forEach(item => {
                this._toastrUtilsService.showSuccessMessage(this.liveSuccess, `S/N: ${this.selectedDevices[item]['deviceIdStruct'].serialNumber}`)
              })
            }
            if (falseIndexes) {
              falseIndexes.forEach(item => {
                this._toastrUtilsService.showErrorMessage(this.liveFail, `S/N: ${this.selectedDevices[item]['deviceIdStruct'].serialNumber}`)
              })
            }
            this.loading = false
            this.loadingChange('false')
            // console.log("False indexes:", falseIndexes);
            // console.log("True indexes:", trueIndexes);
          },
          error: error => {
            this.loading = false
            this.loadingChange('false')
            this._toastrUtilsService.showErrorMessage(this.liveFail, error.error);
          }})
        } else {
          Swal.fire({
            title: this.translateService.instant('DEVICES.ACTION.PLESESELECT'),
            icon: "warning",
            showCancelButton: false,
            confirmButtonText: this.translateService.instant('COMMON.OK'),
            customClass: {
              confirmButton: 'btn btn-primary'
            }
          })
        }
        break;
    }
  }

  getDevice() {
    this.route.data.pipe(untilDestroyed(this)).subscribe(res => {
      // console.log(res)
      this.currentDeviceSN = res.basicInfo.deviceIdStruct.serialNumber;
      this.protocol = res.basicInfo.protocol;
    });
  }


  ngOnInit(): void {
    this.protocol = this.row?.protocol
    this.currentDevice = this.route.snapshot.paramMap.get('id')
    let isGroup = this.route.snapshot['_routerState'].url.endsWith("group-info")
    if (this.currentDevice && !isGroup) {
      this.getDevice()
    }
  }
}
