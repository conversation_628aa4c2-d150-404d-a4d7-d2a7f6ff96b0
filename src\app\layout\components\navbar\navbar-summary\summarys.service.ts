import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DatePipe } from '@angular/common';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SummarysService {
  // Public
  public informNavbar = false
  /**
   *
   * @param {HttpClient} _httpClient
   */
  constructor(
    private _httpClient: HttpClient,
    private datePipe: DatePipe
  ) {
  }

  /**
   * Get Notifications Data
   */
  getAvailProductNames(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/device/generalData/productList').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getSummaryReportConfig(cpn, protocol?): Promise<any[]> {
    let pl = protocol ? protocol : 'cwmp'
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/addOn/reportExport/${cpn}/summaryReportConfig?protocol=${pl}`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  generateSummaryReport(format, params) {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/addOn/reportExport/summaryReport/export?format=${format}`, params, { responseType: "blob" }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

}
