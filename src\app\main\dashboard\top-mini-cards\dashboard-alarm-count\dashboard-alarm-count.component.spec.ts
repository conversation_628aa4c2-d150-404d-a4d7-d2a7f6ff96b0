import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardAlarmCountComponent } from './dashboard-alarm-count.component';

describe('DashboardAlarmCountComponent', () => {
  let component: DashboardAlarmCountComponent;
  let fixture: ComponentFixture<DashboardAlarmCountComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DashboardAlarmCountComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardAlarmCountComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
