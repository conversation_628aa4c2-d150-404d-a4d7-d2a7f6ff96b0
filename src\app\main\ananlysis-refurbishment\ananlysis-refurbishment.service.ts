import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { BehaviorSubject, Observable, throwError, of, forkJoin, Subject, merge, interval } from 'rxjs';
import { map, catchError, tap, share, shareReplay, switchMap } from "rxjs/operators";
import { AuthenticationService } from 'app/auth/service';


@Injectable({
    providedIn: 'root'
})
export class AnalysisRefurbishmentService {
    public personalThemeObservable: Observable<any>;

    constructor(
        private _httpClient: HttpClient,
        private authService: AuthenticationService
    ) {
    }

    /**
   * Resolver
   *
   * @param {ActivatedRouteSnapshot} route
   * @param {RouterStateSnapshot} state
   * @returns {Observable<any> | Promise<any> | any}
   */
    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
        return new Promise<void>((resolve, reject) => {
            resolve();
        });
    }

    getPersonalTheme(): Observable<any[]> {
        if (this.personalThemeObservable) {
            return this.personalThemeObservable;
        } else {
            this.personalThemeObservable = this._httpClient.get(`nbi/addOn/personalTheme`).pipe(
                share(),
                catchError(error => throwError(() => error))
            );
            return this.personalThemeObservable;
        }
    }
    getRefurbishmentDeviceList(params): Promise<any[]> {
        return new Promise((resolve, reject) => {
            this._httpClient.get('nbi/analysis/refurbishStatistics/list',params).subscribe({
                next: (response: any) => {
                    resolve(response);
                },
                error: reject
            });
        });
    }

}
