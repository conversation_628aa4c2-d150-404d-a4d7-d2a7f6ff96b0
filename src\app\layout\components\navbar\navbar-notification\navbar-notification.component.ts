import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { NotificationsService } from 'app/layout/components/navbar/navbar-notification/notifications.service';

// Interface
interface notification {
  messages: [];
  systemMessages: [];
  system: Boolean;
}

@UntilDestroy()
@Component({
  selector: 'app-navbar-notification',
  templateUrl: './navbar-notification.component.html'
})
export class NavbarNotificationComponent implements OnInit {
  @Input() editMode: boolean;
  // Public
  // public notifications: notification;
  public notifications = {
    messages: []
  };
  public num = 0;

  /**
   *
   * @param {NotificationsService} _notificationsService
   */
  constructor(private _notificationsService: NotificationsService, private _router: Router) { }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------
  readMore() {
    this._router.navigate(['/events/device-alarms']);
  }
  /**
   * Get Notification message filted by Major or Critical
   */
  getNotificationMessage() {
    this._notificationsService.getNotificationsData().pipe(untilDestroyed(this)).subscribe((res: any) => {
      // console.log(res)
      this.notifications.messages = res.data.filter(m => m.PerceivedSeverity == 'Major' || m.PerceivedSeverity == 'Critical' || m.PerceivedSeverity == 'Error')
      this.num = this.notifications.messages.length
      // console.log(this.notifications)
    })
  }
  /**
   * On init
   */
  ngOnInit(): void {
    this.getNotificationMessage();
  }
}
