import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { BehaviorSubject, Observable, throwError, of, forkJoin, Subject, merge } from 'rxjs';
import { map, catchError, tap } from "rxjs/operators";
import { CSVToArray } from '../commonService/tools/csv2json';

@Injectable({
  providedIn: 'root'
})
export class ProductDataService {
  // Public
  public paramsName: string = '';
  /**
   * Constructor
   *
   * @param {HttpClient} _httpClient
   */
  constructor(
    private _httpClient: HttpClient,
    private _authenticationService: AuthenticationService,
  ) {
    // Set the defaults
  }

  /**
   * Resolver
   *
   * @param {ActivatedRouteSnapshot} route
   * @param {RouterStateSnapshot} state
   * @returns {Observable<any> | Promise<any> | any}
   */
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {

  }

  getProductListByFilters(params): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/generalData/productList/byFilters`, {params: params}).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getNetworkListByFilters(deploymentMode, params): Promise<any> {
    let subclass = deploymentMode===1 ? 'cellularSpecific' : 'wifiSpecific'
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/${subclass}/networkList/byFilters?deploymentMode=${deploymentMode}`, {params: params}).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }
  

  /**
  * Get products-list
  */
  getProductList(): Observable<any[]> {
    return this._httpClient.get('nbi/device/generalData/productList').pipe(
      map((resp: any) => resp),
      catchError(error => throwError(() => error))
    );
  }

  getOrgProductList(): Observable<any[]> {
    return this._httpClient.get('nbi/device/generalData/orgProductList').pipe(
      map((resp: any) => resp),
      catchError(error => throwError(() => error))
    );
  }

  getNetworkList(params?): Observable<any[]> {

    if(params===1){
      return this._httpClient.get(`nbi/device/cellularSpecific/networkList?deploymentMode=${params}`).pipe(
        map((resp: any) => resp),
        catchError(error => throwError(() => error))
      );
    }else if(params===2 || params===3){
      return this._httpClient.get(`nbi/device/wifiSpecific/networkList?deploymentMode=${params}`).pipe(
        map((resp: any) => resp),
        catchError(error => throwError(() => error))
      );
    }
  }
  
  


  getProductImage(value) {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/productAdmin/${value}/image`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getInitProvisioningDefault(): Observable<any[]> {
    return this._httpClient.get('nbi/device/productAdmin/all/init/default').pipe(
      map((resp: any) => resp),
      catchError(error => throwError(() => error))
    );
  }

  formatProductName(productlist) {
    let productNamelist = [];
    if (this._authenticationService.isAdmin) {
      productNamelist = [{
        name: 'All Products',
        value: 'ALL'
      }];
    }
    productlist = productlist.sort((a, b) => {
      if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
      return 1
    });
    productlist.forEach(item => {
      productNamelist.push({
        name: item.name,
        value: item.name
      });
    })
    return productNamelist;
  }
  /**
  * Get New products-list
  */
  getProductNameList(): Observable<any[]> {
    return this._httpClient.get('nbi/device/generalData/productList')
      .pipe(map((resp: any) => {
        return this.formatProductName(resp);
      }));
  }
  
  // getProductNameList(): Observable<any> {
  //   let productlist = this.onProductsListChanged.getValue() || [];
  //   if (productlist.length === 0) {
  //     return this.getNewProductList();
  //   } else {
  //     return of(this.formatProductName(productlist));
  //   }
  // }
  /**
  * add product
  */
  addNewProduct(params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post('nbi/device/productAdmin/productAdd', params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  // add network
  addNetwork(params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post('nbi/device/productAdmin/networkAdd', params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }
  

  /**
  * update product
  */
  updateSingleProduct(id, params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/device/productAdmin/${id}/updateProduct`, params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
  * update Network
  */
  updateSingleNetwork(id, params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/device/productAdmin/${id}/updateNetwork`, params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
  * Get product summart rpt-list
  */
  getSummaryReportList(productId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/productAdmin/${productId}/summaryReportConfig`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
  * Get server summary rpt-list
  */
  getServerDefaultSummaryReportList(): Observable<any[]> {
    return this._httpClient.get('nbi/device/productAdmin/summaryReportConfig')
      .pipe(tap((resp: any) => {}),
        catchError(error => throwError(() => error))
      )
  }

  /**
  * Save product summary rpt-list
  */
  saveSummaryReportList(productId, params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/productAdmin/${productId}/summaryReportConfig`, params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  deleteSingleProduct(productId, force?): Promise<any[]> {
    let url = `nbi/device/productAdmin/${productId}/deleteProduct${force ? '?force=true' : ''}`
    return new Promise((resolve, reject) => {
      this._httpClient.delete(url, {
        responseType: "text"
      }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  // delete network
  deleteSingleNetwork(productId, force?): Promise<any[]> {
    let url = `nbi/device/productAdmin/${productId}/deleteNetwork${force ? '?force=true' : ''}`
    return new Promise((resolve, reject) => {
      this._httpClient.delete(url, {
        responseType: "text"
      }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  deactivateSingleProduct(productId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/device/productAdmin/${productId}/deactivate`, {}).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  activateSingleProduct(productId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/device/productAdmin/${productId}/activate`, {}).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }


  getAttribute(productId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/productAdmin/${productId}/getAttributes`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  updateProductSetting(productId, params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/productAdmin/${productId}/updateAttribute`, params, {
        headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' })
      }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  updateProductBooleanSetting(productId, booleanParams, params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/productAdmin/${productId}/updateAttribute/${booleanParams}`, params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getPermissionDevice(productId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/productAdmin/${productId}/getProduct`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  updatePermissionDevice(productId, params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/device/productAdmin/${productId}/cpeList`, params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: err => {
          reject(err)
        }
      });
    });
  }

  checkAllowList(id, params): Observable<any[]> {
    return this._httpClient.post(`nbi/device/productAdmin/${id}/cpeList/check`, params)
      .pipe(map((resp: any) => resp),
        catchError(error => throwError(() => error))
      )
  }

  buildDeviceIdStructs(csv) {
    var commaArray = CSVToArray(csv, ",");
    var array = commaArray

    var deviceIdStructsArray = [];
    for (var i = 0; i < array.length; i++) {
      var serialNumber = array[i][0]
      if (serialNumber) {
        deviceIdStructsArray.push({
          "serialNumber": serialNumber
        });
      }
    }
    return deviceIdStructsArray;
  }

  getWiFiConfigruation(productId) {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/productAdmin/${productId}/vendorConfigProvision/request`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  applyWiFiConfigruation(productId, param) {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/productAdmin/${productId}/vendorConfigProvision/request`, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  addTag(params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/productAdmin/addTag`, params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  deleteTag(params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/productAdmin/deleteTag`, params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

}
