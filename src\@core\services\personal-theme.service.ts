import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { AuthenticationService } from 'app/auth/service';

@Injectable({
  providedIn: 'root'
})
export class PersonalThemeService {
  public personalTheme: BehaviorSubject<any>;
  constructor(
    private _httpClient: HttpClient,
    private _authService: AuthenticationService
  ) {
    this.personalTheme = new BehaviorSubject({});
  }
  resolve(): Observable<any> {
    this._authService.checkUrl()
    if (this._authService.check('addOn', 'personalTheme', 'read')) {
      const personalTheme = this.personalTheme.getValue();
      if (personalTheme && personalTheme.key && personalTheme.key === this._authService.personalThemeKey) {
        return personalTheme;
      } else {
        return this.getPersonalTheme();
      }
    } else {
      let result = { key: new Date().toISOString() , personalTheme: {} }
      this.personalTheme.next(result);
      return of(result);
    }
  }

  

  getPersonalTheme(): Observable<any> {
    return this._httpClient.get('nbi/addOn/personalTheme')
      .pipe(map((resp: any) => {
        const key = this._authService.username + '*' + new Date().toISOString();
        if (resp.config) {
          if (resp.config.layout) {
            if (resp.config.layout.buyNow) {
              delete(resp.config.layout.buyNow);
            }
            if (resp.config.layout.editMode) {
              resp.config.layout.editMode = false;
            }
            if (resp.config.layout.skin) {
              if (resp.config.layout.skin !== 'default') {
                resp.config.layout.skin = 'dark'
              }
            }
          }
        }
        let result = { key: key, personalTheme: resp }
        this._authService.personalThemeKey = key
        this.personalTheme.next(result);
        return result;
      }));
  }
}