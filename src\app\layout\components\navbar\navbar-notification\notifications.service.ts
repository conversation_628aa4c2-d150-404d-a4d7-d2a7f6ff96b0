import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';


import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, tap, share } from "rxjs/operators";

@Injectable({
  providedIn: 'root'
})
export class NotificationsService {
  // Public
  public apiData = [];
  public onApiDataChange: BehaviorSubject<any>;
  public apiDataObservable: Observable<any>;

  /**
   *
   * @param {HttpClient} _httpClient
   */
  constructor(private _httpClient: HttpClient) {
    this.onApiDataChange = new BehaviorSubject('');
  }

  /**
   * Get Notifications Data
   */
  getNotificationsData(): Observable<any[]> {
    if (this.apiDataObservable) {
      return this.apiDataObservable;
    } else {
      this.apiDataObservable = this._httpClient.get('nbi/alarm/deviceAlarm/24hours').pipe(
        tap((resp: any) => {
          this.apiData = resp.data
          this.onApiDataChange.next(this.apiData);
        }),
        share(),
        catchError(error => throwError(() => error))
      );
      return this.apiDataObservable;
    }
  }
}
