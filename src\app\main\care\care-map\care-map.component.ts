import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { CareService } from 'app/main/care/care.service';
import cloneDeep from 'lodash/cloneDeep';
import { MapInfoWindow, MapMarker } from '@angular/google-maps';
import { DeviceInfoService } from 'app/main/devices/device-info/device-info.service'
import { StorageService } from 'app/main/commonService/storage.service';
import { UserService } from 'app/auth/service/user.service';
import { NominatimService } from 'app/main/commonService/nominatim.service';
import * as L from 'leaflet';
import { TranslateService } from '@ngx-translate/core';
import { GoogleMapService } from 'app/main/commonService/google-map.service';
import { FormatIpPipe } from 'app/main/commonService/pipes/format-ip.pipe';
const iconRetinaUrl = 'assets/marker-icon-2x.png';
const iconUrl = 'assets/marker-icon.png';
const shadowUrl = 'assets/marker-shadow.png';
const iconDefault = L.icon({
  iconRetinaUrl,
  iconUrl,
  shadowUrl,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  tooltipAnchor: [16, -28],
  shadowSize: [41, 41]
});

@UntilDestroy()
@Component({
  selector: 'app-care-map',
  templateUrl: './care-map.component.html',
  styleUrls: ['./care-map.component.scss']
})
export class CareMapComponent extends Unsubscribe implements OnInit {
  @ViewChild('map', { static: true }) mapElement: ElementRef;
  @ViewChild(MapInfoWindow, { static: false }) infoWindow: MapInfoWindow;
  @Input() accessor: any;
  public blockUIStatus = false;
  public apiLoaded = new BehaviorSubject<boolean>(false);
  public markerZoom = 18;
  public streetZoom = 12;

  public serialNumber: string;
  public ip: string;
  public productName: string;
  public deviceId: string;
  public cloneRes: any;
  public status: string;
  public lat: any;
  public lng: any;
  public LocationCenter: google.maps.LatLngLiteral;

  public markers: object[]
  public mapDraggable = false
  public apiKey;
  public mapType: any
  private geocoder: google.maps.Geocoder;


  constructor(
    httpClient: HttpClient,
    private _careService: CareService,
    private _deviceInfoService: DeviceInfoService,
    private _storageService: StorageService,
    private cdr: ChangeDetectorRef,
    private _userService: UserService,
    private nominatimService: NominatimService,
    private googleMapsService: GoogleMapService,
    private translateService: TranslateService,
    private formatIpPipe: FormatIpPipe
  ) {
    super();
    this.mapType = this._storageService.get('locationType') ? this._storageService.get('locationType').toString() : this._userService.getInformation().pipe(untilDestroyed(this)).subscribe((response: any) => { return this._storageService.get('locationType') })
    // console.log(this.mapType)
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CONFIRMSAVELOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMSAVELOCA');
      this.DOSAVELOCA = this.translateService.instant('DEVICES.ACTION.DOSAVELOCA');
      this.CONFIRMRESETLOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMRESETLOCA');
      this.DORESETLOCA = this.translateService.instant('DEVICES.ACTION.DORESETLOCA');
      this.SAVESUCC = this.translateService.instant('DEVICES.ACTION.SAVESUCC');
      this.SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
      this.SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
      this.OSMERROR = this.translateService.instant('DEVICES.OSMERROR');
      this.GOOGLEMAPERROR = this.translateService.instant('DEVICES.GOOGLEMAPERROR');
    })
  }


  public CONFIRMSAVELOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMSAVELOCA');
  public DOSAVELOCA = this.translateService.instant('DEVICES.ACTION.DOSAVELOCA');
  public CONFIRMRESETLOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMRESETLOCA');
  public DORESETLOCA = this.translateService.instant('DEVICES.ACTION.DORESETLOCA');
  public SAVESUCC = this.translateService.instant('DEVICES.ACTION.SAVESUCC');
  public SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
  public RESETSUCC = this.translateService.instant('DEVICES.ACTION.RESETSUCC');
  public RESETFAIL = this.translateService.instant('DEVICES.ACTION.RESETFAIL');
  public OSMERROR = this.translateService.instant('DEVICES.OSMERROR');
  public GOOGLEMAPERROR = this.translateService.instant('DEVICES.GOOGLEMAPERROR');
  public ismapErr: boolean = false


  getDeviceId(): void {
    this.customSubscribe(this._careService.careDeviceChanged, res => {
      if (res && res.id) {
        this.deviceId = res.id;
        this.getLocationRes(this.deviceId)
        if (res.deviceIdStruct) {
          this.serialNumber = res.deviceIdStruct.serialNumber;
        } else {
          this.serialNumber = res.serialNumber
        }
        this.ip = res.ip
        this.productName = res.productName
      } else {
        this.deviceId = '';
        this.serialNumber = '';
        this.ip = '';
        this.productName = '';
        this.drawMap('', '');
        // this.drawMap(37.4224772, -122.0859043)
        this.status = 'Offline';
      }
    })
  }



  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        let deviceId = this.deviceId;
        this._careService.refreshLocation(deviceId).finally(() => {
          this.getLocationRes(deviceId);
        });
        break;
    }
  };

  isNumber(value) {
    if (value && /^\d+$/.test(value)) {
      return true;
    }
    return false;
  }

  public mapOptions = {
    mapTypeControl: false,
    scaleControl: false,
    streetViewControl: false,
    rotateControl: false,
    fullscreenControl: true,
    icon: null,
    scrollwheel: false,
    zoomControl: true,
    gestureHandling: "cooperative"
  }


  drawMap(lat, lng) {
    this.LocationCenter = {
      lat: parseFloat(lat),
      lng: parseFloat(lng)
    }
    this.markers = [
      {
        position: {
          lat: parseFloat(lat),
          lng: parseFloat(lng)
        },
        options: {
          draggable: this.mapDraggable,
          icon: {
            url: this.status === 'Online'
              ? 'assets/images/eap_online.png'
              : 'assets/images/eap_offline.png',
            // anchor: { x: 25, y: 50 }, // 設定錨點 (寬度一半, 高度)
          },
        }
      }
    ]
  }

  public updateBtnDisabled = true
  getLocationRes(deviceId) {
    this.updateBtnDisabled = true;
    this.blockUIStatus = true;
    this._deviceInfoService.getLocation(deviceId).then((res: any) => {
      // console.log(res)
      this.cloneRes = cloneDeep(res)
      if (res.status == 'Inactive' || res.status == 'Offline') {
        res.status = 'Offline'
      } else {
        res.status = 'Online'
      }
      this.status = res.status
      if (res.userDefindLat && res.userDefindLng) {
        this.lat = res.userDefindLat
        this.lng = res.userDefindLng
        this.drawMap(this.lat, this.lng)
      }
      else if (res.dev_Location) {
        let arr = ["GPS", "AGPS", "Manual", "External"];
        let find = arr.some(i => {
          if (res.dev_Location[i]) {
            this.lat = res.dev_Location[i].lat
            this.lng = res.dev_Location[i].lng
            return true;
          }
          return false;
        });
        if (!find) {
          this.lat = 0
          this.lng = 0
        }
        this.drawMap(this.lat, this.lng)
      }
      else {
        this.lat = 0
        this.lng = 0
        this.drawMap(this.lat, this.lng)
      }

      // console.log(this.lat)
      // console.log(this.lng)
      // osm
      this.initialPosition = {
        "lat": this.lat,
        "lng": this.lng
      }
      if (this.map) {
        this.map.setView([this.lat, this.lng]);
        this.marker.setLatLng(this.initialPosition);
        this.updatePopupContent(this.lat, this.lng);
      }
      this.osmInitMap(this.lat, this.lng);
      this.cdr.detectChanges();
    }).finally(() => {
      this.blockUIStatus = false;
    });
  }

  updatePopupContent(lat: number, lng: number): void {
    if (this.marker) {
      const formattedIp = this.formatIpPipe.transform(this.ip);
      this.marker.setPopupContent(`
        <div>
          <p>S/N : ${this.serialNumber}</p>
          <p>IP : ${formattedIp}</p>
          <p>Product : ${this.productName}</p>
          <p>Status : ${this.status}</p>
          <p>Latitude : ${lat}</p>
          <p>Longitude : ${lng}</p>
        </div>
      `);
    }
  }

  /**
 * Marker with Tooltip Component
 * @param marker
 */
  openInfo(marker: MapMarker) {
    this.infoWindow.open(marker);
  }

  public updateBtn = false
  public editBtn = false
  public initialPosition

  editLocation(event) {
    let value = event.currentTarget.checked
    if (value == true) {
      this.updateBtn = true
      this.mapDraggable = true
      this.editBtn = true
      this.getLocationRes(this.deviceId)
    } else {
      this.updateBtn = false
      this.mapDraggable = false
      this.editBtn = false
      this.getLocationRes(this.deviceId)
    }
  }

  osmEditLocation(event) {
    let value = event.currentTarget.checked
    if (value == true) {
      this.updateBtn = true
      this.mapDraggable = true
      this.editBtn = true
      this.marker.dragging.enable();
      this.marker.on('dragend', (event) => {
        this.updateBtnDisabled = false
        this.lat = event.target.getLatLng().lat;
        this.lng = event.target.getLatLng().lng;
        // console.log(this.lat)
        // console.log(this.lng)
        this.cdr.detectChanges();
      });
      // this.getLocationRes(this.deviceId)
    } else {
      this.updateBtn = false
      this.mapDraggable = false
      this.editBtn = false
      this.marker.dragging.disable();
      this.getLocationRes(this.deviceId)
    }
  }

  mapDragend(position) {
    this.updateBtnDisabled = false
    this.lat = position.latLng.lat()
    this.lng = position.latLng.lng()

    // console.log('Dragend lat', this.lat)
    // console.log('Dragend lng', this.lng)
  }

  inputLatLngChange(lat, lng) {
    this.updateBtnDisabled = false
    if (lat) {
      this.lat = lat
      this.drawMap(this.lat, this.lng)
    } else if (lng) {
      this.lng = lng
      this.drawMap(this.lat, this.lng)
    }
    // console.log(this.lat, this.lng)
  }




  map: any
  marker: any
  public LOADMAPFAIL = this.translateService.instant('DEVICES.LOADMAPFAIL');
  osmInitMap(lat, lng): void {
    this.ismapErr = false
    // 创建新的地图对象
    this.map = L.map('map', {
      center: [lat, lng],
      zoom: 16,
      zoomControl: false,
      scrollWheelZoom: false,
    });

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; OpenStreetMap contributors',
    }).on('tileerror', (error) => {
      // console.log(error)
      this.ismapErr = true
    }).addTo(this.map);

    // 添加缩放控件
    L.control.zoom({
      position: 'bottomright',
    }).addTo(this.map);

    // 创建新的标记
    const customIcon = L.icon({
      iconUrl: this.status == 'Online' ? 'assets/images/eap_online.png' : 'assets/images/eap_offline.png',
      iconAnchor: [18, 48], // 正中間底部對應 (寬度一半, 高度)
    });
    L.Marker.prototype.options.icon = customIcon;
    this.marker = L.marker([lat, lng], {
      icon: customIcon,
      opacity: 1.0,
      draggable: false
    }).addTo(this.map);
    // console.log(this.marker)
    const formattedIp = this.formatIpPipe.transform(this.ip);
    // 绑定标记弹出窗口
    this.marker.bindPopup(`
      <div>
        <p>S/N : ${this.serialNumber}</p>
        <p>IP : ${formattedIp}</p>
        <p>Product : ${this.productName}</p>
        <p>Status : ${this.status}</p>
        <p>Latitude : ${lat}</p>
        <p>Longitude : ${lng}</p>
      </div>
      `);

    this.marker.on('popupopen', () => {
      const overlay = document.querySelector('.top-header-action') as HTMLElement;;
      if (overlay) {
        overlay.style.pointerEvents = 'none'; // 允許點擊彈出窗口
      }
    });

    this.marker.on('popupclose', () => {
      const overlay = document.querySelector('.top-header-action') as HTMLElement;;
      if (overlay) {
        overlay.style.pointerEvents = 'auto'; // 恢復事件處理
      }
    });

    setTimeout(() => {
      this.map.invalidateSize();
    }, 0);
  }

  reverseGeocode(lat: number, lng: number): Promise<google.maps.GeocoderResult[]> {
    this.geocoder = new google.maps.Geocoder();
    const latlng = new google.maps.LatLng(lat, lng);
    return new Promise((resolve, reject) => {
      this.geocoder.geocode({ location: latlng }, (results, status) => {
        if (status === google.maps.GeocoderStatus.OK) {
          if (results[0]) {
            resolve(results);
          } else {
            reject('No results found');
          }
        } else {
          reject('Geocoder failed due to: ' + status);
        }
      });
    });
  }

  ngAfterViewInit() {
    if (this.mapType == '1') {
      this.getLocationRes(this.deviceId)
      this.cdr.detectChanges();
    }
  }
  ngOnInit(): void {
    this.getDeviceId()
    if (this.mapType === '0') {
      this.googleMapsService.loadGoogleMapsApi()
        .then(() => {
          Promise.resolve().then(() => {
            this.apiLoaded.next(true);
            this.getLocationRes(this.deviceId)
          });
          this.ismapErr = false
          this.cdr.detectChanges();
        })
        .catch(error => {
          Promise.resolve().then(() => {
            this.apiLoaded.next(false);
          });
          console.error('Google Maps API 加載失敗:', error);
          this.ismapErr = true
          this.cdr.detectChanges();
        });
    } else if (this.mapType == '1') {
      this.getDeviceId()
      this.cdr.detectChanges();
    }
  }
  ngOnDestroy(): void {
    if (this.map) {
      this.map.remove();
    }
  }

}
