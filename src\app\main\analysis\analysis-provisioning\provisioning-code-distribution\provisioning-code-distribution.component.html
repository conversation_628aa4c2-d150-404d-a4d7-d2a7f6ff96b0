<core-card
  [actions]="['reload','maximize','download']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title d-flex justify-content-between align-items-center"
  style="width: calc(100% - 65px);">
    <span
      class="text-truncate"
      ngbTooltip="{{ accessor.name | translate }}"
      container="body">
      {{ accessor.name | translate }}
    </span>
    <div class="mr-50">
      <select
        class="form-control form-control-sm"
        (change)="changeFn($event)"
        [(ngModel)]="selectedProductName"
        name="provisioningCodeProductName"
        required>
        <option
          *ngFor="let item of productNameSelect"
          [value]="item">
          {{ item }}
        </option>
      </select>
    </div>
  </h4>
  <div class="card-body">
    <main
      class="w-100 h-100 "
      #provisioningCodeDistributioneRef>
      <app-pie-echarts
        [chartRef]="provisioningCodeDistributioneRef"
        [chartData]="chartData"
        [isShowNoData]="isShowNoData"
        [title]="'ProvisioningStatistics-ProvisioningCodeDistribution'"
        [downloadData]="downloadData"
        [openModal]="openModalFlag">
      </app-pie-echarts>
    </main>
  </div>
</core-card>
