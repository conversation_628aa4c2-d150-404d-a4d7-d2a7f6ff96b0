import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { SystemNodesService } from 'app/main/system/system-nodes/system-nodes.service';
import { HttpClient } from '@angular/common/http';
import { EncryptUtilsService } from 'app/main/commonService/encrypt-utils.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-navbar-shutdown-node',
  templateUrl: './navbar-shutdown-node.component.html',
  styleUrls: ['./navbar-shutdown-node.component.scss']
})
export class NavbarShutdownNodeComponent implements OnInit {
  @Output() dismissEvt = new EventEmitter<string>();
  public loading: boolean = false;
  public basicNewPwdShow: boolean = false;

  public nodesList: any;
  public nodesName: string
  public password: string
  public reset = false;

  public ERROR = this.translateService.instant('DEVICES.ACTION.ERROR');
  constructor(
    private _systemNodesService: SystemNodesService,
    private _httpClient: HttpClient,
    private _encryptUtilsService: EncryptUtilsService,
    private toastr: ToastrUtilsService,
    private translateService: TranslateService
  ) { }

  getNodes() {
    this._systemNodesService.getCommonNodes().then((res: any) => {
      this.nodesList = res.sort((a, b) => {
        if (a.toLowerCase() < b.toLowerCase()) return -1
        return 1
      })
      this.nodesName = this.nodesList.map(item => {
        return item
      })[0]
    }).finally(() => {
    })
  }



  onSubmit(form) {
    if (form.form && form.form.status === "VALID") {
      this.onSubmits(this.nodesName, '', '')
    }
  }

  onSubmits(nodesName, password, newKey = '') {
    let date = new Date();

    if (newKey) {
      date = new Date(Number(newKey));
      this.reset = false;
    } else {
      this.reset = true;
    }
    const key = date.toISOString();
    let passwd = ''
    let passwd1 = {}
    if (password) {
      passwd = this._encryptUtilsService.encryptNodePasswd(password, key);
      passwd = this._encryptUtilsService.b64EncodeUnicode(passwd + ":" + date.getTime());
      passwd1 = { "password": passwd }
    } else {
      passwd = ''
    }

    this._systemNodesService.shutdownCommonNode(nodesName, passwd1).then((res: any) => {
      this.dismissEvt.emit('Cross click');
      this.toastr.showSuccessMessage(this.translateService.instant('DEVICES.ACTION.SHUTDOWN_SUCCESS'),'');
    }).catch(error => {
      if (error.status == 400) {
        if (this.reset) {
          this.onSubmits(this.nodesName, this.password, error.error);
        }
      } else {
        this.toastr.showErrorMessage(this.ERROR, error.error);
      }

    }).finally(() => {
      this.loading = false;
    })

  }
  ngOnInit(): void {
    this.getNodes()
  }


}
