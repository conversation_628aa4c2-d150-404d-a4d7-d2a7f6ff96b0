<div
  *ngIf="horizontalMenu"
  class="navbar-header d-xl-block d-none">
  <!-- Navbar brand -->
  <ul class="nav navbar-nav flex-row">
    <li class="nav-item">
      <a
        class="navbar-brand"
        [routerLink]="['/']">
        <span class="brand-logo">
          <img
            src="{{ coreConfig.app.appLogoImage }}"
            alt="brand-logo"
            width="36">
        </span>
        <h2 class="brand-text mb-0">{{ coreConfig.app.appName }}</h2>
      </a>
    </li>
  </ul>
  <!-- / Navbar brand -->
</div>
<div class="navbar-container d-flex content">
  <div class="bookmark-wrapper d-flex align-items-center">
    <!-- Menu Toggler | Menu icon will be hidden in case of layout without menu -->
    <ul
      class="nav navbar-nav d-xl-none"
      *ngIf="!coreConfig.layout.menu.hidden">
      <li class="nav-item">
        <a
          class="nav-link menu-toggle"
          (click)="toggleSidebar('menu')">
          <span
            [data-feather]="'menu'"
            [class]="'ficon'"></span>
        </a>
      </li>
    </ul>
    <!-- / Menu Toggler -->
    <!-- Toggle skin -->
    <!-- <li class="nav-item d-none d-lg-block">
      <a
        type="button"
        class="nav-link nav-link-style btn"
        (click)="toggleDarkSkin()">
        <span
          [ngClass]="currentSkin === 'dark' ? 'icon-sun' : 'icon-moon'"
          class="ficon font-medium-5 feather"></span>
      </a>
    </li> -->
    <!-- / Toggle skin -->
    <!-- Edit Mode -->
    <li class="nav-item d-none d-lg-block">
      <a
        *ngIf="personalThemePermission"
        type="button"
        class="nav-link nav-link-style btn"
         [ngClass]="{
            'btn-warning': editMode,
            'btn-disabled': isDisabled
          }"
        ngbTooltip="{{ 'COMMON.EDITMODE' | translate }}"
        (click)="toggleEditMode()"
        container="body">
        <span
          data-feather="grid"
          class="ficon font-medium-5 feather"></span>
      </a>
    </li>
    <!-- Toggle Widget -->
    <li
      class="nav-item d-none d-lg-block"
      *ngIf="editMode">
      <a
        type="button"
        class="nav-link nav-link-style btn"
        ngbTooltip="{{ 'DEVICES.EDITCOMPONENT' | translate }}"
        (click)="toggleWidget(toggleWidgetModal)">
        <span
          data-feather="plus-square"
          class="ficon font-medium-5 feather"></span>
      </a>
    </li>
    <!-- Reset Personal Theme -->
    <li
      class="nav-item d-none d-lg-block"
      *ngIf="editMode">
      <a
        type="button"
        class="nav-link nav-link-style btn"
        ngbTooltip="{{ 'DEVICES.ACTION.RESETCURRENTPAGE' | translate }}"
        (click)="reset()">
        <span
          data-feather="refresh-cw"
          class="ficon font-medium-5 feather"></span>
      </a>
    </li>
    <!-- <li
      class="nav-item d-none d-lg-block"
      *ngIf="editMode">
      <a
        type="button"
        class="nav-link nav-link-style btn text-danger"
        ngbTooltip="{{ 'DEVICES.ACTION.RESETALL' | translate }}"
        (click)="reset(true)">
        <span
          data-feather="refresh-cw"
          class="ficon font-medium-5 feather"></span>
      </a>
    </li> -->
    <!-- / Edit Mode -->
  </div>
  <ul class="nav navbar-nav align-items-center ml-auto">
    <!-- Search -->
    <app-navbar-search *ngIf="false"></app-navbar-search>
    <!-- / Search -->
    <!-- Summary report -->
    <app-navbar-summary
      *ngIf="summaryReportPermission && summaryDisplay"
      [editMode]="editMode"></app-navbar-summary>
    <!-- / Summary report -->
    <!-- Notification -->
    <app-navbar-notification
      *ngIf="notificationPermission && alarmDisplay"
      [editMode]="editMode"></app-navbar-notification>
    <!-- / Notification -->
    <li
      ngbDropdown
      #userDropdown="ngbDropdown"
      [autoClose]="'outside'"
      class="nav-item dropdown-user">
      <button
        class="btn nav-link dropdown-toggle dropdown-user-link custom-style"
        ngbDropdownToggle
        id="navbarUserDropdown"
        [disabled]="editMode"
        (click)="showLanguage=false;(editMode?userDropdown.close():null)"
        aria-haspopup="true"
        aria-expanded="false">
        <ng-container *ngIf="this.currentUser">
          <div class="user-nav d-sm-flex d-none">
            <span class="user-name font-weight-bolder">{{ this.currentUser.userName }}</span>
            <span class="user-status">{{ this.currentUser.role }}</span>
          </div>
          <div *ngIf="!!this.currentUser.avatar; else customAvatar">
            <img
              class="rounded-circle mr-1"
              src="{{ this.currentUser.avatar }}"
              (error)="this.currentUser.avatar = null"
              height="32"
              width="32"
              alt="datatable-avatar">
          </div>
          <ng-template #customAvatar>
            <div class="avatar mr-1 ml-0 bg-light-success">
              <div class="avatar-content">
                {{ this.currentUser.userName | initials }}
              </div>
            </div>
          </ng-template>
        </ng-container>
      </button>
      <div
        ngbDropdownMenu
        aria-labelledby="navbarUserDropdown"
        class="dropdown-menu dropdown-menu-right">
        <ng-container *ngIf="this.currentUser">
          <a
            ngbDropdownItem
            *ngIf="this.currentUser.role != 'CSR'"
            (click)="$event.stopPropagation();userDropdown.close();"
            [routerLink]="['/users/account']">
            <span
              [data-feather]="'user'"
              [class]="'mr-50'"></span>
            {{ 'USERS.PROFILE' | translate }}
          </a>
        </ng-container>
        <a
          ngbDropdownItem
          (click)="$event.stopPropagation();userDropdown.close();openChangePasswordModal(modalChangePassword)">
          <span
            [data-feather]="'user'"
            [class]="'mr-50'"></span>
          {{ 'USERS.CHANGEPASSWORD' | translate }}
        </a>
        <!-- <a
          ngbDropdownItem
          (click)="$event.stopPropagation();userDropdown.close();"
          [routerLink]="['/api-docs']"
          >
          <span
            [data-feather]="'book-open'"
            [class]="'mr-50'"></span>
          {{ 'USERS.APIDOCUMENT' | translate }}
        </a> -->
        <ng-container *ngIf="this.currentUser">
          <a
            *ngIf="this.currentUser.role != 'CSR'"
            ngbDropdownItem
            (click)="$event.stopPropagation();userDropdown.close();"
            [routerLink]="['/user-manual']">
            <span
              [data-feather]="'info'"
              [class]="'mr-50'"></span>
            {{ 'USERS.USERMANUAL' | translate }}
          </a>
        </ng-container>
        <a
          ngbDropdownItem
          (click)="showLanguage=!showLanguage">
          <span
            [data-feather]="'align-justify'"
            [class]="'mr-50'"></span>
          {{ languageOptions[_translateService.currentLang].title }}
          <i data-feather="chevron-down"></i>
        </a>
        <li *ngIf="showLanguage">
          <div class="dropdown-divider"></div>
          <a
            *ngFor="let lang of _translateService.getLangs()"
            ngbDropdownItem
            (click)="$event.stopPropagation();userDropdown.close();setLanguage(lang)">
            <i class="flag-icon"></i>
            {{ languageOptions[lang].title }}
          </a>
          <div class="dropdown-divider"></div>
        </li>
        <ng-container *ngIf="this.currentUser">
          <a
            ngbDropdownItem
            *ngIf="this.currentUser.role != 'CSR'"
            (click)="$event.stopPropagation();userDropdown.close();rebootNode(modalRebootNode)">
            <span [class]="'mr-50'">
              <svg>
                <use href="./../assets/fonts/added-icon.svg#reboot-test"></use>
              </svg>
            </span>
            {{ 'DEVICES.REBOOT' | translate }}
          </a>
        </ng-container>
        <ng-container *ngIf="this.currentUser">
          <a
            ngbDropdownItem
            *ngIf="this.currentUser.role != 'CSR'"
            (click)="$event.stopPropagation();userDropdown.close();shutDown(modalShutDown)">
            <span
              [data-feather]="'power'"
              [class]="'mr-50'"></span>
            {{ 'COMMON.SHUTDOWN' | translate }}
          </a>
        </ng-container>
        <a
          ngbDropdownItem
          (click)="$event.stopPropagation();userDropdown.close();logout()">
          <span
            [data-feather]="'log-out'"
            [class]="'mr-50'"></span>
          {{ 'COMMON.LOGOUT' | translate }}
        </a>
      </div>
    </li>
    <!-- / User Dropdown -->
  </ul>
</div>
<!-- Change Password Sidebar -->
<ng-template
  #modalChangePassword
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      {{'USERS.CHANGEPASSWORD'| translate}}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <app-navbar-change-password
      (dismissEvt)="modal.dismiss('Cross click')"
      [username]="currentUser.userName"
      [modal]="modal"></app-navbar-change-password>
  </div>
</ng-template>
<!-- / Change Password Sidebar -->
<!-- Toggle Widget Modal -->
<ng-template
  #toggleWidgetModal
  let-modal>
  <div class="modal-header align-item-center">
    <div class="justify-content-start d-flex ">
      <h4
        class="modal-title  "
        id="myModalLabel4">
        {{ 'DEVICES.EDITCOMPONENT' | translate }}
      </h4>
      <div class="manual avatar ml-0 bg-light-success ml-1">
        <a
          class
          (click)="helpWidgetDocument(modal)"
          ngbTooltip="Help document">
          <span
            [data-feather]="'info'"
            class="ficon"></span>
        </a>
      </div>
    </div>
    <div class="d-flex  justify-content-end">
      <div class="widget-search mr-1">
        <div class="input-group">
          <input
            type="text"
            placeholder="{{'COMMON.WIDGETNAME'|translate}}"
            class="form-control form-control-sm"
            [(ngModel)]="widgetKeyword"
            trim>
          <div class="input-group-append">
            <button
              (click)="filterInput()"
              class="btn btn-primary btn-sm btn-icon"
              type="button">
              <i data-feather="search"></i>
            </button>
          </div>
        </div>
      </div>
      <div>
        <button
          type="button"
          class="close"
          (click)="modal.dismiss('Cross click')"
          aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
    </div>
  </div>
  <div
    id="widgetSettingModal"
    class="modal-body"
    style="margin: 0px !important;padding-top: 0 !important;"
    tabindex="0"
    ngbAutofocus>
    <div class="justify-content-center">
      <div class="row justify-content-center sticky-bar">


        <ul
          ngbNav
          #navCenter="ngbNav"
          class="nav nav-tabs justify-content-center"
          [(activeId)]="activeId">
          <ngb-alert
        *ngIf="formatWidgets.length===0"
        [type]="'warning'"
        [dismissible]="false">
        <div class="alert-body w-100">No matching search results.</div>
        </ngb-alert>

          <li
            ngbNavItem
            *ngFor="let item of formatWidgets;let i = index"
            class="nav-item"
            id="ngb-panel-{{i}}"
            [ngbNavItem]="i">
            <div *ngIf="item.key!=='undefined'">
              <a
                class="nav-link"
                ngbNavLink
                *ngIf="formatWidgets.length > 0 && item.key==='General'"
                (click)="themeIndex = 'General'">
                <span>
                  {{ tabSubclassMapping['General']?tabSubclassMapping['General'] : 'General' | translate}}
                </span>
              </a>
              <a
                class="nav-link"
                ngbNavLink
                *ngIf="formatWidgets.length > 0 && item.key==='Ran'"
                (click)="themeIndex = 'Ran'">
                <span>
                  {{ tabSubclassMapping['Ran']?tabSubclassMapping['Ran'] : 'Ran' | translate}}
                </span>
              </a>
              <a
                class="nav-link"
                ngbNavLink
                *ngIf="formatWidgets.length > 0 && item.key==='Cellular'"
                (click)="themeIndex = 'Cellular'">
                <span>
                  {{ tabSubclassMapping['Cellular']?tabSubclassMapping['Cellular'] : 'Cellular' | translate}}
                </span>
              </a>
              <a
                class="nav-link"
                ngbNavLink
                *ngIf="formatWidgets.length > 0 && item.key==='Wifi'"
                (click)="themeIndex = 'Wifi'">
                <span>
                  {{ tabSubclassMapping['Wifi']?tabSubclassMapping['Wifi'] : 'Wifi'| translate}}
                </span>
              </a>
              <a
                class="nav-link"
                ngbNavLink
                *ngIf="formatWidgets.length > 0 && item.key==='App'"
                (click)="themeIndex = 'App'">
                <span>
                  {{ tabSubclassMapping['App']?tabSubclassMapping['App'] : 'App' | translate}}
                </span>
              </a>
              <a
                class="nav-link"
                ngbNavLink
                *ngIf="formatWidgets.length > 0 && item.key!=='Wifi' && item.key!=='App'&&item.key!=='General'&&item.key!=='Cellular' && item.key!=='Ran'"
                (click)="themeIndex = item.key">
                <span>
                  {{ tabSubclassMapping[item.key]?tabSubclassMapping[item.key] : item.key | translate}}
                </span>
              </a>
            </div>
            <ng-template
              ngbNavContent
              *ngIf="tabSubclassMapping[item.key]?tabSubclassMapping[item.key] : item.key===item.key">
              <div class="row">
                <div
                  class="col-lg-4 col-sm-6 col-12"
                  style="padding: 0.4rem;"
                  *ngFor="let widget of item.widgets;let j = index">
                  <!-- card -->
                  <div
                    class="card"
                    style="margin-bottom: 0px;box-shadow: none;">
                    <!-- unautherized -->
                    <div
                      *ngIf="!widget.render"
                      class="unautherized-badge badge badge-light-warning icon-wrapper">
                      <i
                        class
                        data-feather="slash"></i>
                      <span>Unauthorized</span>
                    </div>
                    <div
                      class="card card-body card-body-animate"
                      [ngClass]="{'hidden-card':!widget.render,'card-body-animate':widget.render}"
                      style="cursor:default !important;margin-bottom:0px !important; height: 100%; background-color: rgba(255, 255, 255, 0.05);padding:9.8px;">
                      <!-- title -->
                      <div class="card-title mb-50">
                        <h5 class="d-flex justify-content-between m-0">
                          <div class="d-flex">
                            <div class="mr-50 icon-wrapper">
                              <svg
                                width="24px"
                                height="24px"
                                style="color: #2BDAC7;"
                                *ngIf="widget.dmsType=='lineChart'">
                                <use href="./../../../../assets/fonts/added-icon.svg#unicons-chart-line"></use>
                              </svg>
                              <i
                                *ngIf="widget.dmsType=='barChart'"
                                data-feather="bar-chart-2"
                                class="bar-chart-color widget-icon"></i>
                              <i
                                *ngIf="widget.dmsType=='pieChart'"
                                class="pie-chart-color widget-icon"
                                data-feather="pie-chart"></i>
                              <i
                                *ngIf="widget.dmsType=='table'"
                                class="table-color widget-icon"
                                data-feather="layout"></i>
                              <svg
                                width="24px"
                                height="24px"
                                style="color:#53cbff"
                                *ngIf="widget.dmsType=='keyValue'">
                                <use href="../../../../assets/fonts/added-icon.svg#tabler-key-value"></use>
                              </svg>
                              <svg
                                width="24px"
                                height="24px"
                                *ngIf="widget.dmsType=='count'"
                                style="display: inline-block;color:#ffc086">
                                <use href="../../../../assets/fonts/added-icon.svg#unicons-dashboard"></use>
                              </svg>
                              <i
                                *ngIf="widget.dmsType=='map'"
                                data-feather="map-pin"
                                class="widget-icon"></i>
                              <i
                                *ngIf="widget.dmsType=='tag'"
                                data-feather="tag"
                                class="widget-icon"></i>
                              <svg
                                width="24px"
                                height="24px"
                                *ngIf="widget.dmsType=='tree'">
                                <use href="../.././../../assets/fonts/added-icon.svg#unicons-cloud-database-tree"></use>
                              </svg>
                              <i
                                *ngIf="widget.dmsType=='terminal'"
                                data-feather="terminal"
                                class="widget-icon"></i>
                              <i
                                *ngIf="widget.dmsType=='image'"
                                data-feather="image"
                                class="widget-icon"></i>
                              <i
                                *ngIf="widget.dmsType=='profile'"
                                data-feather="user"
                                class="widget-icon"></i>
                              <i
                                *ngIf="widget.dmsType=='license'"
                                data-feather="pocket"
                                class="widget-icon"></i>
                              <i
                                *ngIf="widget.dmsType=='link'"
                                data-feather="link"
                                class="widget-icon"></i>
                            </div>
                            <div class="d-flex align-items-center">
                              <app-beautify-content
                                [content]="(widget.name | translate)"
                                [textClass]="'line1  text-truncate text-bold'"></app-beautify-content>
                            </div>
                          </div>
                          <!-- toggle -->
                          <div
                            class="custom-control custom-switch margin-right"
                            style="padding-left: 0.8rem;"
                            [ngClass]="{'cursor-not-allowed'  :!widget.render,'cursor-pointer':widget.render}">
                            <input
                              type="checkbox"
                              class="custom-control-input"
                              id="personalThemeSwitch{{ i }}-{{ j }}"
                              [checked]="!widget.hidden && widget.render"
                              [disabled]="!widget.render"
                              [ngClass]="{'cursor-not-allowed'  :!widget.render,'cursor-pointer':widget.render}"
                              (change)="statusChange(widget)">
                            <label
                              class="custom-control-label"
                              [ngClass]="{'cursor-not-allowed'  :!widget.render,'cursor-pointer':widget.render}"
                              for="personalThemeSwitch{{ i }}-{{ j }}"
                              style="padding-left: 3.2rem;"></label>
                          </div>
                        </h5>
                      </div>
                      <!-- class/subclass -->
                      <div>
                        <span
                          class="badge badge-light-info mr-50"
                          style="font-size: 12px;">
                          {{this.capitalize(classMapping[widget.class]?classMapping[widget.class]:widget.class)}}
                        </span>
                        <span
                          class="badge badge-light-success"
                          style="font-size: 12px;">
                          {{subClassMapping[this.capitalize(widget.subClass)]?subClassMapping[this.capitalize(widget.subClass)]:this.capitalize(widget.subClass)}}
                        </span>
                      </div>
                      <!-- description -->
                      <div class="pl-0 widget-description mt-50">
                        <app-beautify-content
                          class="center-content"
                          style="padding-left: 3px;"
                          [content]="widget.description | translate"
                          [textClass]="'line2 text-secondary'"></app-beautify-content>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </li>
          <li
            ngbNavItem
            *ngIf="isDeviceInfo && dataCollectWidgets.length>0"
            class="nav-item">
            <a
              class="nav-link"
              (click)="themeIndex = 6"
              ngbNavLink>
              <span>KPI</span>
            </a>
            <ng-template ngbNavContent>
              <div class="row">
                <div
                  class="col-lg-4 col-sm-6 col-12"
                  style="padding: 0.4rem;"
                  *ngFor="let widget of dataCollectWidgets;let i = index">
                  <div
                    class="card"
                    style="margin-bottom:0px;">
                    <!-- unautherized -->
                    <div
                      *ngIf="!widget.render "
                      class="unautherized-badge badge badge-light-warning icon-wrapper">
                      <i
                        class
                        data-feather="slash"></i>
                      <span>Unauthorized</span>
                    </div>
                    <div
                      class="card card-body card-body-animate"
                      [ngClass]="{'hidden-card':!widget.render,'card-body-animate':widget.render}"
                      style="cursor:default !important;margin-bottom:0px !important; height: 100%; background-color: rgba(255, 255, 255, 0.05);padding:9.8px">
                      <!-- title -->
                      <div
                        class="card-title mb-50"
                        style="margin-bottom: 5px;">
                        <h5 class="d-flex justify-content-between m-0">
                          <div class="d-flex">
                            <div class="mr-50 icon-wrapper">
                              <!-- <i data-feather="database"></i> -->
                              <svg
                                width="24px"
                                height="24px"
                                style="color: #2BDAC7;"
                                *ngIf="widget.dmsType=='lineChart'">
                                <use href="./../../../../assets/fonts/added-icon.svg#unicons-chart-line"></use>
                              </svg>
                            </div>
                            <div class="d-flex align-items-center">
                              <app-beautify-content
                                style="align-items: center;"
                                [content]="labelNameMapping[widget.labelName]?labelNameMapping[widget.labelName]:widget.labelName"
                                [textClass]="'line1 align-content-center text-truncate text-bold'"></app-beautify-content>
                            </div>
                          </div>
                          <!-- toggle -->
                          <div
                            class="custom-control custom-switch margin-right"
                            style="padding-left: 1rem;"
                            [ngClass]="{'cursor-not-allowed':!widget.render}">
                            <input
                              type="checkbox"
                              class="custom-control-input"
                              id="personalThemeSwitch{{ i }}-{{ j }}"
                              [checked]="!widget.hidden && widget.render"
                              [disabled]="!widget.render"
                              (change)="statusChange(widget)">
                            <label
                              class="custom-control-label cursor-pointer"
                              for="personalThemeSwitch{{ i }}-{{ j }}"
                              style="padding-left: 3.2rem;"></label>
                          </div>
                        </h5>
                      </div>
                      <!-- class/subclass -->
                      <div>
                        <span
                          class="badge badge-light-info mr-50"
                          style="font-size: 12px;">
                          {{this.capitalize(widget.class)}}
                        </span>
                        <span class="badge badge-light-success">
                          {{subClassMapping[this.capitalize(widget.subClass)]?subClassMapping[this.capitalize(widget.subClass)]:this.capitalize(widget.subClass)}}
                        </span>
                      </div>
                      <!-- description -->
                      <div
                        class="pl-0 widget-description mt-50"
                        style="margin-top: 5px;">
                        <app-beautify-content
                          class="center-content"
                          style="padding-left: 3px;"
                          [content]="widget.description | translate"
                          [textClass]="'line2 text-secondary'"></app-beautify-content>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </li>
        </ul>
      </div>
      <div [ngbNavOutlet]="navCenter"></div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Accept click')"
      rippleEffect>
      {{ 'COMMON.CLOSE' | translate }}
    </button>
  </div>
</ng-template>
<!-- / Toggle Widget Modal -->


<!-- Toggle Widget Detail Image Modal -->
<ng-template
  #modalDetailImage
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel2">
      {{ detailImageData.name | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="d-flex justify-content-center align-content-center p-1">
      <img
        [src]="detailImageData.src"
        style="max-width: 100%; max-height: 100%; border: 2px solid grey; box-shadow: 3px 3px 1px #0002;">
    </div>
  </div>
</ng-template>


<!-- rebootNode -->
<ng-template
  #modalRebootNode
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      {{ 'DEVICES.REBOOT' | translate }} |
      <span
        class="text-danger"
        style="font-size: 14px;">
        {{"SYSTEM.NODE_DESCRIPTION.ACSNODEREBOOT" | translate}}
      </span>
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <app-navbar-reboot-node
      (dismissEvt)="modal.dismiss('Cross click')"
      [username]="currentUser.userName"
      [modal]="modal"></app-navbar-reboot-node>
  </div>
</ng-template>
<!-- modalShutDown -->
<ng-template
  #modalShutDown
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      {{ 'COMMON.SHUTDOWN' | translate }} |
      <span
        class="text-danger"
        style="font-size: 14px;">
        {{"SYSTEM.NODE_DESCRIPTION.ACSNODESHUTDOWN" | translate}}
      </span>
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <app-navbar-shutdown-node
      (dismissEvt)="modal.dismiss('Cross click')"
      [username]="currentUser.userName"
      [modal]="modal"></app-navbar-shutdown-node>
  </div>
</ng-template>
