import { Widget } from 'app/layout/widget';
import { SmallCell_NSA_ENB_LIST, SmallCell_NSA_List, SmallCell_SA_List, Enterprise_Wifi_List, _5G_ConCurrent_SmallCell_List } from 'app/main/products/provisioningType';
import { CareGeneralInfoComponent } from './care-general-info/care-general-info.component';
import { CareMapComponent } from './care-map/care-map.component';
import { CareWifiClientsListComponent } from './care-wifi-clients-list/care-wifi-clients-list.component';
import { CareErrorStatusComponent } from './care-error-status/care-error-status.component';

const careWidgets: Widget[] = [
    // GeneralInfo
    {
        componentId: 'CareModule/CareGeneralInfoComponent',
        component: CareGeneralInfoComponent,
        name: 'DEVICES.GENERALINFO',
        description: 'DEVICES.GENERALINFODESCRIPTION',
        class: 'csr',
        subClass: 'common',
        render: false,
        hidden: false,
        cols: 36,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: '',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
        src: {
            light: '/assets/images/device-info/light/general.jpg',
            dark: '/assets/images/device-info/dark/general.jpg'
        }
    },
    // Map Location
    {
        componentId: 'CareModule/CareMapComponent',
        component: CareMapComponent,
        name: 'DEVICES.LOCATION',
        description: 'DEVICES.LOCATIONDESCRIPTION',
        class: 'csr',
        subClass: 'common',
        render: false,
        hidden: false,
        cols: 36,
        rows: 11,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: '',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
        src: {
            light: '/assets/images/device-info/light/general.jpg',
            dark: '/assets/images/device-info/dark/general.jpg'
        }
    },
    // wifi Client list
    {
        componentId: 'CareModule/CareWifiClientsListComponent',
        component: CareWifiClientsListComponent,
        name: 'COMMON.WIFICLIENTLIST',
        description: 'COMMON.WIFICLIENTLISTDESCRIPTION',
        class: 'csr',
        subClass: 'common',
        render: false,
        hidden: false,
        cols: 36,
        rows: 9,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: '',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
        src: {
            light: '/assets/images/device-info/light/wifi-list.jpg',
            dark: '/assets/images/device-info/dark/wifi-list.jpg'
        }
    },
     // ErrorStatus
     {
        componentId: 'CareModule/CareErrorStatusComponent',
        component: CareErrorStatusComponent,
        name: 'DEVICES.ERRORSTATUS',
        description: 'DEVICES.ERRORSTATUSDESCRIPTION',
        class: 'csr',
        subClass: 'common',
        render: false,
        hidden: false,
        cols: 36,
        rows: 6,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: '',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
        src: {
            light: '/assets/images/device-info/light/error.jpg',
            dark: '/assets/images/device-info/dark/error.jpg'
        }
    },   
]

const widgetLayoutMapping = {
    "Default": [
        {
            componentId: 'CareModule/CareGeneralInfoComponent',
            cols: 36,
            rows: 5,
            x: 0,
            y: 0,
            data: [],
        },
        {
            componentId: 'CareModule/CareMapComponent',
            cols: 36,
            rows: 11,
            x: 0,
            y: 5,
            data: [],
        },
        {
            componentId: 'CareModule/CareWifiClientsListComponent',
            cols: 36,
            rows: 9,
            x: 0,
            y: 14,
            data: [],
        },
        {
            componentId: 'CareModule/CareErrorStatusComponent',
            cols: 36,
            rows: 6,
            x: 0,
            y: 20,
            data: [],
        },
    ],
    "SmallCell SA": [
        {
            componentId: 'CareModule/CareGeneralInfoComponent',
            cols: 36,
            rows: 5,
            x: 0,
            y: 0,
            data: [],
        },
        {
            componentId: 'CareModule/CareMapComponent',
            cols: 36,
            rows: 11,
            x: 0,
            y: 5,
            data: [],
        },
        {
            componentId: 'CareModule/CareErrorStatusComponent',
            cols: 36,
            rows: 6,
            x: 0,
            y: 5,
            data: [],
        },

    ],
    "SmallCell NSA": [
        {
            componentId: 'CareModule/CareGeneralInfoComponent',
            cols: 36,
            rows: 5,
            x: 0,
            y: 0,
            data: [],
        },
        {
            componentId: 'CareModule/CareMapComponent',
            cols: 36,
            rows: 5,
            x: 0,
            y: 5,
            data: [],
        },
        
        {
            componentId: 'CareModule/CareErrorStatusComponent',
            cols: 36,
            rows: 6,
            x: 0,
            y: 5,
            data: [],
        },
 
  
    ],
    "SmallCell NSA ENB": [
        {
            componentId: 'CareModule/CareGeneralInfoComponent',
            cols: 36,
            rows: 5,
            x: 0,
            y: 0,
            data: [],
        },
        {
            componentId: 'CareModule/CareMapComponent',
            cols: 36,
            rows: 5,
            x: 0,
            y: 0,
            data: [],
        },
        {
            componentId: 'CareModule/CareErrorStatusComponent',
            cols: 36,
            rows: 6,
            x: 0,
            y: 5,
            data: [],
        },

    ],
    "Enterprise Wi-Fi": [
        {
            componentId: 'CareModule/CareGeneralInfoComponent',
            cols: 36,
            rows: 5,
            x: 0,
            y: 0,
            data: [],
            hidden: false
        },
        {
            componentId: 'CareModule/CareMapComponent',
            cols: 36,
            rows: 11,
            x: 0,
            y: 5,
            data: [],
            hidden: false
        },
        {
            componentId: 'CareModule/CareWifiClientsListComponent',
            cols: 36,
            rows: 9,
            x: 0,
            y: 14,
            data: [],
            hidden: false
        },
        {
            componentId: 'CareModule/CareErrorStatusComponent',
            cols: 36,
            rows: 6,
            x: 0,
            y: 20,
            data: [],
        },
       
    ]
}

function getWidgets(provisioningType): Widget[] {
    let mapping = widgetLayoutMapping['Default'];
    if (SmallCell_SA_List.includes(provisioningType) || _5G_ConCurrent_SmallCell_List.includes(provisioningType)) {
        mapping = widgetLayoutMapping['SmallCell SA'];
    } else if (SmallCell_NSA_List.includes(provisioningType)) {
        mapping = widgetLayoutMapping['SmallCell NSA'];
    } else if (SmallCell_NSA_ENB_LIST.includes(provisioningType)) {
        mapping = widgetLayoutMapping['SmallCell NSA ENB'];
    } else if (Enterprise_Wifi_List.includes(provisioningType)) {
        mapping = widgetLayoutMapping['Enterprise Wi-Fi'];
    }
    let res = []
    careWidgets.forEach(item => {
        let mItem = mapping.find(i => i.componentId === item.componentId);
        if (mItem) {
            item.hidden = false
            item.x = mItem.x
            item.y = mItem.y
            item.cols = mItem.cols
            item.rows = mItem.rows
            item.data = mItem.data
        } else {
            item.hidden = true
        }
        res.push(item)
    })
    return res
}
export { careWidgets,getWidgets }