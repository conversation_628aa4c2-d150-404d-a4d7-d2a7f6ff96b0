<section class="row ">
    
    <div class="col-lg-6 col-6 order-lg-3  d-flex  align-items-center justify-content-end">
      <app-care-button-actions [editMode]="editMode"></app-care-button-actions>
    </div>
    <div class="col-lg-6 col-12 order-lg-2  d-flex  align-items-center justify-content-start">
      <div class="mr-1" style="width: 150px;">
          <ng-select 
            class="column-select-filter ng-select-size-lg mr-50"
            appendTo="body"
            [(ngModel)]="searchDuration"
            [items]="selectDuration"
            [disabled]="editMode"
            bindLabel="name"
            [clearable]="false">
          </ng-select>
       </div>
       <div class="mr-1">
       <input
            type="text"
            id="modernSerialNumber"
            class="form-control"
            [disabled]="editMode"
            name="modernSerialNumber"
            [(ngModel)]="inputNumber">
       </div>
       <div
          class="mr-1"
          style="display: flex;align-items: end;">
          <!-- (click)="searchMetric(searchResultConditionList)" -->
          <!-- ngbTooltip="{{ 'PM.RESEARCH' | translate }}" -->
          <!-- [openDelay]="300" -->
          <button
            type="button"
            class="btn btn-primary"
            style="padding: 10px 10px"
            container="body"
            [disabled]="editMode"
            (click)="searchDetails(inputNumber,modalDetail)"
            placement="auto">
            <i data-feather="search"></i>
            <!-- <svg
              width="14"
              height="14">
              <use href="./../assets/fonts/added-icon.svg#tabler-icons-filter-research"></use>
            </svg> -->
          </button>
        </div>
    </div>
  </section>

  <!-- Model -->


<ng-template
  #modalDetail
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel4">
      {{ 'CARE.SELECTDEVICE' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <div aria-hidden="true">&times;</div>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <ngx-datatable
      #tableRowDetails
      appDatatableRecalculate
      [datatable]="tableRowDetails"
      [rows]="filesList"
      [rowHeight]="37"
      class="bootstrap core-bootstrap"
      [limit]="selectedOption"
      [columnMode]="ColumnMode.force"
      [headerHeight]="40"
      [footerHeight]="40"
      [scrollbarH]="true">
      <!-- id -->
      <ngx-datatable-column
        [width]="150"
        name="{{ 'CARE.SERIALNUMBER' | translate }}"
        prop="serialNumber">
        <ng-template
          let-row="row"
          let-serialNumber="value"
          ngx-datatable-cell-template>
          <app-beautify-content [content]="serialNumber"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- productName -->
      <ngx-datatable-column
        [width]="80"
        name="{{ 'CARE.PRODUCTNAME' | translate }}"
        prop="productName">
        <ng-template
          let-row="row"
          let-productName="value"
          ngx-datatable-cell-template>
          <app-beautify-content [content]="productName"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- select button -->
      <ngx-datatable-column
        [width]="50"
        [sortable]="false"
        [draggable]="false"
        name="{{ 'CARE.SELECTDEVICE' | translate }}">
        <ng-template
          let-row="row"
          ngx-datatable-cell-template>
          <div>
            <button
              (click)="selectFile(row);
              modal.dismiss('Cross click')"
              type="button"
              class="btn btn-primary btn-sm"
              rippleEffect>
              {{ 'ALARMS.SELECT' | translate }}
            </button>
          </div>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
  </div>
</ng-template>
  