import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FileDownloadService } from 'app/main/commonService/file-download.service';

@Component({
  selector: 'app-pm-download',
  templateUrl: './pm-download.component.html',
  styleUrls: ['./pm-download.component.scss']
})
export class PmDownloadComponent implements OnInit {
  public blockUIStatus = false;
  @Output() refreshEvt = new EventEmitter<string>();

  @Input() mode: string;
  @Input() row: any;
  @Input() selectedMetric?: string;
  @Input() selectedMetrics
  @Input() ruleName
  @Input() table

  constructor(
    private translateService: TranslateService,
    private _fileDownloadService: FileDownloadService,
  ) { }

  public metricNme: any;
  public ALL = this.translateService.instant('PM.ALL');
  public METRICRULE = this.translateService.instant('PM.METRICRULE');
  public CONFIRM_DOWNLOAD = this.translateService.instant('PM.CONFIRM_DOWNLOAD');
  public DOWNLOAD = this.translateService.instant('PM.DOWNLOAD');
  public DOWNLOADSUCCESS = this.translateService.instant('PM.DOWNLOADSUCCESS');
  public DOWNLOADFAIL = this.translateService.instant('PM.DOWNLOADFAIL');
  public DO_DOWNLOAD = this.translateService.instant('PM.DO_DOWNLOAD');
  public DODOWNLOAD = this.translateService.instant('PM.DODOWNLOAD');
  public PleaseSelect = this.translateService.instant('PM.PLESESELECT');

  change(message: string) {
    this.refreshEvt.emit(message);
  }

  public performanceReportListDownloadData = [
    'Name',
    'Target Serial Number',
    'Target Group',
    'Conditions',
    'Description',
    'Search Result',
    'Duration',
    'From',
    'To',
    'Created By',
    'Update Time'
  ]

  download() {
    let structList
    let data
    switch (this.table) {
      case 'performanceReportList':
        structList = this.selectedMetrics;
        data = [this.performanceReportListDownloadData];
        structList.map(item => {
          let _structInfoArry = [];
          _structInfoArry.push(item.name.toString() || '-');
          _structInfoArry.push(item.serialNumber?.toString() || '-');
          _structInfoArry.push(item.groupName?.toString() || '-');
          _structInfoArry.push(item.searchRule.toString() || '-');
          _structInfoArry.push(item.description?.toString() || '-');
          _structInfoArry.push(item.deviceCount.toString() || '-');
          _structInfoArry.push(item.duration.toString() || '-');
          _structInfoArry.push(item.beginTime.toString() || '-');
          _structInfoArry.push(item.endTime.toString() || '-');
          _structInfoArry.push(item.creator.toString() || '-');
          _structInfoArry.push(item.updated.toString() || '-');
          return _structInfoArry;
        }).forEach(struct => {
          data.push(struct);
        });
        this._fileDownloadService.generateFileToXLSX({
          data,
          name: "PM_PerformanceReport"
        })
        break;
      case 'merticRuleResult':
        structList = this.selectedMetrics;
        // console.log(this.ruleName)
        data = []
        let keyArr = []
        keyArr = Object.keys(this.selectedMetrics[0]);
        data.push(keyArr)
        structList.map(item => {
          let _structInfoArry = [];
          for (const key in item) {
            _structInfoArry.push(item[key]);
          }
          return _structInfoArry;
        }).forEach(struct => {
          data.push(struct);
        });
        this._fileDownloadService.generateFileToXLSX({
          data,
          name: `PM_SearchResult_${this.ruleName}`
        })
        break;
    }
  }

  ngOnInit(): void {
  }

}
