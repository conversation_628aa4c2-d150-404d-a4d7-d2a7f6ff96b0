import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewEncapsulation, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { ColumnMode, SelectionType } from '@almaobservatory/ngx-datatable';
import { FilesService } from 'app/main/provisioning/files/files.service';
import { ProductDataService } from 'app/main/products/products.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { DeviceListService } from 'app/main/devices/device-list/device-list.service';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import cloneDeep from 'lodash/cloneDeep';
import { AuthenticationService } from 'app/auth/service';
import { Subject } from 'rxjs';
import { debounceTime, retry } from 'rxjs/operators';
import { DataStoreService } from 'app/main/commonService/data-store.service';
import { SanityTestService } from 'app/main/commonService/sanity-test.service';
export interface IqueryParam {
  filter: {},
  page: number;
  size: number,
  sort: string
}

@UntilDestroy()
@Component({
  selector: 'app-public-add-file-form',
  templateUrl: './public-add-file-form.component.html',
  styleUrls: ['./public-add-file-form.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PublicAddFileFormComponent implements OnInit {
  @Input() page: string = 'file';
  @Input() modalConfig: any;
  @Input() fileTypes: any;
  @Input() editData: any;
  @Input() isEditModal: any;
  @Input() targetFileType: string = 'Default';
  @Input() isChangeSN?: boolean = false;
  @Input() selectDisabled?: boolean = false;
  @Input() radioDisabled?: boolean = false;
  @Input() productData?: any;
  @Input() loading = false;
  @Output() transFormDataEvt = new EventEmitter<string>();
  constructor(
    private _filesService: FilesService,
    private _productDataService: ProductDataService,
    private _toastrUtilsService: ToastrUtilsService,
    private _findDeviceService: DeviceListService,
    private modalService: NgbModal,
    private router: Router,
    private translateService: TranslateService,
    private _authService: AuthenticationService,
    private _dataStoreService: DataStoreService,
    private st: SanityTestService,
    private cdr: ChangeDetectorRef,
  ) {
    this.filterChanged.pipe(
      debounceTime(1000),
      untilDestroyed(this))
      .subscribe(model => {
        // console.log(model);
        this.fuzzySearch(this.cloneParams(this.queryParams))
      });

    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.FAIL = this.translateService.instant('DEVICES.ACTION.FAIL');
      this.SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
      this.ADDSUCC = this.translateService.instant('CONFIRM.ADDSUCC');
      this.UPDATESUCC = this.translateService.instant('CONFIRM.UPDATESUCC');
      this.DONE = this.translateService.instant('CONFIRM.DONE');
    })
  }
  public queryParams: IqueryParam = {
    filter: {
      'deviceIdStruct.serialNumber': '',
    },
    page: 1,
    size: 10,
    sort: 'lastConnected,desc'
  }
  public selectedOption = 10;
  public ColumnMode = ColumnMode;
  public fileTypeOptionInTheForm: { name: string, value: string }[];
  public productList = [];
  public fileTargetList: any = [
    { key: "Default", value: "Server" },
    { key: "Product", value: "Product" },
    { key: "Device", value: "Device" }
  ]
  public sNoList: [];
  public pageCount: number
  filterChanged: Subject<string> = new Subject<string>();
  public formError: any = {}
  public FAIL = this.translateService.instant('DEVICES.ACTION.FAIL');
  public SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
  public ADDSUCC = this.translateService.instant('CONFIRM.ADDSUCC');
  public UPDATESUCC = this.translateService.instant('CONFIRM.UPDATESUCC');
  public DONE = this.translateService.instant('CONFIRM.DONE');
  public get noPermission(): boolean {
    return !this._authService.check('provisioning', 'fileSetup', 'write');
  }
  private modalRef: NgbModalRef | undefined;
  public selectedProtocol: any = '';
  ngOnInit(): void {
    // this._productDataService.productListObservable = null
    this.getProductListDataRes();
    this.removeAllfileTypeOption();
    this.checkFileTarget();
  }

  checkFileTarget() {
    if (!this._authService.isAdmin) {
      this.fileTargetList = this.fileTargetList.map(item => {
        if (item.key == 'Default') {
          item.disable = true
        } else {
          item.disable = false
        }
        return item
      })
    }
  }


  public deploymentMode = 0
  getProductListDataRes() {
    if (this.targetFileType == 'Device') {
      this.editData.deviceId = this.editData.targetDeviceId ? this.editData.targetDeviceId : this.editData.deviceId
      this.editData.serial = this.editData.targetDeviceSn ? this.editData.targetDeviceSn : this.editData.serial
      this.editData.fileTarget = this.targetFileType
    } else if (this.targetFileType == 'Product') {
      this.editData.productId = this.editData.targetProductId
      this.editData.targetProductName = this.editData.pName ? this.editData.pName : this.editData.targetProductName
      this.editData.fileTarget = this.targetFileType
    } else {
      this.editData.fileTarget = this.editData.source
    }


    // if (this.page == 'file') {
    //   this.editData.fileDescription = this.editData.description
    // }
    if (this.page == 'product') {
      this.productList = this.productData.map(item => {
        return {
          name: item.name,
          value: item.value,
          productId: item.id,
          protocol: item.protocol,
          deploymentMode: item.deploymentMode,
        }
      }).sort((a, b) => {
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
        return 1
      })
      this.selectedProtocol = this.editData.protocol
      this.deploymentMode = this.editData.deploymentMode
      this.editData.instancePath = this.editData.instancePath ? this.editData.instancePath : this.selectedProtocol == 'usp' ? 'Device.DeviceInfo.FirmwareImage.1.' : ''
    } else {
      this.editData.fileDescription = this.editData?.description || ''
      this._productDataService.getProductList().pipe(untilDestroyed(this)).subscribe(res => {
        this.productList = res.map(item => {
          return {
            name: item.name,
            value: item.value,
            productId: item.id,
            protocol: item.protocol,
            deploymentMode: item.deploymentMode,
          }
        }).sort((a, b) => {
          if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
          return 1
        })
        let currentProductName = Array.isArray(this.editData.productName) ? this.editData.productName[0] : this.editData.productName
        this.selectedProtocol = currentProductName ? this.productList.filter(item => item.name === currentProductName)[0]["protocol"] : ''
        this.deploymentMode = currentProductName ? this.productList.filter(item => item.name === currentProductName)[0]["deploymentMode"] : 0
        this.editData.instancePath = this.editData.instancePath ? this.editData.instancePath : this.selectedProtocol == 'usp' ? 'Device.DeviceInfo.FirmwareImage.1.' : ''
        this.showConfigModal()
      })
    }

  }


  isWifiConfig = false
  isJsonConfig = false
  showConfigModal() {
    if (this.productList) {
      switch (this.targetFileType) {
        case 'Device':
          if (this.deploymentMode) {
            if (this.deploymentMode == 2) {
              this.isWifiConfig = true
            } else if (this.deploymentMode != 2) {
              this.isJsonConfig = true
            }
          } else {
            if (this.editData.vendorConfigContent) {
              this.isJsonConfig = true
            } else {
              return
            }
          }
        case 'Product':
          if (this.deploymentMode && this.deploymentMode == 2) {
            this.isWifiConfig = true
          } else if (this.deploymentMode && this.deploymentMode != 2) {
            this.isJsonConfig = true
          } else {
            return
          }
          break;
        case 'Default':
          if (this.editData.vendorConfigContent) {
            this.isJsonConfig = true
          } else {
            return
          }
          break;
      }
    }
  }

  fileTargetChange(obj) {
    this.editData.fileTarget = obj.key
    this.targetFileType = obj.key
    this.editData.targetProductName = undefined
    this.editData.serial = ''
    this.selectedProtocol = ''
  }

  getProductIdByName(name) {
    this.editData.productId = this.productList.filter(item => name === item.name)[0].productId
    this.selectedProtocol = this.productList.filter(item => name === item.name)[0].protocol
    this.editData.instancePath = this.editData.instancePath ? this.editData.instancePath : this.selectedProtocol == 'usp' ? 'Device.DeviceInfo.FirmwareImage.1.' : ''
  }

  /**
   * All File types option is not needed in the Form
   */
  removeAllfileTypeOption() {
    this.fileTypeOptionInTheForm = this.fileTypes.filter(ft => ft.value !== '-' && ft.value !== '')
  }

  // onSort({ sorts }) {
  //   // console.log('sorts', sorts)
  //   this.queryParams.sort = sorts[0].prop + ',' + sorts[0].dir;
  //   this.fuzzySearch(this.cloneParams(this.queryParams))
  // }

  fuzzySearch(param) {
    param = this.trimAttr(param)
    var params = {
      params: param
    };
    this._findDeviceService.getDeviceList(params).then((res: any) => {
      this.sNoList = res.data.map(item => {
        return {
          ...item.deviceIdStruct,
          deviceId: item.id,
          group: item.group,
          firmwareVersion: item.firmwareVersion,
          productName: item.productName,
          ModelName: item.ModelName,
          mac: item.mac,
          lastIp: item.lastIp,
          status: item.status,
          checked: false,
          protocol: item.protocol
        }
      })
      this.pageCount = res["num"];
    })
  }

  openSelectModal(modalSelect) {
    this.modalService.open(modalSelect, {
      backdrop: 'static',
      size: 'xl',
      scrollable: true
    });
    this.fuzzySearch(this.cloneParams(this.queryParams))
  }

  selectedSn(item) {
    this.editData.deviceId = item.deviceId
    this.editData.serial = item.serialNumber
    this.isChangeSN = true
    this.selectedProtocol = item.protocol
    this.editData.instancePath = this.editData.instancePath ? this.editData.instancePath : this.selectedProtocol == 'usp' ? 'Device.DeviceInfo.FirmwareImage.1.' : ''
    // console.log('isChangeSN=true')
  }

  // snChange(sn){
  //   console.log(sn)
  //   this.queryParams.filter['deviceIdStruct.serialNumber']=sn;
  //   this.editData.deviceId='';
  //   this.isChangeSN = false;
  //   console.log('isChangeSN=false')
  // }

  trimAttr(param) {
    if (JSON.parse(param.filter).hasOwnProperty("deviceIdStruct.serialNumber")) {
      let _sn = JSON.parse(param.filter)["deviceIdStruct.serialNumber"].trim()
      let _filter = {
        ...JSON.parse(param.filter),
        "deviceIdStruct.serialNumber": _sn
      }
      param.filter = JSON.stringify(_filter)
    }
    return param
  }

  filterInputChange(text: string) {
    this.filterChanged.next(text);
  }

  buttonDisabled() {
    if (!this.editData.fileTarget) {
      return true
    }
    switch (this.targetFileType) {
      case 'Default':
        if (this.editData.fileTarget == 'Default') {
          return !this.editData.fileType || !this.editData.url
        }
        if (this.editData.fileTarget == 'Product') {
          return !this.editData.targetProductName || !this.editData.productId || (!this.editData.instancePath && this.selectedProtocol == 'usp') || (!this.editData.fileType && this.selectedProtocol != 'usp') || !this.editData.url
        }
        if (this.editData.fileTarget == 'Device') {
          return !this.editData.serial || !this.editData.deviceId || (!this.editData.instancePath && this.selectedProtocol == 'usp') || (!this.editData.fileType && this.selectedProtocol != 'usp') || !this.editData.url
        }

        break;
      case 'Product':
        return !this.editData.targetProductName || !this.editData.productId || (!this.editData.instancePath && this.selectedProtocol == 'usp') || (!this.editData.fileType && this.selectedProtocol != 'usp') || !this.editData.url
      case 'Device':
        return !this.editData.serial || !this.editData.deviceId || (!this.editData.instancePath && this.selectedProtocol == 'usp') || (!this.editData.fileType && this.selectedProtocol != 'usp') || !this.editData.url

      default:
        return (!this.editData.instancePath && this.selectedProtocol == 'usp') || (!this.editData.fileType && this.selectedProtocol != 'usp') || !this.editData.url
    }
  }

  checkParams(obj) {
    this.formError = {}
    let errorMsg: string;
    this.formError["URL"] = this.st.check("isURL", obj.url)
    this.formError["User Name"] = obj.username?.length > 0 ? this.st.check("acs_username", obj.username) : null
    this.formError["User Password"] = obj.password?.length > 0 ? this.st.check("isPasswd", obj.password) : null
    this.formError["Target File Name"] = obj.targetName?.length > 0 ? this.st.check("fileName", obj.targetName) : null
    this.formError["Description"] = obj.description?.length > 0 ? this.st.check("fileDescription", obj.description) : null
    for (let i in this.formError) {
      if (this.formError[i]) {
        errorMsg = `${i}:${this.formError[i]}`
        break
      }
    }
    if (!!errorMsg) {
      this.loading = false
      this._toastrUtilsService.showWarningMessage(this.FAIL, errorMsg);
    }
    return !!errorMsg
  }


  saveEdit() {
    this.loading = true
    switch (this.page) {
      case 'file':
        let defaultParam: any = {
          source: this.editData.fileTarget,
          url: this.editData.url,
          username: this.editData.username,
          password: this.editData.password,
          fileSize: this.editData.fileSize,
          description: this.editData.fileDescription,
          targetName: this.editData.targetName,
          vendorConfigContent: this.editData.vendorConfigContent,
        }
        if (this.editData.fileTarget == 'Product') {
          defaultParam.targetProductName = this.editData.targetProductName
          defaultParam.targetProductId = this.editData.productId
        }

        if (this.editData.fileTarget == 'Device') {
          defaultParam.targetDeviceSn = this.editData.serial
          defaultParam.targetDeviceId = this.editData.deviceId
        }

        if (this.selectedProtocol == 'usp') {
          defaultParam.instancePath = this.editData.instancePath
        } else {
          defaultParam.fileType = this.editData.fileType
        }
        if (this.checkParams(defaultParam)) {
          return false
        }
        if (this.editData.username === '********') delete defaultParam.username;
        if (this.editData.password === '********') delete defaultParam.password;
        if (this.isEditModal == 'Add') {
          this._filesService.createSingleFile(defaultParam).then(res => {
            this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDSUCC);
            this.modalConfig.dismiss('Cross click')
          }).catch(err => {
            this._toastrUtilsService.showErrorMessage(this.FAIL, err.error);
          }).finally(() => {
            this.loading = false
            this.transFormDataEvt.emit('done')
          })
        } else {
          defaultParam.id = this.editData.id
          this._filesService.updateSingleFile(defaultParam).then(res => {
            this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.UPDATESUCC);
            this.modalConfig.dismiss('Cross click')
          }).catch(err => {
            this._toastrUtilsService.showErrorMessage(this.FAIL, err.error);
          }).finally(() => {
            this.loading = false
            this.transFormDataEvt.emit(this.DONE)
          })
        }
        break;
      case 'product':
        let productParam: any = {
          targetProductName: this.editData.targetProductName,
          targetProductId: this.editData.productId,
          source: 'Product',
          url: this.editData.url,
          username: this.editData.username,
          password: this.editData.password,
          fileSize: this.editData.fileSize,
          description: this.editData.fileDescription,
          targetName: this.editData.targetName
        }
        if (this.selectedProtocol == 'usp') {
          productParam.instancePath = this.editData.instancePath
        } else {
          productParam.fileType = this.editData.fileType
        }

        if (this.checkParams(productParam)) {
          return false
        }
        if (this.editData.username === '********') delete productParam.username;
        if (this.editData.password === '********') delete productParam.password;

        this._filesService.createSingleFile(productParam).then(res => {
          this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDSUCC);
          this.modalConfig.dismiss('Cross click')
        }).catch(err => {
          this._toastrUtilsService.showErrorMessage(this.FAIL, err.error);
        }).finally(() => {
          this.transFormDataEvt.emit(this.DONE)
          this.router.navigate(['/provisioning/files'], { skipLocationChange: true });
          //跳转到file
        })
        break;
      case 'device':
        let deviceParam: any = {
          targetDeviceSn: this.editData.serial,
          targetDeviceId: this.editData.deviceId,
          source: 'Device',
          url: this.editData.url,
          username: this.editData.username,
          password: this.editData.password,
          fileSize: this.editData.fileSize,
          description: this.editData.fileDescription,
          targetName: this.editData.targetName
        }

        if (this.selectedProtocol == 'usp') {
          deviceParam.instancePath = this.editData.instancePath
        } else {
          deviceParam.fileType = this.editData.fileType
        }

        if (this.checkParams(deviceParam)) {
          return false
        }
        if (this.editData.username === '********') delete deviceParam.username;
        if (this.editData.password === '********') delete deviceParam.password;
        this._filesService.createSingleFile(deviceParam).then(res => {
          this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDSUCC);
          this.modalConfig.dismiss('Cross click')
        }).catch(err => {
          this._toastrUtilsService.showErrorMessage(this.FAIL, err.error);
        }).finally(() => {
          this.transFormDataEvt.emit(this.DONE)
          this.router.navigate(['/provisioning/files'], { skipLocationChange: true });
          //跳转到file
        })
        break;
    }

  }

  cloneParams(queryParams) {
    let cloneQueryParamsClone
    cloneQueryParamsClone = cloneDeep(queryParams)
    for (let key in cloneQueryParamsClone.filter) {
      if (cloneQueryParamsClone.filter[key] == '') {
        delete cloneQueryParamsClone.filter[key]
      }
    }
    cloneQueryParamsClone.filter = JSON.stringify(cloneQueryParamsClone.filter)
    cloneQueryParamsClone.page = cloneQueryParamsClone.page - 1
    // console.log('queryParamsClone', cloneQueryParamsClone)
    return cloneQueryParamsClone
  }

  onFooterPage(e) {
    console.log(e)
    this.queryParams.page = e.page
    // console.log(this.queryParams)
    this.fuzzySearch(this.cloneParams(this.queryParams))
  }



  public jsonContent: any
  openFileInput() {
    // this.modalImConfig = modalIm
    document.getElementById("deviceImportFile")['value'] = '';
    document.getElementById("deviceImportFile").click();
  }

  onFileChange(event) {
    const reader = new FileReader();

    if (event.target.files && event.target.files.length) {
      const file = event.target.files[0];

      // Check if the file type is JSON
      if (file.type === 'application/json') {
        reader.readAsText(file);
        reader.onload = () => {
          this.jsonContent = JSON.parse(reader.result as string);
          this.cdr.detectChanges()
          // console.log(this.jsonContent)
        };
      } else {
        // Handle error: File type is not JSON
        this._toastrUtilsService.showWarningMessage('Please upload a JSON file.', '')
      }
    }
  }

  downloadJson() {
    // 將 JSON 物件轉換為字串
    const jsonStr = JSON.stringify(this.applyJsonContent, null, 2);

    // 創建一個 Blob 物件，指定 MIME 類型為 JSON
    const blob = new Blob([jsonStr], { type: 'application/json' });

    // 創建一個指向 Blob 的 URL
    const url = window.URL.createObjectURL(blob);

    // 動態創建一個 <a> 標籤
    const a = document.createElement('a');
    a.href = url;
    a.download = this.applyJsonContent.confName;

    // 模擬點擊該 <a> 標籤
    a.click();

    // 釋放 URL 物件
    window.URL.revokeObjectURL(url);
  }

  public applyJsonContent: any
  loadJsonContent(event) {
    // console.log(event)
    this.applyJsonContent = event
  }

  apply() {
    // console.log(this.applyJsonContent)
    this.editData.vendorConfigContent = this.applyJsonContent
    // console.log(this.editData)
    this.modalRef.dismiss();
  }

  public confVersion: any
  modalOpenWiFiConfiguration(modal) {
    this.jsonContent = this.editData.vendorConfigContent ? this.editData.vendorConfigContent : undefined
    this.confVersion = this.editData.url.split('/').pop()?.split('-').pop();
    this.modalRef = this.modalService.open(modal, {
      backdrop: 'static',
      size: 'lg',
      modalDialogClass: 'modal-eighty',
      scrollable: true
    });
  }


}
