<div class="content-wrapper container-fluid p-0">
  <div class="content-body">
    <div [ngStyle]="{'height.px': gridsterHeight}" *ngIf="widgets.length; else noPermission">
      <gridster [options]="gridOption" #gridRef>
        <ng-container *ngFor="let widget of widgets">
          <gridster-item
            *ngIf="widget.render && !widget.hidden"
            [item]="widget">
            <ng-template
              appGenWidget
              [widgetData]="widget"
              [gridRef]="gridRef">
            </ng-template>
          </gridster-item>
        </ng-container>
      </gridster>
    </div>
    <ng-template #noPermission>
      <span class="text-warning">{{ 'COMMON.NOPERMISSION' | translate }}</span>
    </ng-template>
  </div>
</div>
