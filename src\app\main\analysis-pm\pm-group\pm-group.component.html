<div class="content-wrapper container-fluid p-0">
  <div class="content-body">
    <div *ngIf="resNum>0">
      <div
        *blockUI="'chartBlock'"
        class="d-flex flex-wrap">
        <div
          *ngFor="let item of series;let i = index"
          class="mb-1"
          [ngClass]="series.length > 1 ? 'col-6' : 'col-12'">
          <core-card [actions]="'[]'">
            <h4 class="card-title w-100">
              <div class="d-flex justify-content-between align-items-center">
                <span>
                  {{mappingName(item[0].xname,selectPMClassify)? mappingName(item[0].xname,selectPMClassify):item[0].xname }}
                </span>
              </div>
            </h4>
            <div 
              *ngIf="series?.length && originalData?.length"
              class="card-body">
              <app-pm-line-chart
                [path]="'group'"
                [title]="mappingName(item[0].xname,selectPMClassify)? groupName+'_'+mappingName(item[0].xname,selectPMClassify):groupName+'_'+item[0].xname"
                [chartData]="item"
                [originalData]="getOriginalByXname(item[0]?.xname)"
                [chartBeginTime]="beginTime"
                [chartEndTime]="endTime">
              </app-pm-line-chart>
            </div>
          </core-card>
        </div>
      </div>
    </div>
    <div *ngIf="resNum==0">
      <div *blockUI="'chartBlock'">
        <ngb-alert
          [type]="'info'"
          [dismissible]="false">
          <div class="alert-body">No PM data.</div>
        </ngb-alert>
      </div>
    </div>
  </div>
</div>
