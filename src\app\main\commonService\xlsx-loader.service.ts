import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class XlsxLoaderService {
  private xlsxModule: any = null;
  private loadingPromise: Promise<any> | null = null;

  constructor() { }

  /**
   * 动态加载XLSX库
   * @returns Promise<any> XLSX模块
   */
  async loadXLSX(): Promise<any> {
    if (this.xlsxModule) {
      return this.xlsxModule;
    }
    if (this.loadingPromise) {
      return this.loadingPromise;
    }
    this.loadingPromise = import('xlsx/xlsx.js').then(module => {
      this.xlsxModule = module;
      this.loadingPromise = null;
      return module;
    }).catch(error => {
      this.loadingPromise = null;
      console.error('Failed to load XLSX module:', error);
      throw error;
    });

    return this.loadingPromise;
  }

  /**
   * 检查XLSX是否已加载
   * @returns boolean
   */
  isLoaded(): boolean {
    return this.xlsxModule !== null;
  }

  /**
   * 获取已加载的XLSX模块
   * @returns any XLSX模块
   */
  getXLSX(): any {
    if (!this.xlsxModule) {
      throw new Error('XLSX module is not loaded. Please call loadXLSX() first.');
    }
    return this.xlsxModule;
  }

  /**
   * 清除缓存的XLSX模块
   */
  clearCache(): void {
    this.xlsxModule = null;
    this.loadingPromise = null;
  }
}
