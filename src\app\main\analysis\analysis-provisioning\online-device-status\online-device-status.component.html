<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <div class="analysis-select">
    <ng-select
      (change)="changeFn($event)"
      [(ngModel)]="selectedProductName"
      class="column-select-filter ng-select-size-sm"
      [items]="productNameSelect"
      bindLabel="name"
      bindValue="value"
      [clearable]="false"
      labelForId="onlineDeviceProductName"
      required
      name="onlineDeviceProductName"
      placeholder="{{ 'COMMON.SELECTPRODUCTNAME' | translate }}">
      <ng-template
        ng-label-tmp
        let-item="item">
        <span class="ng-value-label">{{ item }}</span>
      </ng-template>
    </ng-select>
  </div>
  <div class="card card-transaction overflow-hidden">
    <div
      class="mh-1px "
      style="padding: 1.5rem;padding-top:0;cursor:default;">
      <section class="events-list-wrapper">
        <div class="card">
          <ngx-datatable
            #tableRowDetails
            appDatatableRecalculate
            [datatable]="tableRowDetails"
            [rows]="tableOnlineDatas"
            [rowHeight]="39"
            class="bootstrap core-bootstrap"
            [limit]="selectedOption"
            [columnMode]="ColumnMode.force"
            [headerHeight]="39"
            [scrollbarH]="true">
            <!-- Name -->
            <ngx-datatable-column
              [width]="150"
              name="{{ 'ANALYSIS.STATUSFORDEVICES' | translate }}"
              prop="name">
              <ng-template
                let-row="row"
                let-name="value"
                ngx-datatable-cell-template>
                <div>{{name?name:"-"}}</div>
              </ng-template>
            </ngx-datatable-column>
            <!-- Number -->
            <ngx-datatable-column
              [width]="50"
              name="{{ 'ANALYSIS.NUMBER' | translate }}"
              prop="count">
              <ng-template
                let-row="row"
                let-count="value"
                ngx-datatable-cell-template>
                <div>{{count?count:"0"}}</div>
              </ng-template>
            </ngx-datatable-column>
            <!-- rate -->
            <ngx-datatable-column
              [width]="50"
              name="{{ 'ANALYSIS.RATE' | translate }}"
              prop="rate">
              <ng-template
                let-row="row"
                let-rate="value"
                ngx-datatable-cell-template>
                <div>{{rate?rate:"-"}}</div>
              </ng-template>
            </ngx-datatable-column>
          </ngx-datatable>
        </div>
      </section>
    </div>
  </div>
</core-card>
