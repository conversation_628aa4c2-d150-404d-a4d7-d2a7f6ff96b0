import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, Input, OnInit } from '@angular/core';
import { DashboardService } from '../../dashboard.service';
import { timer, Subscription } from 'rxjs';

@UntilDestroy()
@Component({
  selector: 'app-dashboard-alarm-count',
  templateUrl: './dashboard-alarm-count.component.html',
  styleUrls: ['./dashboard-alarm-count.component.scss']
})
export class DashboardAlarmCountComponent implements OnInit {
  @Input() accessor: any;
  public loading: boolean;
  public data: any

  constructor(
    private _dashboardService: DashboardService,
  ) {
    this.getActAlarms()
  }
  /**
   * Core card event
   * @param event 
   */
  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        // this.getAlarms();
        this.getActAlarms()
        break;
    }
  }

  /**Get total alarms
   * 
   */
  getActAlarms() {
    this.loading = true;
    this._dashboardService.getActAlarms().pipe(untilDestroyed(this)).subscribe({
      next: res => {
        // console.log(res)
        this.data = res
        if (res) {
          this.loading = false
        }
      },
      error: error => {
        this.loading = false;
        console.log(error);
      },
      complete: () => {
        this.loading = false;
      }
    });
  }


  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getActAlarms();
    });
  }

  ngOnDestroy(): void {
  }

}
