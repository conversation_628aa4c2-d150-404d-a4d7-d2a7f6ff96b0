<!-- <a
  *ngIf="!loading"
  (click)="modalOpenBD(modalBD)"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center">
  <i
    data-feather="link"
    class="font-medium-1 mr-50 font-weight-bold">
  </i>
  {{ 'DEVICES.CONNECTIVITYTEST' | translate }}
</a>
<a
  *ngIf="loading"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center"
  [disabled]="true">
  <div
    style="margin-right: 7px;"
    class="spinner-border spinner-border-sm"
    role="status">
  </div>
  {{ 'DEVICES.CONNECTIVITYTEST' | translate }}
</a> -->
<button
  *ngIf="!loading"
  (click)="modalOpenBD(modalBD)"
  type="button"
  class="btn btn-primary btn-sm mr-50"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.SPEEDTEST' | translate }}"
  container="body"
  placement="bottom"
  [disabled]="noPermission"
  rippleEffect>
  <span [data-feather]="'fast-forward'"></span>
</button>
<button
  *ngIf="loading"
  type="button"
  class="btn btn-primary btn-sm mr-50"
  style="padding:0.486rem;"
  container="body"
  placement="bottom"
  ngbTooltip="{{ 'DEVICES.SPEEDTEST' | translate }}"
  rippleEffect
  [disabled]="true || noPermission">
  <div
    class="spinner-border spinner-border-sm"
    role="status">
  </div>
</button>
<!-- Modal -->
<ng-template
  #modalBD
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel4">
      {{ 'DEVICES.SPEEDTEST' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    style="padding: 0px !important;"
    tabindex="0"
    ngbAutofocus>
    <div>
      <div>
        <h1 style="text-align: center;margin-top: 10px;">
          <div
            *ngIf="speedTestSpinner"
            style="margin-right: 5px;"
            class="spinner-border"
            role="status">
          </div>
          <span *ngIf="uploadTitle">
            {{ 'DEVICES.ACTION.UPLOAD_SPEEDTEST' | translate }}
          </span>
          <span *ngIf="downloadTitle">
            {{ 'DEVICES.ACTION.DOWNLOAD_SPEEDTEST' | translate }}
          </span>
          <span *ngIf="completeTitle">
            {{ 'DEVICES.ACTION.COMPLETE_SPEEDTEST' | translate }}
          </span>
        </h1>
        <h4
          *ngIf="!completeTitle"
          style="text-align: center;margin-top: 20px;">
          {{ 'DEVICES.ACTION.SPEEDTEST_URL' | translate }} : {{url}}
        </h4>
      </div>
      <div
        *ngIf="!completeTitle"
        class="progress-wrapper"
        style="margin-bottom: 10px;">
        <ngb-progressbar
          showValue="true"
          type="primary"
          [striped]="true"
          [animated]="true"
          [value]="progress"
          style="margin-top: 20px;height: 25px;">
        </ngb-progressbar>
      </div>
      <div
        *ngIf="uploadDiagnosticSpeed || uploadState"
        class="d-flex justify-content-center align-items-center"
        style="height: 80px;width: 100%;">
        <div style="display: flex;align-items: center; justify-content: center; width: 50%;">
          <h3 *ngIf="uploadDiagnosticSpeed || !uploadState">
            <i
              data-feather="arrow-up-circle"
              class="mr-25 font-medium-5"></i>
            <span>{{uploadDiagnosticSpeed}}</span>
          </h3>
          <h4
            *ngIf="uploadState"
            style="text-align: center;">
            <i
              data-feather="arrow-up-circle"
              class="mr-25 font-medium-5"></i>
            <span>{{uploadState}}</span>
          </h4>
        </div>
        <div style=" display: flex;align-items: center; justify-content: center; width: 50%;">
          <h3 *ngIf="downloadDiagnosticSpeed">
            <i
              data-feather="arrow-down-circle"
              class="mr-25 font-medium-5"></i>
            <span>{{downloadDiagnosticSpeed}}</span>
          </h3>
          <h4
            *ngIf="downloadState"
            style="text-align: center;">
            <i
              data-feather="arrow-up-circle"
              class="mr-25 font-medium-5"></i>
            <span>{{downloadState}}</span>
          </h4>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button
        type="button"
        class="btn btn-primary"
        (click)="modal.close('Close click')">
        {{ 'COMMON.CLOSE' | translate }}
      </button>
    </div>
  </div>
</ng-template>
<!-- / Modal -->
