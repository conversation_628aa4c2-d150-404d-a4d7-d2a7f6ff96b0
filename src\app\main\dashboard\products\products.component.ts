import { Component, Input, OnInit, ViewChild,ChangeDetectorRef } from '@angular/core';
import { DashboardService } from '../dashboard.service';
import { timer, Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-products',
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss'],
})
export class ProductsComponent extends Unsubscribe implements OnInit {
  @ViewChild('dashboardProducts') dashboardProducts: any;
  @Input() accessor: any;
  public total = 0;
  public resData;
  public loading: boolean = false;
  public personalTheme;
  public isShowNoData = true

  constructor(
    private _dashboardService: DashboardService,
    private _route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
  ) {
    super();
    this.getProduct()
    this.resData = [];
    this.customSubscribe(_route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }
  /**
 * Core card event
 * @param event 
 */
  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getProduct()
        break;
      case 'download':
        this.triggerDownloadData()
        break;
      case 'maximize':
        this.triggerModal()
        break;
    }
  };
  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }



  getProduct() {
    this.total = 0
    this.loading = true;
    this.isShowNoData = false
    this._dashboardService.getProduct()
      .then(res => {
        // console.log(res)
        res = res.filter(item => {
          return item.cpeNumber != 0
        })
        this.resData = res.map(pd => {
          return { name: pd.name, value: pd.cpeNumber }
        })
        // console.log(this.resData)

        res.forEach(item => {
          this.total += item.cpeNumber || 0
        })

        this.isShowNoData = this.resData.length > 0 ? false : true
        this.cdr.detectChanges();
      })
      .catch((err) => {
        console.error(err)
        this.isShowNoData = true
      })
      .finally(() => this.loading = false)
  };


  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getProduct();
    });
  }

  ngOnDestroy(): void {
  }
}
