import { inject, Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot, CanActivateFn } from '@angular/router';
import { UserService } from 'app/auth/service/user.service';
import { StorageService } from 'app/main/commonService/storage.service';

@Injectable({ providedIn: 'root' })
class PermissionsService {
  /**
   *
   * @param {Router} _router
   */
  constructor(
    private _router: Router,
    private _userService: UserService,
    private _storageService: StorageService,
  ) { }


  falseToErrorPage(access) {
    if (!access) {
      this._router.navigate(['/pages/miscellaneous/error']);
    }
    return access
  }

  navigateToDevice(access) {
    if (!access) {
      this._router.navigate(['/devices']);
    } else {
      return true
    }
  }
  // canActivate
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const { animation } = route.data
    let systemEventsRouteAccess = this._userService.pageController("events", 'system')
    let deviceEventsRouteAccess = this._userService.pageController("events", 'device')
    let serverReportsRouteAccess = this._userService.pageController("reports", 'server')
    let scriptRouteAccess = this._userService.canActivateRoute("ScriptsComponent")
    let groupRouteAccess = this._userService.canActivateRoute("GroupListComponent")
    let productRouteAccess = this._userService.canActivateRoute("ProductProfilesComponent")
    let monitoringRouteAccess = this._userService.canActivateRoute("EventsNotificationComponent")
    let workflowRouteAccess = this._userService.canActivateRoute("WorkFlowsComponent")
    let userAccountRouteAccess = this._userService.canActivateRoute("UserAccountComponent")
    let advancedRouteAccess = this._userService.canActivateRoute("AdvancedComponent") || this._userService.canActivateRoute("XMPPComponent")
    let statisticsRouteAccess = this._userService.canActivateRoute("StatisticsComponent")
    let groupLogAccess = this._userService.pageController("log", 'operation')
    let preferenceAccess = this._userService.canActivateRoute("log")
    let rolesRouteAccess = this._userService.canActivateRoute("AclRoleComponent")
    // console.log(animation)
    // if (localStorage.hasOwnProperty('currentUser')) {
    if (this._storageService.hasOwnProperty('currentUser')) {
      if(animation != 'LicenseComponent' && (!this._userService.validForDate || this._userService.Token != 1)){
        return this.falseToErrorPage(this._userService.validForDate && this._userService.Token === 1)
      }
      // authorised so return true
      if (animation === 'dashboard') {
        return this.navigateToDevice(statisticsRouteAccess)
      }
      if (animation === 'EventsDeviceComponent') {
        return this.falseToErrorPage(deviceEventsRouteAccess)
      }
      if (animation === 'SystemEventsComponent') {
        return this.falseToErrorPage(systemEventsRouteAccess)
      }
      if (animation === 'SystemReportsComponent') {
        return this.falseToErrorPage(serverReportsRouteAccess)
      }
      if (animation === 'ScriptsComponent') {
        return this.falseToErrorPage(scriptRouteAccess)
      }
      if (animation === 'GroupListComponent') {
        return this.falseToErrorPage(groupRouteAccess)
      }
      if (animation === 'ProductProfilesComponent') {
        return this.falseToErrorPage(productRouteAccess)
      }
      if (animation === 'WorkFlowsComponent') {
        return this.falseToErrorPage(workflowRouteAccess)
      }
      if (animation === 'UserAccountComponent') {
        return this.falseToErrorPage(userAccountRouteAccess)
      }
      if (animation === 'AclRoleComponent') {
        return this.falseToErrorPage(rolesRouteAccess)
      }
      if (animation === 'EventsNotificationComponent') {
        return this.falseToErrorPage(monitoringRouteAccess)
      }
      if (animation === 'DeviceAdvanceComponent') {
        return this.falseToErrorPage(advancedRouteAccess)
      }
      if (animation === 'AnalysisDeviceComponent' || animation === 'AnalysisSystemComponent' || animation === 'AnalysisPmComponent' || animation === 'AnalysisRefurbishmentComponent') {
        return this.falseToErrorPage(statisticsRouteAccess)
      }
      if (animation === 'GroupDataLogsComponent') {
        return this.falseToErrorPage(groupLogAccess)
      }
      if (animation === 'SystemPreferenceComponent' || animation === 'DeviceDataLogsComponent') {
        return this.falseToErrorPage(preferenceAccess)
      }
      return true
    } else {
      // not logged in so redirect to login page with the return url
      this._router.navigate(['/pages/authentication/login-v2']);
      return false;
    }
  }
}

export const AuthGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean => {
  return inject(PermissionsService).canActivate(route, state);
}
