import { Component, Input, OnInit, ViewChild,ChangeDetectorRef } from '@angular/core';
import { DashboardService } from '../dashboard.service';
import { timer, Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-online-products',
  templateUrl: './online-products.component.html',
  styleUrls: ['./online-products.component.scss']
})
export class OnlineProductsComponent extends Unsubscribe implements OnInit {
  @ViewChild('dashboardOnlineProducts') dashboardOnlineProducts: any;
  @Input() accessor: any;
  public total = 0;
  public resData;
  public loading: boolean = false;
  public personalTheme;
  public isShowNoData = true

  constructor(
    private _dashboardService: DashboardService,
    private _route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
  ) {
    super();
    this.getOnlineProduct()
    this.resData = [];
    this.customSubscribe(_route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getOnlineProduct()
        break;
      case 'download':
        this.triggerDownloadData()
        break;
      case 'maximize':
        this.triggerModal()
        break;
    }
  };
  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }

  getOnlineProduct() {
    this.total = 0
    this.loading = true;
    this.isShowNoData = false
    this.resData = []
    this._dashboardService.getOnlineProduct()
      .then(res => {
        // console.log(res)

        for (const key in res) {
          if (res.hasOwnProperty(key)) {
            this.resData.push({ "name": key, "value": res[key] });
          }
        }
        // 按照 value 的值大到小排列
        this.resData.sort((a, b) => b.value - a.value);
        // console.log(this.resData)

        this.resData.forEach(item => {
          this.total += item.value || 0
        })
        // console.log(this.total)

        this.isShowNoData = this.resData.length > 0 ? false : true
        this.cdr.detectChanges();

      })
      .catch((err) => {
        console.error(err)
        this.isShowNoData = true
      })
      .finally(() => this.loading = false)
  };

  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getOnlineProduct();
    });
  }

  ngOnDestroy(): void {

  }
}