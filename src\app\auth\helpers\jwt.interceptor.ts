import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from "rxjs/operators";
import { environment } from 'environments/environment';
import { AuthenticationService } from 'app/auth/service';
import { StorageService } from 'app/main/commonService/storage.service';
import * as xss from 'xss';
// 创建一个FilterXSS实例，并传入配置
@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  private specialFilter: any;
  /**
   *
   * @param {AuthenticationService} _authenticationService
   */
  constructor(
    private _authenticationService: AuthenticationService,
    private _storageService: StorageService
    // private xss: xss
  ) {
    this.specialFilter = new xss.FilterXSS({
      onTag: (tag, html) => {
        // 对于非script标签，只清理属性，不转义HTML实体
        if (tag !== 'script') {
          return html;
        } else {
          // 对于script标签，遵循默认行为，通常会移除整个标签以防止XSS攻击
          return '';
        }
      },
      escapeHtml(html) {
        const isXssPossible = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(html) && /<|>/gi.test(html);
        if (isXssPossible) {
          return html.replace(/</g, "&lt;").replace(/>/g, "&gt;");
        } else {
          return html
        }
      }
    });
  }

  isNotJsonOrWhiteList(url) {
    // 不需要用xss库过滤response的nbi
    let jsonRegex = /\.json$/;
    let troubleshootingRegx = /remoteTroubleshooting/;
    let sessionLogRegx = /sessionLog/;
    let exportRegx = /.*(export|Export).*/g;
    let loginRegx = /verify/;
    return !jsonRegex.test(url) && !troubleshootingRegx.test(url) && !sessionLogRegx.test(url) && !exportRegx.test(url) && !loginRegx.test(url);
  }

  isPassedNbi(url) {
    // 不需要用xss库过滤request的nbi
    let parameterRegex = /parameter/;
    let updateAttributeRegex = /updateAttribute/
    let serverPreferenceRegx = /serverPreference/;
    return !parameterRegex.test(url) && !updateAttributeRegex.test(url) && !serverPreferenceRegx.test(url);
  }

  sanitizeHtml(unsafeHtml: string): string {
    return this.specialFilter.process(unsafeHtml);
  }

  /**
   * Add auth header with jwt if user is logged in and request is to api url
   * @param request
   * @param next
   */
  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // const currentUser = this._authenticationService.currentUserValue;
    const currentUser = this._storageService.get('currentUser');
    const sessionId = this._storageService.getCookie('sessionId');
    const isLoggedIn = currentUser && currentUser.token;
    // const isApiUrl = request.url.startsWith(environment.acsLabUrl);
    const isApiUrl = !request.url.includes(environment.fakeFolder);
    if (isLoggedIn && isApiUrl && request.url != "spec.json" && sessionId) {
      request = request.clone({
        setHeaders: {
          // Authorization: `Bearer ${currentUser.token}`
          Authorization: `Bearer ${sessionId}`
        }
      });
      if (request.method == "GET") {
        request = request.clone({ url: this.sanitizeHtml(request.url) });
      }

      if (request.method == "POST" || request.method == "PUT") {
        if (request.body && this.isPassedNbi(request.url)) {
          request = request.clone({ body: JSON.parse(this.sanitizeHtml(JSON.stringify(request.body))) });
        }
      }
    }
    return next.handle(request)
      .pipe(map(event => {
        if (event instanceof HttpResponse) {
          let originalResponse = event as any;
          if (originalResponse.body && originalResponse.status === 200 && this.isNotJsonOrWhiteList(originalResponse.url)) {
            let modifiedBody: any = originalResponse.body
            var data = JSON.parse(this.sanitizeHtml(JSON.stringify(modifiedBody)))
            const newResponse = new HttpResponse({
              headers: originalResponse.headers,
              status: originalResponse.status,
              body: data,
              url: originalResponse.url || ''
            });
            return newResponse;
          }

          if (originalResponse.body && originalResponse.status === 201 && this.isNotJsonOrWhiteList(originalResponse.url)) {
            let modifiedBody: any = originalResponse.body
            var data = JSON.parse(this.sanitizeHtml(JSON.stringify(modifiedBody)))
            const newResponse = new HttpResponse({
              headers: originalResponse.headers,
              status: originalResponse.status,
              body: `${originalResponse.status}`,
              url: originalResponse.url || ''
            });
            return newResponse;
          }
          return event;
        } else {
          return event;
        }
      }))
  }
}
