import { TestBed } from '@angular/core/testing';
import { CanMatchFn } from '@angular/router';

import { licenseGuard } from './license.guard';

describe('licenseGuard', () => {
  const executeGuard: CanMatchFn = (...guardParameters) => 
      TestBed.runInInjectionContext(() => licenseGuard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
