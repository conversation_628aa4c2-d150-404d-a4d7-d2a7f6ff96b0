import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardCoverageMapComponent } from './dashboard-coverage-map.component';

describe('DashboardCoverageMapComponent', () => {
  let component: DashboardCoverageMapComponent;
  let fixture: ComponentFixture<DashboardCoverageMapComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DashboardCoverageMapComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardCoverageMapComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
