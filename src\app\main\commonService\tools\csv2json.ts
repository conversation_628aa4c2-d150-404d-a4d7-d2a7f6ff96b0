function CSVToArray(strData, strDelimiter = ",") {
    var objPattern = new RegExp((
        "(\\" + strDelimiter + "|\\r?\\n|\\r|^)" +
        "(?:\"([^\"]*(?:\"\"[^\"]*)*)\"|" +
        "([^\"\\" + strDelimiter + "\\r\\n]*))"), "gi");
    var arrData = [
        []
    ];
    var arrMatches = null;
    while (arrMatches = objPattern.exec(strData)) {
        var strMatchedDelimiter = arrMatches[1];
        var lastValue = objPattern.lastIndex - arrMatches.index > 0;
        if (lastValue) {
            if (strMatchedDelimiter.length && (strMatchedDelimiter != strDelimiter)) {
                arrData.push([]);
            }
            if (arrMatches[2]) {
                var strMatchedValue = arrMatches[2].replace(
                    new RegExp("\"\"", "g"), "\"");
            } else {
                var strMatchedValue = arrMatches[3];
            }
            arrData[arrData.length - 1].push(strMatchedValue.trim());
        }
    }
    return (arrData);
}

function csv2jsonArray(csv, one = false): object[] {
    var commaArray = CSVToArray(csv);
    var array;
    if (commaArray[0] && commaArray[0].length >= 2) {
        array = commaArray;
    } else {
        array = CSVToArray(csv, ";");
    }
    let res = [];
    let titles = array[0];
    let len = one ? 2 : array.length
    for (let i = 1; i < len; i++) {
        let item = array[i];
        if (item[0] || one) {
            let obj = {};
            titles.forEach((key, index) => {
                obj[key] = item[index]
            })
            res.push(obj)
        }
    }
    return res
}

export { csv2jsonArray, CSVToArray }