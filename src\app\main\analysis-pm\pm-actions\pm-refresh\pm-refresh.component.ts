import { Component, OnInit, Input, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { AnalysisPmService } from 'app/main/analysis-pm/analysis-pm.service';
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-pm-refresh',
  templateUrl: './pm-refresh.component.html',
  styleUrls: ['./pm-refresh.component.scss']
})
export class PmRefreshComponent implements OnInit {
  public blockUIStatus = false;
  @Output() refreshEvt = new EventEmitter<string>();

  @Input() mode: string;
  @Input() row: any;
  @Input() selectedMetric?: string;
  @Input() selectedMetrics
  @Input() noPermission: any;
  constructor(
    private _analysisPmService: AnalysisPmService,
    private route: ActivatedRoute,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService,
    private modalService: NgbModal,
  ) { }

  public metricNme: any;
  public ALL = this.translateService.instant('PM.ALL');
  public METRICRULE = this.translateService.instant('PM.METRICRULE');
  public PERFORMANCEREPORT = this.translateService.instant('PM.PERFORMANCEREPORT');
  public CONFIRMREFRESH = this.translateService.instant('PM.CONFIRM_REFRESH');
  public REFRESH = this.translateService.instant('PM.REFRESH');
  public REFRESHSUCCESS = this.translateService.instant('PM.REFRESHSUCCESS');
  public REFRESHFAIL = this.translateService.instant('PM.REFRESHFAIL');
  public DO_REFRESHSELECT = this.translateService.instant('PM.DO_REFRESHSELECT');
  public DO_REFRESH = this.translateService.instant('PM.DO_REFRESH');
  public PleaseSelect = this.translateService.instant('PM.PLESESELECT');

  change(message: string) {
    this.refreshEvt.emit(message);
  }

  public today = new Date();
  public progress = 0;
  refresh(modal) {
    // console.log(this.mode)
    let progress = 0
    let endTime = new Date(this.today.setHours(23, 59, 59)).toISOString()
    let oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
    let oneWeek = 7 * oneDay; // 一周的毫秒数
    let oneMonth = 30 * oneDay; // 一个月的毫秒数
    switch (this.mode) {
      case 'singleMetric':
        let beginTime
        let timeDiff = new Date(this.row.endTime).getTime() - new Date(this.row.beginTime).getTime(); // 获取时间间隔
        if (timeDiff <= oneDay) {
          // console.log("One Day");
          beginTime = this.returnDuration('day')
        } else if (timeDiff <= oneWeek) {
          // console.log("One Week");
          beginTime = this.returnDuration('week')
        } else {
          // console.log("One Month");
          beginTime = this.returnDuration('month')
        }
        let lastQueryRes;
        let searchResultRes;
        this._analysisPmService.viewMetricRule(this.row.id).then(res => {
          // console.log(res)
          searchResultRes = res
          lastQueryRes = JSON.parse(searchResultRes)
          // console.log(lastQueryRes)
          let param = {
            name: this.row.name,
            metricParameters: this.row.metricParameters,
            operator: this.row.operator,
            serialNumber: this.row.serialNumber,
            groupId: this.row.groupId,
            beginTime: beginTime,
            endTime: endTime,
            description: this.row.description,
            lastQueryRes: lastQueryRes
          }
          // console.log(param)
          // Swal.fire({
          //   title: this.CONFIRMREFRESH,
          //   text: this.REFRESH + " " + this.row.name + "?",
          //   showCancelButton: true,
          //   confirmButtonText: this.translateService.instant('COMMON.OK'),
          //   cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          //   customClass: {
          //     confirmButton: 'btn btn-primary',
          //     cancelButton: 'btn btn-danger ml-1'
          //   }
          // }).then((result) => {
          //   if (result.value) {
              this._analysisPmService.updateMetric(this.row.id, param).then(res => {
                // console.log(res)
                this.change('success')
                this._toastrUtilsService.showSuccessMessage(this.REFRESHSUCCESS, `Name: ${this.row.name}`)
                })
          //   }
          // });
        })

        break;
      case 'multiMetrics':
        // console.log(this.selectedMetrics)
        // Swal.fire({
        //   title: this.CONFIRMREFRESH,
        //   text: this.DO_REFRESHSELECT + this.PERFORMANCEREPORT + '?',
        //   showCancelButton: true,
        //   confirmButtonText: this.translateService.instant('COMMON.OK'),
        //   cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
        //   customClass: {
        //     confirmButton: 'btn btn-primary',
        //     cancelButton: 'btn btn-danger ml-1'
        //   }
        // }).then((result) => {
          // if (result.value) {
            let multiData = this.selectedMetrics
            this.modalService.open(modal, {
              centered: true,
              size: 'lg'
            });
            for (let i = 0; i < multiData.length; i++) {
              let beginTime
              progress = Math.floor((i + 1) / multiData.length * 100);
              setTimeout(() => {
                this.updateProgressBar(progress);
              }, 300);
              let timeDiff = new Date(multiData[i].endTime).getTime() - new Date(multiData[i].beginTime).getTime(); // 获取时间间隔
              if (timeDiff <= oneDay) {
                // console.log("One Day");
                beginTime = this.returnDuration('day')
              } else if (timeDiff <= oneWeek) {
                // console.log("One Week");
                beginTime = this.returnDuration('week')
              } else {
                // console.log("One Month");
                beginTime = this.returnDuration('month')
              }
              this._analysisPmService.viewMetricRule(multiData[i].id).then(res => {
                let lastQueryRes;
                let searchResultRes;
                // console.log(res)
                searchResultRes = res
                lastQueryRes = JSON.parse(searchResultRes)
                // console.log(lastQueryRes)
                let param = {
                  name: multiData[i].name,
                  metricParameters: multiData[i].metricParameters,
                  operator: multiData[i].operator,
                  serialNumber: multiData[i].serialNumber,
                  groupId: multiData[i].groupId,
                  beginTime: beginTime,
                  endTime: endTime,
                  description: multiData[i].description,
                  lastQueryRes: lastQueryRes
                }
                this._analysisPmService.updateMetric(multiData[i].id, param).then(res => {
                  // console.log(res)
                  // 在迭代的最后一次加上成功的消息
                  if (i === multiData.length - 1) {
                    this.change('success')
                    this._toastrUtilsService.showSuccessMessage(this.REFRESHSUCCESS, null);
                  }
                })
              })
            }
          // }
        // });
        break;
      case 'allMetrics':
        // Swal.fire({
        //   title: this.CONFIRMREFRESH,
        //   text: this.DO_REFRESH + this.ALL + " " + this.PERFORMANCEREPORT + " " + '?',
        //   showCancelButton: true,
        //   confirmButtonText: this.translateService.instant('COMMON.OK'),
        //   cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
        //   customClass: {
        //     confirmButton: 'btn btn-primary',
        //     cancelButton: 'btn btn-danger ml-1'
        //   }
        // }).then((result) => {
          // console.log(this.selectedMetrics)
          // if (result.value) {
            let data = this.selectedMetrics
            this.modalService.open(modal, {
              centered: true,
              size: 'lg'
            });
            for (let i = 0; i < data.length; i++) {
              let beginTime
              progress = Math.floor((i + 1) / data.length * 100);
              setTimeout(() => {
                this.updateProgressBar(progress);
              }, 300);
              let timeDiff = new Date(data[i].endTime).getTime() - new Date(data[i].beginTime).getTime(); // 获取时间间隔
              if (timeDiff <= oneDay) {
                // console.log("One Day");
                beginTime = this.returnDuration('day')
              } else if (timeDiff <= oneWeek) {
                // console.log("One Week");
                beginTime = this.returnDuration('week')
              } else {
                // console.log("One Month");
                beginTime = this.returnDuration('month')
              }
              this._analysisPmService.viewMetricRule(data[i].id).then(res => {
                let lastQueryRes;
                let searchResultRes;
                // console.log(res)
                searchResultRes = res
                lastQueryRes = JSON.parse(searchResultRes)
                // console.log(lastQueryRes)
                let param = {
                  name: data[i].name,
                  metricParameters: data[i].metricParameters,
                  operator: data[i].operator,
                  serialNumber: data[i].serialNumber,
                  groupId: data[i].groupId,
                  beginTime: beginTime,
                  endTime: endTime,
                  description: data[i].description,
                  lastQueryRes: lastQueryRes
                }
                this._analysisPmService.updateMetric(data[i].id, param).then(res => {
                  // console.log(res)
                  // 在迭代的最后一次加上成功的消息
                  if (i === data.length - 1) {
                    this.change('success')
                    this._toastrUtilsService.showSuccessMessage(this.REFRESHSUCCESS, null);
                  }
                })
              })
            }
          // }
        // });
        break;
    }
  }

  returnDuration(value) {
    let beginTime
    if (value == 'month') {
      var oneMonthAgo = new Date(this.today);
      oneMonthAgo.setMonth(this.today.getMonth() - 1);
      // 如果当前日期的天数大于一个月前的日期的天数，
      // 则需要调整日期，确保在同一月份内
      if (this.today.getDate() < oneMonthAgo.getDate()) {
        oneMonthAgo.setDate(this.today.getDate());
      }
      // console.log(oneMonthAgo);
      oneMonthAgo.setHours(0, 0, 0, 0)
      beginTime = oneMonthAgo.toISOString()
    }
    else if (value == 'week') {
      // 计算七天前的日期
      let sevenDaysAgo = new Date(this.today);
      sevenDaysAgo.setDate(this.today.getDate() - 6);
      sevenDaysAgo.setHours(0, 0, 0, 0)
      // console.log(sevenDaysAgo);
      beginTime = sevenDaysAgo.toISOString()
    }
    else {
      beginTime = new Date(this.today.setHours(0, 0, 0, 0)).toISOString()
    }

    return beginTime
  }

  updateProgressBar(progress) {
    this.progress = progress
    // console.log(`Progress: ${this.progress}%`);
    if (this.progress == 100) {
      setTimeout(() => {
        this.modalService.dismissAll()
      }, 1000);
    }
  }


  ngOnInit(): void {
  }

}
