import { Until<PERSON><PERSON><PERSON>, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input } from '@angular/core';
import { AnalysisSystemService } from '../analysis-system.service'
import moment from 'moment'
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import{CalculatorWidthService} from 'app/main/commonService/calculator-width.service';


@UntilDestroy()
@Component({
  selector: 'app-data-base-status',
  templateUrl: './data-base-status.component.html',
  styleUrls: ['./data-base-status.component.scss']
})
export class DataBaseStatusComponent extends Unsubscribe implements OnInit {
  @Input() accessor: any;
  public blockUIStatus = this._analysisSystemService.serverStatus;
  public dbInfoItems:any = []
  public componentWidth: number = 0;
  public inservice = false;

  constructor(
    private _analysisSystemService: AnalysisSystemService,
    private _gridSystemService: GridSystemService,
    private _calculatorWidthService: CalculatorWidthService,
  
  ) {
    super();
  }

  public dbInfoNameMapping = {
    'version': 'Version',
    'currentConnects': 'Current Connects',
    'availableConnects': 'Available Connects',
    'bytesIn': 'BytesIn',
    'bytesOut': 'BytesOut',
    'uptime': 'Uptime'
  }


  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getDBInfoRes();
        break;
    }
  };

  getDBInfoRes(){
    this._analysisSystemService.onServerStatusChanged.pipe(untilDestroyed(this)).subscribe(res=>{
      // console.log(res)
      if(!res["mongo"]){
        this.dbInfoItems = Object.keys(this.dbInfoNameMapping).map(item=>{
          return {
            "name": item, "reportedValue": ''
          }
        })
      } else {
        this.dbInfoItems = []
        for(let i in res["mongo"]){
          this.dbInfoItems.push({"name": i, "reportedValue": res["mongo"][i]})
        }
        this.inservice=true
      }
      this._analysisSystemService.onBlockUIStatusChanged.next(false)
    })
  }

  getWidth() {
    return this._calculatorWidthService.calculateWidth(this.componentWidth, this.dbInfoItems.length);
  }

  ngOnInit(): void {
    this.getDBInfoRes();

  }

  ngAfterViewInit(): void {
    this.customSubscribe(this._gridSystemService.onGridsterItemComponentInfoChanged, res => {
      this.componentWidth = res[this.accessor.componentId]?.['width'] || 0
    })
    
  }

}
