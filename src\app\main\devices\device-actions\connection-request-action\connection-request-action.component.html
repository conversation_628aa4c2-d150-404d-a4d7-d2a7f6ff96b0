<a
  *ngIf="!iconButton && !loading"
  (click)="connectionRequest();"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center"
  [disabled]="noPermission">
  <i
    data-feather="repeat"
    class="font-medium-1 mr-50 font-weight-bold">
  </i>
  {{ 'DEVICES.LIVEUPDATE' | translate }}
</a>
<a
  *ngIf="!iconButton && loading"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center"
  [disabled]="true">
  <div
    style="margin-right: 7px;"
    class="spinner-border spinner-border-sm"
    role="status">
  </div>
  {{ 'DEVICES.LIVEUPDATE' | translate }}
</a>
<button
  *ngIf="iconButton && !loading"
  (click)="connectionRequest();"
  type="button"
  [disabled]="editMode||noPermission"
  class="btn btn-primary btn-sm mr-50"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.LIVEUPDATE' | translate }}"
  rippleEffect>
  <span [data-feather]="'repeat'"></span>
</button>
<button
  *ngIf="iconButton && loading"
  type="button"
  [disabled]="editMode||noPermission"
  class="btn btn-primary btn-sm mr-50"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.LIVEUPDATE' | translate }}"
  rippleEffect
  [disabled]="true">
  <div
    class="spinner-border spinner-border-sm"
    role="status">
  </div>
</button>
