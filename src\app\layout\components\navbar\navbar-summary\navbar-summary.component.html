<li
  ngbDropdown
  #menuClose="ngbDropdown"
  class="nav-item dropdown-notification mr-25">
  <button
    class="btn nav-link"
    ngbDropdownToggle
    container="body"
    placement="bottom"
    [disabled]="editMode"
    ngbTooltip="{{ 'PROVISIONING.GENERSUMMARYREPORT' | translate }}"
    id="navbarSummaryDropdown">
    <i
      class="ficon"
      data-feather="file-text"></i>
  </button>
  <ul
    id="dropdown-menu-summary"
    ngbDropdownMenu
    aria-labelledby="navbarSummaryDropdown"
    class="dropdown-menu dropdown-menu-media dropdown-menu-right">
    <!-- Summary header -->
    <li class="dropdown-menu-header">
      <div class="dropdown-header d-flex">
        <h4 class="summary-title mb-0 mr-auto">{{ 'PROVISIONING.SUMMARYREPORT' | translate }}</h4>
      </div>
    </li>
    <!-- Summary content -->
    <li
      id="summary-form-content"
      class="scrollable-container media-list"
      [perfectScrollbar]>
      <form action>
        <div class="card-body pt-1 pb-0">
          <div class="row">
            <div class="form-group col-6">
              <h6>{{ 'DEVICES.PROTOCOL' | translate }}</h6>
              <ng-select
                [clearable]="false"
                [searchable]="false"
                [closeOnSelect]="true"
                [items]="protocolOption"
                [(ngModel)]="curProtocol"
                (change)="changeProtocolForProduct($event)"
                bindLabel="name"
                bindValue="value"
                labelForId="modernType"
                #modernTypeRef="ngModel"
                required
                name="modernType"
                placeholder="{{ 'DEVICES.SELECTPROTOCOL' | translate }}">
                <ng-template
                  ng-label-tmp
                  let-item="item"
                  let-clear="clear">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span
                    class="ng-value-icon right"
                    (click)="clear(item)"
                    aria-hidden="true">
                    ×
                  </span>
                </ng-template>
              </ng-select>
            </div>
            <!-- Product -->
            <div class="form-group col-6">
              <h6>{{ 'PROVISIONING.PRODUCT' | translate }}</h6>
              <ng-select
                [items]="summaryReportProductNames"
                [multiple]="true"
                [closeOnSelect]="false"
                [searchable]="false"
                [clearable]="false"
                bindLabel="name"
                bindValue="name"
                name="product"
                (change)="changeNameForProduct($event)"
                placeholder="Select Product"
                [(ngModel)]="curProductName">
                <ng-template
                  ng-label-tmp
                  let-item="item"
                  let-clear="clear">
                  <span class="ng-value-label">{{item.name}}</span>
                  <span
                    class="ng-value-icon right"
                    (click)="clear(item)"
                    aria-hidden="true">
                    ×
                  </span>
                </ng-template>
              </ng-select>
            </div>
            <!-- Duration -->
            <div
              class="form-group col-12"
              [ngClass]="{'col-md-6':curDuration.key == 2}">
              <h6>{{ 'PROVISIONING.DURATION' | translate }}</h6>
              <ng-select
                class="w-100"
                [items]="timeRange"
                name="duration"
                bindLabel="value"
                [searchable]="false"
                [clearable]="false"
                [(ngModel)]="curDuration"
                (change)="changeDuration($event)"
                placeholder="Select Duration">
              </ng-select>
            </div>
            <!-- Date -->
            <div
              class="form-group col-md-6"
              [hidden]="curDuration.key != 2">
              <h6>{{ 'PROVISIONING.VALIDFROM' | translate }}</h6>
              <input
                class="form-control"
                placeholder="yyyy-mm-dd"
                (click)="basicDP.toggle()"
                name="time"
                [(ngModel)]="basicDPdata"
                ngbDatepicker
                #basicDP="ngbDatepicker"
                [maxDate]="maxDate">
            </div>
            <!-- Field -->
            <div class="form-group col-12">
              <div class="d-flex justify-content-between">
                <h6>{{ 'PRODUCTS.LABEL' | translate }}</h6>
                <div class="custom-control custom-checkbox custom-control-success">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    id="incluedeFields"
                    (change)="selectAllFields($event)"
                    [checked]="isSelectedAllFields">
                  <label
                    class="custom-control-label summary-form-label"
                    for="incluedeFields">
                    All Fields
                  </label>
                </div>
              </div>
              <div class="d-flex flex-wrap">
                <div
                  class="col-md-4 col-6 p-0"
                  *ngFor="let item of summaryReportFields;let i = index">
                  <div
                    class="custom-control custom-checkbox"
                    style="margin-bottom:0.15rem">
                    <input
                      type="checkbox"
                      class="custom-control-input"
                      id="customCheck{{i}}"
                      (change)="selectSingleFields($event,item)"
                      [checked]="item.include">
                    <label
                      class="custom-control-label summary-form-label"
                      for="customCheck{{i}}">
                      {{item.labelName}}
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <!-- Format -->
            <div class="form-group col-12">
              <h6>{{ 'PROVISIONING.FILETYPE' | translate }}</h6>
              <div class="d-flex justify-content-start">
                <div
                  class="custom-control custom-radio mr-1"
                  *ngFor="let item of formateMap;">
                  <input
                    type="radio"
                    id="customRadio{{item.key}}"
                    name="customRadio"
                    [value]="item.key"
                    (change)="selectedFormateChange($event)"
                    class="custom-control-input"
                    [checked]="item.key == selectedFormat">
                  <label
                    class="custom-control-label summary-form-label"
                    for="customRadio{{item.key}}">
                    {{item.value}}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </li>
    <!-- Summary footer -->
    <li class="dropdown-menu-footer">
      <button
        class="btn btn-primary btn-block"
        [disabled]="!selectedFormat || curProductName.length == 0 || !curProtocol || loading"
        (click)="generate();">
        <span
          class="spinner-border spinner-border-sm"
          role="status"
          aria-hidden="true"
          *ngIf="loading"></span>
        {{ 'PROVISIONING.GENERSUMMARYREPORT' | translate }}
      </button>
    </li>
  </ul>
</li>
