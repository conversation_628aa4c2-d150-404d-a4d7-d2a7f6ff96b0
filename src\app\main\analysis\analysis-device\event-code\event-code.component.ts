import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { AnalysisDeviceService } from 'app/main/analysis/analysis-device/analysis-device.service';
import moment from 'moment'
import { ActivatedRoute } from '@angular/router';
import { GridSystemService } from 'app/main/commonService/grid-system.service';

@Component({
  selector: 'app-event-code',
  templateUrl: './event-code.component.html',
  styleUrls: ['./event-code.component.scss']
})
export class EventCodeComponent extends Unsubscribe implements OnInit {
  @ViewChild('eventCodeContainer', { static: false }) eventCodeContainer: ElementRef;
  @ViewChild('eventCodeRef', { static: false }) eventCodeRef: ElementRef;
  @Input() accessor: any;
  public loading = false;
  public dayItems: any[];
  public days: number;
  public personalTheme;
  public title = 'DeviceStatistics-TotalDevice'
  public componentTop: number = 0;
  selectedIcon: string = '';

  constructor(
    private _analysisDeviceService: AnalysisDeviceService,
    translateService: TranslateService,
    private _route: ActivatedRoute,
    private _gridService: GridSystemService
  ) {
    super();
    this.dayItems = _analysisDeviceService.getDaysItem().map(item => { return { key: translateService.instant(item.key), value: item.value, icons: item.icons } });
    this.days = _analysisDeviceService.curDays
    this.selectedIcon = this.dayItems.find(item => item.value === this.days)?.icons
    this.customSubscribe(_analysisDeviceService.onDaysChanged, days => { 
      this.days = days; 
      this.selectedIcon = this.dayItems.find(item => item.value === days)?.icons || '';
      this.getAnalysisEventCodeRes(); })
    this.customSubscribe(_route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  changeFn(item: any) {
    this.selectedIcon = item.icons;
    this.days = item.value;
    this._analysisDeviceService.changeDays(this.days);
  }

  public chartData = []

  public timeArray = [];
  /**
  * get AnalysisEventCode
  */
  getAnalysisEventCodeRes() {
    this.timeArray = []
    this.chartData = []
    this.loading = true
    this._analysisDeviceService.getAnalysisEventCode(this.days).then(res => {
      // console.log(res)
      let timeArray = [];
      let one_day = 86400000;
      let date_today = new Date(new Date().toLocaleDateString()).getTime();
      let date_start = date_today - one_day * (this.days - 1);
      for (let i = 0; i < this.days; i++) {
        timeArray.push(date_start + i * one_day);
        this.timeArray.push(moment(date_start + i * one_day).format("MM/DD"));
      }
      let dataArr = [];
      let tempData = {}
      if (res) {
        for (let i= res.length -1; i >=0; i--) {
          let item = res[i]
          let time = timeArray[i];
          if (item && item.eventCountDetail) {
            for (var key in item.eventCountDetail) {
              let value = item.eventCountDetail[key]
              if (value) {
                if (tempData[key] === undefined) {
                  tempData[key] = value
                }
                var index = dataArr.findIndex(function (item) {
                  return item.name == key;
                });
                if (index == -1) {
                  dataArr.push({ name: key, value: [[time, value]] });
                } else if (index != -1) {
                  dataArr[index].value.push([time, value]);
                }
              }
            }
          }
        }
      }
      dataArr.forEach((item) => {
        for (let i in timeArray) {
          let time = timeArray[i];
          let checkTime = item.value.findIndex(function (item) {
            return item[0] == time;
          });
          if (checkTime == -1) {
            item.value.push([time, 0]);
          }
        }
        item.value.sort(function (a, b) {
          return a[0] - b[0];
        });
      })
      dataArr.sort((a, b) => {
        if (a.name === 'UnAuthorized') {
          return 1
        } else if (b.name === 'UnAuthorized') {
          return -1
        }
        return tempData[b.name] - tempData[a.name]
      })

      //照字母順序排序name
      dataArr.sort((a, b) => a.name.localeCompare(b.name));

      let data = []
      dataArr.forEach(item => {
        data.push({
          name: item.name.charAt(0).toUpperCase() + item.name.slice(1),
          data: item.value,
          stack: 'total',
          type: 'bar',
          smooth: true,
          sampling: 'lttb'
        })
      })
      this.chartData = data
      // console.log(dataArr)
      // console.log(this.chartData)
    }).finally(() => {
      this.loading = false;
    });
  }

  emittedEvents(event: any): void {
    if (event === 'reload') {
      this.getAnalysisEventCodeRes()
    }else if (event.type === 'changeDay') {
      this.changeFn(event.daysItem);
    }else if (event === 'download') {
      this.triggerDownloadData()
    }else if (event === 'maximize') {
      this.triggerModal()
    }
  }
  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }

  ngOnInit(): void {
    this.getAnalysisEventCodeRes()
    this.customSubscribe(this._gridService.onGridsterItemComponentInfoChanged, item => {
      let rows = item[this.accessor.componentId]
      if (rows) {
        this.componentTop = rows.widget.top
      }
    })
  }

}

