import { Component, Input, OnInit } from '@angular/core';
import { DashboardService } from '../../dashboard.service';
import { Router } from '@angular/router';
import { timer, Subscription } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-user-numbers',
  templateUrl: './user-numbers.component.html',
  styleUrls: ['./user-numbers.component.scss']
})
export class UserNumbersComponent implements OnInit {
  @Input() accessor: any;
  public totalUsers: number;
  public loading: boolean = false;
  public iconloading: boolean = false;

  constructor(
    private _dashboardService: DashboardService,
    private _router: Router,
  ) {
    this.getTotalAlarms();
  }
  /**
 * Core card event
 * @param event 
 */
  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getTotalAlarms();
        break;
    }
  }

  viewUserList() {
    this.iconloading = true;
    this._router.navigate(['/users/account']);
  }

  /**
   * Get total users
   */
  getTotalAlarms() {
    this.loading = true;
    this._dashboardService.getTotalUsers()
      .then(res => this.totalUsers = res)
      .catch((err) => console.error(err))
      .finally(() => this.loading = false)
  }


  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getTotalAlarms();
    });
  }

  ngOnDestroy(): void {
  }

}
