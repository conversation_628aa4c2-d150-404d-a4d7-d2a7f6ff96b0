import { NgModule } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';
import { TranslateModule } from '@ngx-translate/core';
import { CoreCommonModule } from '@core/common.module';
import { AuthGuard, Navigation, ThemeStatus } from 'app/auth/helpers';
import { BlockUIModule } from 'ng-block-ui';
import { CoreBlockUiComponent } from '@core/components/core-card/core-block-ui/core-block-ui.component';

import { SharedModule } from '../shared/shared.module';
import { ChartModule } from '../shared/chart/chart.module'
import { PmChartModule } from '../shared/pm-chart/pm-chart.module'
import { GridsterModule } from 'angular-gridster2';
import { CoreCardModule } from '@core/components/core-card/core-card.module';
import { EventsModule } from '../events/events.module';
import { DeviceModule } from '../shared/device/device.module';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

import { NgxDatatableModule } from '@almaobservatory/ngx-datatable';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar-portable';

import { PersonalThemeService } from '@core/services/personal-theme.service';
// import { AnanlysisRefurbishmentComponent } from './ananlysis-refurbishment.component';
import { RefurbishmentHistoryComponent } from './refurbishment-history/refurbishment-history.component';
const routes = [
    {
        path: 'analysis/refurbishment',
        component: RefurbishmentHistoryComponent,
        canActivate: [AuthGuard],
        canDeactivate: [ThemeStatus],
        data: { animation: 'RefurbishmentHistoryComponent' },
        resolve: {
            pts: PersonalThemeService
        }
    }
];

@NgModule({
    declarations: [
        // AnanlysisRefurbishmentComponent,
        RefurbishmentHistoryComponent
    ],
    imports: [
      CommonModule,
      RouterModule.forChild(routes),
      ContentHeaderModule,
      TranslateModule,
      CoreCommonModule,
      SharedModule,
      ChartModule,
      PmChartModule,
      GridsterModule,
      CoreCardModule,
      NgxDatatableModule,
      NgSelectModule,
      PerfectScrollbarModule,
      BlockUIModule.forRoot({ template: CoreBlockUiComponent }),
      EventsModule,
      DeviceModule,
      FontAwesomeModule
    ],
    exports: [
        // AnanlysisRefurbishmentComponent
    ]
  })
  export class AnalysisRefurbishmentModule { }