<!-- core card component -->
<div
  class="card"
  #coreCard
  [ngClass]="{ 'card-fullscreen': onclickEvent.expandStatus }"
  *blockUI="coreCardId; template: _CoreBlockUiComponent">
  <div
    class="card-header"
    #cardHeader>
    <ng-content select=".card-title"></ng-content>
    <!-- heading elements -->
    <div class="heading-elements">
      <ul class="list-inline mb-0">
        <!-- flatpickr -->
        <li *ngIf="actionsView.flatpickr">
          <ngx-flatpickr-wrapper
            #actionFlatpickr
            placeholder="{{ 'USERS.DATERANGE' | translate }}"
            [config]="flatpickrConfig">
          </ngx-flatpickr-wrapper>
        </li>
        <li *ngIf="actionsView.flatpickrTime">
          <div class="d-flex">
            <ngx-flatpickr-wrapper
              class="actionFlatpickrTime mr-1"
              #actionFlatpickrBeginTime
              placeholder="{{ 'USERS.BEGINTIMEDATERANGE' | translate }}"
              [config]="flatpickrBeginTimeConfig">
            </ngx-flatpickr-wrapper>
            <ngx-flatpickr-wrapper
              class="actionFlatpickrTime"
              #actionFlatpickrEndTime
              placeholder="{{ 'USERS.ENDTIMEDATERANGE' | translate }}"
              [config]="flatpickrEndTimeConfig">
            </ngx-flatpickr-wrapper>
          </div>
        </li>
        <!-- column -->
        <li *ngIf="actionsView.column">
          <app-column-select
            [columns]="columns"
            (columSelectEvt)="updateColumnSelect($event)"
            placement="bottom"
            container="body"
            ngbTooltip="{{ 'COMMON.SELECTCOLUMN' | translate }}">
          </app-column-select>
        </li>
        <!-- columnDragula -->
        <li *ngIf="actionsView.columnDragula">
          <app-column-select-dragula
            [columns]="columnDragula"
            [componentId]="componentId"
            (columSelectEvt)="updateColumnDragulaSelect($event)"
            (columChangeEvt)="updateColumnDragulaChange($event)"
            placement="bottom"
            container="body"
            ngbTooltip="{{ 'COMMON.SELECTCOLUMN' | translate }}">
          </app-column-select-dragula>
        </li>
        <!-- collapse -->
        <li
          *ngIf="actionsView.collapse"
          (click)="collapse()">
          <a
            data-action="collapse"
            [ngClass]="{ rotate: onclickEvent.collapseStatus }">
            <i [data-feather]="'chevron-down'"></i>
          </a>
        </li>
        <!-- expand -->
        <li
          *ngIf="actionsView.expand"
          (click)="expand()">
          <a data-action="expand">
            <i [data-feather]="onclickEvent.expandStatus ? 'minimize' : 'maximize'"></i>
          </a>
        </li>
        <!-- duration -->
        <li *ngIf="actionsView.duration" (click)="duration()">
          <div ngbDropdown [placement]="'bottom'" container="body" class="btn-group dropup dropdown-icon-wrapper shadow-none pointer">
            <div ngbDropdownToggle class="shadow-none" 
            ngbTooltip="{{ 'ANALYSIS.SELECTDURATION' | translate }}" container="body" placement="bottom">
            <a data-action="duration" class="d-flex align-items-center shadow-none">
                <svg width="14px" height="14px" class="dropdown-icon">
                  <use [attr.href]="'../assets/fonts/added-icon.svg#' + selectedIcon"></use>
                </svg>
              </a>
              </div>
              <div ngbDropdownMenu class="dropdown-menu">
                <div *ngFor="let item of dayItems" 
                ngbDropdownItem class="draggable" (click)="changeFn(item)">
                  {{ item.key }}
                </div>
              </div>
            </div>
        </li>
        <!-- measure -->
        <li *ngIf="actionsView.measure" (click)="measure()">
          <div ngbDropdown [placement]="'bottom'" container="body" class="btn-group dropup dropdown-icon-wrapper shadow-none pointer">
            <div ngbDropdownToggle class="shadow-none" 
            ngbTooltip="Select Data Measure" container="body" placement="bottom">
            <a data-action="duration" class="d-flex align-items-center shadow-none">
                <svg width="14px" height="14px" class="dropdown-icon">
                  <use [attr.href]="'../assets/fonts/added-icon.svg#' + selectedMeasure"></use>
                </svg>
              </a>
              </div>
              <div ngbDropdownMenu class="dropdown-menu">
                <div *ngFor="let item of measureItems" 
                ngbDropdownItem class="draggable" (click)="changeMeasure(item)">
                  {{ item.key }}
                </div>
              </div>
            </div>
        </li>
        <!-- download -->
        <li
          *ngIf="actionsView.download"
          (click)="download()">
          <a
            data-action="download"
            placement="bottom"
            container="body"
            ngbTooltip="{{ 'COMMON.DOWNLOAD' | translate }}">
            <i [data-feather]="'download'"></i>
          </a>
        </li>
        <!-- ChartMaximize -->
        <li
          *ngIf="actionsView.maximize"
          (click)="maximize()">
          <a data-action="maximize" class="">
            <span 
              class=""
              ngbTooltip="{{ 'COMMON.OPEN_MAXIMIZE' | translate }}"
              container="body"
              type="button"
              placement="bottom">
              <i data-feather="maximize"></i>
            </span>
          </a>
        </li>
        <!-- reload -->
        <li
          *ngIf="actionsView.reload && !actionsView.close"
          (click)="reload()">
          <a
            data-action="reload"
            placement="bottom"
            container="body"
            ngbTooltip="{{ 'COMMON.RELOAD' | translate }}">
            <i [data-feather]="'rotate-cw'"></i>
          </a>
        </li>
        <!-- close -->
        <li
          *ngIf="actionsView.close"
          (click)="close()">
          <a data-action="close">
            <i
              class="text-danger"
              [data-feather]="'x'"></i>
          </a>
        </li>
      </ul>
    </div>
    <!-- / heading elements -->
  </div>
  <div
    *ngIf="actionsView.collapse; else noCollapse"
    class="card-content"
    [ngbCollapse]="onclickEvent.collapseStatus">
    <ng-container *ngTemplateOutlet="cardBodyOutlet"></ng-container>
  </div>












  <ng-template #noCollapse>
    <ng-container *ngTemplateOutlet="cardBodyOutlet"></ng-container>
  </ng-template>
  <ng-template #cardBodyOutlet>
    <!-- ? content wrapped with .card-body -->
    <ng-content select=".card-body"></ng-content>












    <!-- ? content without any wrapper -->
    <ng-content></ng-content>
  </ng-template>
</div>












<!-- /core card component -->
