import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { BlockUIModule } from 'ng-block-ui';
import { CoreBlockUiComponent } from '@core/components/core-card/core-block-ui/core-block-ui.component';
import { PmChartModule } from '../shared/pm-chart/pm-chart.module';
import { CoreCardModule } from '@core/components/core-card/core-card.module';

// Import the components to be shared
import { PmDeviceComponent } from './pm-device/pm-device.component';
import { PmGroupComponent } from './pm-group/pm-group.component';

@NgModule({
  declarations: [
    PmDeviceComponent,
    PmGroupComponent
  ],
  imports: [
    CommonModule,
    RouterModule, // Needed for ActivatedRoute in PmDeviceComponent
    TranslateModule, // Needed for translate pipe in templates
    NgbModule, // Needed for ngb-alert component
    PmChartModule, // Needed for app-pm-line-chart component
    CoreCardModule, // Needed for core-card component
    BlockUIModule.forRoot({ template: CoreBlockUiComponent }) // Needed for *blockUI directive
  ],
  exports: [
    PmDeviceComponent,
    PmGroupComponent
  ]
})
export class AnalysisPmSharedModule { }
