import { Component, OnInit, Input, ViewChild, ElementRef, HostListener, EventEmitter, Output } from '@angular/core';
import { BlockUIService } from 'ng-block-ui';
import { CoreBlockUiComponent } from '@core/components/core-card/core-block-ui/core-block-ui.component';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import cloneDeep from 'lodash/cloneDeep';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'core-card',
  templateUrl: './core-card.component.html'
})
export class CoreCardComponent implements OnInit {
  // public
  // Generate random string  assign to specific core-card to only block that specific card
  public coreCardId: string = Math.random().toString(36).substring(2);

  // To pass core-block-ui component values to _CoreBlockUiComponent variable
  public _CoreBlockUiComponent = CoreBlockUiComponent;

  // default status before click event
  public onclickEvent = {
    flatpickrStatus: false,
    flatpickrTimeStatus: false,
    columnStatus: false,
    collapseStatus: false,
    expandStatus: false,
    reloadStatus: false,
    closeStatus: false
  };

  // default action-views
  public actionsView = {
    flatpickr: false,
    flatpickrTime: false,
    column: false,
    columnDragula: false,
    collapse: false,
    expand: false,
    duration: false,
    measure: false,
    maximize:false,
    download:false,
    reload: false,
    close: false
  };

  @Input() actions: string[];
  @Input() componentId: string;
  @Input() columns: any;
  @Input() columnDragula: any;
  @Input() flatpickrConfig: any;
  @Input() flatpickrBeginTimeConfig: any;
  @Input() flatpickrEndTimeConfig: any;
  @Input() isReload = false;
  @Input() blockUIStatus = false;
  @Input() reloadTime: number = 2500;
  @Output() events: EventEmitter<any>;
  @Output() changeColumn = new EventEmitter<any>();
  @Output() selectColumn = new EventEmitter<any>();
  @Output() changeDay= new EventEmitter<any>();
  @Input() selectedIcon: string;
  @Input() dayItems: any[];
  @Input() selectedMeasure: string;
  @Input() measureItems: any[];
  
  @HostListener('document:keydown.escape', ['$event']) onKeydownHandler(event: KeyboardEvent) {
    // on press of esc card will return to normal from full screen
    if (this.onclickEvent.expandStatus) {
      this.onclickEvent.expandStatus = false;
    }
  }

  // private
  @ViewChild('coreCard') private coreCard: ElementRef;
  @ViewChild('cardHeader') private cardHeader: ElementRef;
  @ViewChild('actionFlatpickr') private actionFlatpickr: any;

  private editMode = false;

  /**
   * Constructor
   *
   * @param {BlockUIService} blockUIService
   */
  constructor(
    private blockUIService: BlockUIService,
    private gridService: GridSystemService,
    private genWidgetService: GenWidgetService
  ) {
    this.events = new EventEmitter<any>();
    gridService.onEditModeChanged.pipe(untilDestroyed(this)).subscribe(editMode => {
      this.editMode = editMode;
      if (editMode) {
        this.actionsView.reload = false;
        this.actionsView.close = true;
      } else {
        if (this.actions && this.actions.includes('reload')) {
          this.actionsView.reload = true;
        }
        this.actionsView.close = false;
      }
    });
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * ng On Init
   */
  ngOnInit() {
    // show flatpickr icon if actions includes 'flatpickr'
    if (this.actions.includes('flatpickr')) {
      this.actionsView.flatpickr = true;
      // this.flatpickrConfig = Object.assign(this.flatpickrConfig, { altInputClass: 'invisible'});
    }
    if (this.actions.includes('flatpickrTime')) {
      this.actionsView.flatpickrTime = true;
      // this.flatpickrConfig = Object.assign(this.flatpickrConfig, { altInputClass: 'invisible'});
    }
    // show column icon if actions includes 'column'
    if (this.actions.includes('column')) {
      this.actionsView.column = true;
    }
    // show columnDragula icon if actions includes 'columnDragula'
    if (this.actions.includes('columnDragula')) {
      this.actionsView.columnDragula = true;
    }
    // show collapse icon if actions includes 'collapse'
    if (this.actions.includes('collapse')) {
      this.actionsView.collapse = true;
    }

    // show expand icon if actions includes 'expand'
    if (this.actions.includes('expand')) {
      this.actionsView.expand = true;
    }
    // show duration icon if actions includes 'duration'
    if (this.actions.includes('duration')) {
      this.actionsView.duration = true;
    }
    if (this.actions.includes('measure')) {
      this.actionsView.measure = true;
    }
    // show duration icon if actions includes 'maximize'
    if (this.actions.includes('maximize')) {
      this.actionsView.maximize = true;
    }

    // show download icon if actions includes 'download'
    if (this.actions.includes('download')) {
      this.actionsView.download = true;
    }
    // show reload icon if actions includes 'reload'
    if (this.actions.includes('reload')) {
      this.actionsView.reload = true;
    }

    // show close icon if actions includes 'close'
    if (this.actions.includes('close')) {
      this.actionsView.close = true;
    }

    let columns = this.gridService.getPersonalColumns(this.componentId).option;
    if (columns) {
      for (var key in this.columns) {
        if (columns.hasOwnProperty(key)) {
          this.columns[key] = columns[key];
        }
      }
    }
  }

  /**
   *
   * @param changes
   *
   * ng On Changes
   */
  ngOnChanges(changes: any) {
    if (changes.isReload?.currentValue === true) {
      this.events.emit('reload');
      this.blockUIService.start(this.coreCardId);
    } else if (changes.isReload?.currentValue === false) {
      this.blockUIService.stop(this.coreCardId);
    }
    if (changes.blockUIStatus?.currentValue === true) {
      this.blockUIService.start(this.coreCardId);
    } else if (changes.blockUIStatus?.currentValue === false) {
      this.blockUIService.stop(this.coreCardId);
    }
  }

  // Public Methods
  // -----------------------------------------------------------------------------------------------------
  // -----------------------------------------------------------------------------------------------------
  /**
   * @description: toggle Flatpickr
   * @return {*}
   */
  // toggleFlatpickr() {
  //   this.events.emit('flatpickr');
  //   this.actionFlatpickr.flatpickr.toggle();
  // }
  /**
    * update column select status
    * @param columnName
    */
  updateColumnSelect(columnName) {
    this.events.emit('column');
    this.columns[columnName] = !this.columns[columnName]
    this.gridService.savePersonalColumns(this.componentId, this.columns);
  }
  updateColumnDragulaSelect(columnName) {
    this.events.emit('columnDragula');
    this.columnDragula.map(item => {
      if (item.name === columnName) {
        item.columnStatus = !item.columnStatus;
      }
    })
    this.selectColumn.emit(columnName)
    this.gridService.savePersonalColumns(this.componentId, this.columnDragula);
  }
  updateColumnDragulaChange(cloumns) {
    const cloneCloumns = cloneDeep(cloumns);
    this.changeColumn.emit({ cloumns: cloneCloumns });
    this.gridService.savePersonalColumns(this.componentId, cloneCloumns);
  }
  /**
   * Collapse
   */
  collapse() {
    this.events.emit('collapse');
    const cardHeaderEl = this.cardHeader.nativeElement;
    this.onclickEvent.collapseStatus = !this.onclickEvent.collapseStatus;
    if (this.onclickEvent.collapseStatus) {
      setTimeout(() => {
        cardHeaderEl.classList.add('pb-2');
      }, 350);
    } else {
      cardHeaderEl.classList.remove('pb-2');
    }
  }

  /**
   * Expand
   */
  expand() {
    this.events.emit('expand');
    this.onclickEvent.expandStatus = !this.onclickEvent.expandStatus;
  }
  

  /**
   * Close
   */
  close() {
    this.events.emit('close');
    if (this.editMode) {
      this.genWidgetService.widgetChanged([{
        componentId: this.componentId,
        hidden: true
      }])
      // this.coreCard.nativeElement.remove();
    } else {
      // console.log('close', this.componentId)
    }
  }

  /**
   * Duration
   */

  duration(){
    this.events.emit('duration');
  }
  changeFn(item: any) {
    this.selectedIcon = item.icons; 
    this.events.emit({ type: 'changeDay', daysItem: item });
  }
  /**
   * measure
   */

  measure(){
    this.events.emit('measure');
  }
  changeMeasure(item: any) {
    this.selectedMeasure = item.icons; 
    this.events.emit({ type: 'changeMeasure', measureItem: item });
  }

  /**
     * download
     */
  download() {
    this.events.emit('download');
  }
  /**
   * maximize
   */
  maximize() {
    this.events.emit('maximize');
  }

  /**
   * Reload
   */
  reload() {
    this.isReload = true;
    this.events.emit('reload');
    // this.blockUIService.start(this.coreCardId);

    // block-ui timeout
    setTimeout(() => {
      // this.blockUIService.stop(this.coreCardId);
      this.isReload = false;
    }, this.reloadTime);
  }
}
