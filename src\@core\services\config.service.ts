import { Inject, Injectable, InjectionToken } from '@angular/core';
import { ResolveEnd, Router } from '@angular/router';

import { BehaviorSubject, Observable } from 'rxjs';
import { filter } from 'rxjs/operators';

import merge from 'lodash/merge';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { StorageService } from 'app/main/commonService/storage.service';
import { AuthenticationService } from 'app/auth/service';

// Injection token for the core custom settings
export const CORE_CUSTOM_CONFIG = new InjectionToken('coreCustomConfig');

@Injectable({
  providedIn: 'root'
})
export class CoreConfigService {
  // Private
  public localConfig: any;
  public dbConfig: any;
  private readonly _defaultConfig: any;
  private _configSubject: BehaviorSubject<any>;

  /**
   * Constructor
   *
   * @param _config
   * @param {Router} _router
   */
  constructor(
    private _router: Router,
    @Inject(CORE_CUSTOM_CONFIG) private _config,
    private _gridService: GridSystemService,
    private _storageService: StorageService,
    private _authService: AuthenticationService
  ) {
    // Get the config from local storage
    if (_config.layout.enableLocalStorage) {
      // this.localConfig = JSON.parse(localStorage.getItem('config'));
      this.localConfig = this._storageService.get('config');
    } else {
      // localStorage.removeItem('config');
      this._storageService.remove('config');
    }

    let _gridConfig = _gridService.config;
    this.dbConfig = _gridConfig ? this.mergeConfig(cloneDeep(this._defaultConfig), _gridService.config) : null;
    // Set the defaultConfig to dbConfig if we have else appConfig (app-config.ts)
    this._defaultConfig = this.dbConfig ? this.dbConfig : _config;

    // 
    _gridService.configInit.subscribe(config => {
      this.setConfig(this.getUpdatedConfig(config));
    })

    // Initialize the config service
    this._initConfig();
  }

  //  Accessors
  // -----------------------------------------------------------------------------------------------------

  // Set the config
  set config(data) {
    let config;

    // Set config = dbConfig, If we have else defaultConfig
    if (this.dbConfig) {
      config = this.dbConfig;
    } else {
      config = this._configSubject.getValue();
    }

    // Merge provided data with config, and create new merged config
    config = merge({}, config, data);
    if (config?.layout?.menu?.hidden) {
      config.layout.menu.hidden = !this._authService.username || (this._authService.username && this._authService.isCSR)
    }
    
    this.saveConfig(config);

    // Set config to local storage if enableLocalStorage parameter is true
    if (config.layout.enableLocalStorage) {
      // localStorage.setItem('config', JSON.stringify(config));
      this._storageService.set('config', config);
    }

    // Inform the observers
    this._configSubject.next(config);
  }

  // Get the config
  get config(): any | Observable<any> {
    return this._configSubject.asObservable();
  }

  /**
   * Get default config
   *
   * @returns {any}
   */
  get defaultConfig(): any {
    return this._defaultConfig;
  }

  // Private methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Initialize
   *
   * @private
   */
  private _initConfig(): void {
    // Set the config from the default config
    this._configSubject = new BehaviorSubject(cloneDeep(this._defaultConfig));

    // On every RoutesRecognized event
    // Check if localDefault (localStorage if we have else defaultConfig) is different form the default one
    this._router.events.pipe(filter(event => event instanceof ResolveEnd)).subscribe(() => {

      // Get the local config from personal theme
      this.dbConfig = this.mergeConfig(cloneDeep(this._defaultConfig), this._gridService.config);

      let subjectConfig = this._configSubject.getValue();
      this.dbConfig.layout.editMode = subjectConfig.layout.editMode;


      // Get the local config from local storage
      // this.localConfig = JSON.parse(localStorage.getItem('config'));

      // Set localDefault to localConfig if we have else defaultConfig
      // let localDefault = this.localConfig ? this.localConfig : this._defaultConfig;
      let localDefault = this.dbConfig;

      // If localDefault is different form the provided config (page config)
      if (!isEqual(subjectConfig.layout, localDefault.layout)) {
        // Clone the current config
        const config = cloneDeep(this._configSubject.getValue());

        // Reset the layout from the default config
        config.layout = cloneDeep(localDefault.layout);

        // Set the config
        this._configSubject.next(config);
      }
    });
  }

  // Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Set config
   *
   * @param data
   * @param {{emitEvent: boolean}} param
   */
  setConfig(data, param = { emitEvent: true }): void {
    let config;

    // Set config = localConfig, If we have else defaultConfig
    // this.localConfig = JSON.parse(localStorage.getItem('config'));
    // console.log(this._defaultConfig, this._gridService.config);
    
    this.dbConfig = this.mergeConfig(cloneDeep(this._defaultConfig), this._gridService.config);
    if (this.dbConfig) {
      config = this.dbConfig;
    } else {
      config = this._configSubject.getValue();
    }
    // console.log(this.dbConfig, config, data);
    
    // Merge provided value with config, and create new merged config
    config = merge({}, config, data);
    if (config?.layout?.menu?.hidden) {
      config.layout.menu.hidden = !this._authService.username || (this._authService.username && this._authService.isCSR)
    }
    this.saveConfig(config);

    // Set config to local storage if enableLocalStorage parameter is true
    if (config.layout.enableLocalStorage) {
      // localStorage.setItem('config', JSON.stringify(config));
      this._storageService.set('config', config);
    }

    // If emitEvent option is true...
    if (param.emitEvent === true) {
      // Inform the observers
      this._configSubject.next(config);
    }
  }

  /**
   * Get config
   *
   * @returns {Observable<any>}
   */
  getConfig(): Observable<any> {
    return this._configSubject.asObservable();
  }

  /**
   * Reset to the default config
   */
  resetConfig(): void {
    this._configSubject.next(cloneDeep(this._defaultConfig));
  }

  mergeConfig(base, data, updatedItem = this.updatedItem) {
    if (data) {
      for (const key in updatedItem) {
        if (data.hasOwnProperty(key)) {
          let cItem = updatedItem[key];
          if (typeof cItem === 'object') {
            if (Array.isArray(cItem)) {
              if (cItem.indexOf(data[key]) !== -1) {
                base[key] = data[key];
              }
            } else {
              base[key] = this.mergeConfig(base[key], data[key], cItem);
            }
          } else {
            base[key] = data[key];
          }
        }
      }
    }
    return base;
  }

  getUpdatedConfig(config, updatedItem = this.updatedItem): any {
    let result = {};
    for (const key in updatedItem) {
      if (config.hasOwnProperty(key)) {
        let cItem = updatedItem[key];
        if (typeof cItem === 'object') {
          if (Array.isArray(cItem)) {
            if (cItem.indexOf(config[key]) !== -1) {
              result[key] = config[key];
            }
          } else {
            result[key] = this.getUpdatedConfig(config[key], cItem);
          }
        } else {
          result[key] = config[key];
        }
      }
    }
    return result;
  }

  saveConfig(config) {
    let data = this.getUpdatedConfig(config);
    this._gridService.savePersonalConfig(data);
  }

  private updatedItem = {
    app: {
      appLanguage: ['en', 'ch', 'jp', 'tw', 'de', 'fr', 'pt'],
    },
    layout: {
      skin: ['dark', 'default', 'blue', 'red'],
      animation: ['fadeIn', 'fadeInLeft', 'zoomIn'],
      menu: {
        collapsed: [true, false],
        hidden: [true, false]
      },
      navbar: {
        backgroundColor: ''
      },
      editMode: [false, true]
    }
  }

}