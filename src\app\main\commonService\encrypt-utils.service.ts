import { Injectable } from '@angular/core';
// import CryptoJ<PERSON> from 'crypto-js';
import MD5 from 'crypto-js/md5';
import AES from 'crypto-js/aes';
import Pkcs7 from 'crypto-js/pad-pkcs7';
import utf8 from 'crypto-js/enc-utf8';

@Injectable({
  providedIn: 'root'
})
export class EncryptUtilsService {

  constructor() { }

  encrypt (value, key) {
    if (!value) {
      return undefined;
    } else {
      return key && AES.encrypt(JSON.stringify(value), key).toString();
    }
  }

  decrypt (value, key) {
    if (!value) {
      return undefined;
    } else {
      try {
        var bytes = key && AES.decrypt(value, key).toString(utf8);
        return bytes && JSON.parse(bytes);
      } catch (error) {
        return undefined;
      }
    }
  }

  encryptPasswd (username, password, key) {
    const key1 = MD5(key + username).toString();
    const aesKey = MD5(username + key).toString();
    const iv = MD5(key).toString();
    const value = AES.encrypt(password, key1).toString();
    const bytes = AES.encrypt(value.toString(), aesKey, {
      iv: iv,//enc.Utf8.parse('94c3e5e0e39ceb01'),
      padding: Pkcs7
    }).toString();
    return bytes;
  }

  encryptNodePasswd (password, key) {
    const key1 = MD5(key).toString();
    const iv = MD5(key).toString();
    const value = AES.encrypt(password, key1).toString();
    const bytes = AES.encrypt(value.toString(), key1, {
      iv: iv,//enc.Utf8.parse('94c3e5e0e39ceb01'),
      padding: Pkcs7
    }).toString();
    return bytes;
  }

  b64EncodeUnicode(str) {
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode(+('0x' + p1));
    }));
  }
  
  md5(str) {
    return MD5(str).toString();
  }
}
