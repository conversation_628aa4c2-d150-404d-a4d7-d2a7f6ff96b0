<core-card
  [actions]="['reload','maximize','download']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title d-flex justify-content-between align-items-center"
  style="width: calc(100% - 65px);">
    <span
      class="text-truncate"
      ngbTooltip="{{ 'ANALYSIS.SIMSTATUS' | translate }}"
      container="body">
      {{ 'ANALYSIS.SIMSTATUS' | translate }}
    </span>
    <div class="mr-50">
      <select
        class="form-control form-control-sm"
        [(ngModel)]="selectedProductName"
        (change)="changeFn($event)">
        <option
          *ngFor="let option of productNameSelect"
          [value]="option">
          {{option}}
        </option>
      </select>
    </div>
  </h4>
  <div class="card-body">
    <main
      class="w-100 h-100 "
      #simStatusRef>
      <app-pie-echarts
        [chartRef]="simStatusRef"
        [chartData]="chartData"
        [isShowNoData]="isShowNoData"
        [title]="'ProvisioningStatistics-SIMStatus'"
        [downloadData]="downloadData"
        [openModal]="openModalFlag">
      </app-pie-echarts>
    </main>
  </div>
</core-card>
