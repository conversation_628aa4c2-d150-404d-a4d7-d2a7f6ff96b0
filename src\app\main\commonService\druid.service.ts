import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { GetExternalServerUrlService } from 'app/main/commonService/get-external-server-url.service'
import { Observable, of, throwError } from 'rxjs';
import { throttleTime, tap } from 'rxjs/operators';
import { catchError, delay, scan, shareReplay, retry, timeout, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DruidService {
  public vendorMapping = {
    "0": "Druid",
    "1": "Druid_DNM",
    "2": "HP",
    "3": "Open_5GC",
  }
  public vendor

  constructor(
    private _httpClient: HttpClient,
    private _getExternalServerUrlService: GetExternalServerUrlService
  ) {
    this.vendor = this._getExternalServerUrlService.type_5gc;
    // console.log(this.vendor)
  }

  private countRequestCache: Map<string, Observable<any>> = new Map();
  getCount(subclass: string, path: string, param?: any): Observable<any> {
    const cacheKey = `${subclass}-${path}-${JSON.stringify(param)}`;
    if (this.countRequestCache.has(cacheKey)) {
      return this.countRequestCache.get(cacheKey) as Observable<any>;
    } else {
      let params = { path: `/api/count/${path}` };
      if (param) {
        params = { ...params, ...param };
      }
      // console.log(params);
      const queryString = new URLSearchParams(params).toString();
      // console.log(queryString);

      const request$ = this._httpClient.post(`nbi/5gc/${subclass}/${this.vendorMapping[this.vendor]}?${queryString}`, null).pipe(
        map(response => {
          // 檢查回應是否為字串，如果是字串才需要解析
          if (typeof response === 'string') {
            try {
              // 嘗試解析回應，如果是字串且有效 JSON，則返回解析後的物件
              return JSON.parse(response);
            } catch (error) {
              // 如果無法解析為 JSON，則返回原始字串
              return response;
            }
          }
          // 如果回應本身就是物件，直接返回
          return response;
        }),
        shareReplay(1),
        tap(() => {
          this.countRequestCache.delete(cacheKey);
        }),
        catchError(error => {
          this.countRequestCache.delete(cacheKey);
          return throwError(() => error);
        })
      );

      this.countRequestCache.set(cacheKey, request$);
      return request$;
    }
  }


  private requestCache: Map<string, Observable<any>> = new Map();
  getAPI(subclass: string, path: string, param?: any): Observable<any> {
    const cacheKey = `${subclass}-${path}-${JSON.stringify(param)}`;
    if (this.requestCache.has(cacheKey)) {
      return this.requestCache.get(cacheKey) as Observable<any>;
    } else {
      let params = { path: `/api/${path}` };
      if (param) {
        params = { ...params, ...param };
      }
      // console.log(params)
      const queryString = new URLSearchParams(params).toString();
      // console.log(queryString)

      const request$ = this._httpClient.post(`nbi/5gc/${subclass}/${this.vendorMapping[this.vendor]}?${queryString}`, null).pipe(
        map(response => {
          // 檢查回應是否為字串，如果是字串才需要解析
          if (typeof response === 'string') {
            try {
              // 嘗試解析回應，如果是字串且有效 JSON，則返回解析後的物件
              return JSON.parse(response);
            } catch (error) {
              // 如果無法解析為 JSON，則返回原始字串
              return response;
            }
          }
          // 如果回應本身就是物件，直接返回
          return response;
        }),
        shareReplay(1),
        tap(() => {
          this.requestCache.delete(cacheKey);
        }),
        catchError(error => {
          this.requestCache.delete(cacheKey);
          return throwError(() => error);
        })
      );
      this.requestCache.set(cacheKey, request$);
      return request$;
    }
  }


  deleteAPI(subclass: string, path: string, param?: any): Promise<any[]> {
    let params = {
      method: 'DELETE',
      path: `/api/${path}`
    };
    if (param) {
      params = { ...params, ...param };
    }
    const queryString = new URLSearchParams(params).toString();
    // console.log(queryString)
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/5gc/${subclass}/${this.vendorMapping[this.vendor]}?${queryString}`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (err: any) => {
          try {
            // 檢查是否為字串並嘗試將錯誤轉換為 JSON 格式
            if (typeof err.error === 'string') {
              err.error = JSON.parse(err.error);
            }
          } catch (parseError) {
            console.error('Error is not in JSON format:', parseError);
            // 如果不是 JSON 格式，將錯誤包裝成物件格式
            err.error = { message: err.error };
          }
          reject(err);
        }
      });
    });
  }


  postAPI(subclass: string, path: string, body: any): Promise<any[]> {
    let params = {
      method: 'POST',
      path: `/api/${path}`
    };

    const queryString = new URLSearchParams(params).toString();

    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/5gc/${subclass}/${this.vendorMapping[this.vendor]}?${queryString}`, body).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (err: any) => {
          try {
            // 檢查是否為字串並嘗試將錯誤轉換為 JSON 格式
            if (typeof err.error === 'string') {
              err.error = JSON.parse(err.error);
            }
          } catch (parseError) {
            console.error('Error is not in JSON format:', parseError);
            // 如果不是 JSON 格式，將錯誤包裝成物件格式
            err.error = { message: err.error };
          }
          reject(err);
        }
      });
    });
  }

  patchAPI(subclass: string, path: string, body: any, param?: any): Promise<any[]> {
    let params = {
      method: 'PUT',
      path: `/api/${path}`
    };
    if (param) {
      params = { ...params, ...param };
    }
    const queryString = new URLSearchParams(params).toString();
    const bodyString = new URLSearchParams(body).toString();

    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/5gc/${subclass}/${this.vendorMapping[this.vendor]}?${queryString}`, body).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (err: any) => {
          try {
            // 檢查是否為字串並嘗試將錯誤轉換為 JSON 格式
            if (typeof err.error === 'string') {
              err.error = JSON.parse(err.error);
            }
          } catch (parseError) {
            console.error('Error is not in JSON format:', parseError);
            // 如果不是 JSON 格式，將錯誤包裝成物件格式
            err.error = { message: err.error };
          }
          reject(err);
        }
      });
    });
  }


  getCSV(subclass: string, path: string, param?: any): Promise<any[]> {
    let params = {
      path: `/api/csv/${path}`
    };
    if (param) {
      params = { ...params, ...param };
    }
    const queryString = new URLSearchParams(params).toString();

    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/5gc/${subclass}/${this.vendorMapping[this.vendor]}?${queryString}`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (err: any) => {
          try {
            // 檢查是否為字串並嘗試將錯誤轉換為 JSON 格式
            if (typeof err.error === 'string') {
              err.error = JSON.parse(err.error);
            }
          } catch (parseError) {
            console.error('Error is not in JSON format:', parseError);
            // 如果不是 JSON 格式，將錯誤包裝成物件格式
            err.error = { message: err.error };
          }
          reject(err);
        }
      });
    });
  }

  postCSV(subclass: string, path: string, body: any, filename): Promise<any[]> {
    let params = {
      method: 'POST',
      path: `/api/csv/${path}`
    };
    const queryString = new URLSearchParams(params).toString();

    // 生成一個 boundary
    const boundary = '----WebKitFormBoundary' + Math.random().toString(36).substring(2);

    // 手動構建 multipart/form-data 請求體
    const bodyContent =
      `--${boundary}\r\n` +
      `Content-Disposition: form-data; name="csvfile"; filename="${filename}"\r\n` +
      `Content-Type: text/csv\r\n\r\n` +
      `${body}\r\n` +
      `--${boundary}--\r\n`;

    // 設置  Content-Type 標頭，並手動添加 boundary
    const headers = new HttpHeaders({
      'Content-Type': `multipart/form-data; boundary=${boundary}`
    });

    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/5gc/${subclass}/Druid?${queryString}`, bodyContent, { headers }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (err: any) => {
          try {
            // 檢查是否為字串並嘗試將錯誤轉換為 JSON 格式
            if (typeof err.error === 'string') {
              err.error = JSON.parse(err.error);
            }
          } catch (parseError) {
            console.error('Error is not in JSON format:', parseError);
            // 如果不是 JSON 格式，將錯誤包裝成物件格式
            err.error = { message: err.error };
          }
          reject(err);
        }
      });
    });
  }

  postRestoreConfigurationCSV(subclass: string, path: string, body: any, filename): Promise<any[]> {
    let params = {
      method: 'POST',
      path: `/api/${path}`
    };
    const queryString = new URLSearchParams(params).toString();

    // 生成一個 boundary
    const boundary = '----WebKitFormBoundary' + Math.random().toString(36).substring(2);

    // 手動構建 multipart/form-data 請求體
    const bodyContent =
      `--${boundary}\r\n` +
      `Content-Disposition: form-data; name="csvfile"; filename="${filename}"\r\n` +
      `Content-Type: text/csv\r\n\r\n` +
      `${body}\r\n` +
      `--${boundary}--\r\n`;

    // 設置  Content-Type 標頭，並手動添加 boundary
    const headers = new HttpHeaders({
      'Content-Type': `multipart/form-data; boundary=${boundary}`
    });

    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/5gc/${subclass}/Druid?${queryString}`, bodyContent, { headers }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (err: any) => {
          try {
            // 檢查是否為字串並嘗試將錯誤轉換為 JSON 格式
            if (typeof err.error === 'string') {
              err.error = JSON.parse(err.error);
            }
          } catch (parseError) {
            console.error('Error is not in JSON format:', parseError);
            // 如果不是 JSON 格式，將錯誤包裝成物件格式
            err.error = { message: err.error };
          }
          reject(err);
        }
      });
    });
  }


  postDruidCSV(body: any, filename): Promise<any[]> {
    // 生成一個 boundary
    const boundary = '----WebKitFormBoundary' + Math.random().toString(36).substring(2);

    // 手動構建 multipart/form-data 請求體
    const bodyContent =
      `--${boundary}\r\n` +
      `Content-Disposition: form-data; name="csvfile"; filename="${filename}"\r\n` +
      `Content-Type: text/csv\r\n\r\n` +
      `${body}\r\n` +
      `--${boundary}--\r\n`;

    // 設置 Authorization 和 Content-Type 標頭，並手動添加 boundary
    const headers = new HttpHeaders({
      'Authorization': 'Basic ' + btoa(`raemis:password`),
      'Content-Type': `multipart/form-data; boundary=${boundary}`
    });
    return new Promise((resolve, reject) => {
      this._httpClient.post(`api/csv/subscriber`, bodyContent, { headers }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (err: any) => {
          try {
            // 檢查是否為字串並嘗試將錯誤轉換為 JSON 格式
            if (typeof err.error === 'string') {
              err.error = JSON.parse(err.error);
            }
          } catch (parseError) {
            console.error('Error is not in JSON format:', parseError);
            // 如果不是 JSON 格式，將錯誤包裝成物件格式
            err.error = { message: err.error };
          }
          reject(err);
        }
      });
    });
  }
}


