import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CareErrorStatusComponent } from './care-error-status.component';

describe('CareErrorStatusComponent', () => {
  let component: CareErrorStatusComponent;
  let fixture: ComponentFixture<CareErrorStatusComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CareErrorStatusComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CareErrorStatusComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
