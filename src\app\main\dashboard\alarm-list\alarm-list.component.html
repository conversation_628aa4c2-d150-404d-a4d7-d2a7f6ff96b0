<core-card
  [actions]="['columnDragula', 'reload']"
  [componentId]="accessor.componentId"
  [columnDragula]="tableOption"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event);"
  (changeColumn)="changeColumn($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <ngx-datatable
    #tableRowDetails
    appDatatableRecalculate
    [datatable]="tableRowDetails"
    [rows]="deviceAlarms"
    [rowHeight]="37"
    class="bootstrap core-bootstrap"
    [limit]="selectedOption"
    [columnMode]="ColumnMode.standard"
    [headerHeight]="50"
    [footerHeight]="45"
    [scrollbarH]="true"
    [scrollbarV]="true"
    (sort)="onSort($event)"
    (resize)="onResize($event)">
    <ngx-datatable-row-detail [rowHeight]="100">
      <ng-template
        let-row="row"
        let-expanded="expanded"
        ngx-datatable-row-detail-template>
        <div class="ml-75 pl-5 pt-75">
          <div class="text-success border-left-success border-left-3 pl-1">
            <span *ngIf="row.NotificationType!=''">
              <strong>
                {{ 'ALARMS.NOTIFICATIONTYPE' | translate }} :
              </strong>
              {{ row.NotificationType }}
            </span>
            <br *ngIf="row.NotificationType!=''">
            <span *ngIf="row.ProbableCause!=''">
              <strong>Probable Cause :</strong>
              {{ row.ProbableCause }}
            </span>
            <br *ngIf="row.ProbableCause!=''">
            <span *ngIf="row.SpecificProblem!=''">
              <strong>Specific Problem :</strong>
              {{ row.SpecificProblem }}
            </span>
            <br *ngIf="row.SpecificProblem!=''">
            <span *ngIf="row.AdditionalText!=''">
              <strong>Additional Text :</strong>
              {{ row.AdditionalText }}
            </span>
            <br *ngIf="row.AdditionalText!=''">
            <span *ngIf="row.AdditionalInformation!=''">
              <strong>Additional Information :</strong>
              {{ row.AdditionalInformation }}
            </span>
          </div>
        </div>
      </ng-template>
    </ngx-datatable-row-detail>
    <!-- Detail -->
    <ngx-datatable-column
      [width]="30"
      cellClass="datatable-expand"
      [resizeable]="false"
      [sortable]="false"
      [draggable]="false"
      [canAutoResize]="false">
      <ng-template
        let-row="row"
        let-expanded="expanded"
        ngx-datatable-cell-template>
        <a
          href="javascript:void(0)"
          class="text-body"
          [class.datatable-icon-right]="!expanded"
          [class.datatable-icon-down]="expanded"
          title="{{ 'COMMON.EXPANDCOLLROW' | translate }}"
          (click)="rowDetailsToggleExpand(row)">
        </a>
      </ng-template>
    </ngx-datatable-column>
    <ng-container *ngFor="let col of tableOption">
      <!-- State -->
      <ngx-datatable-column
        *ngIf="col.prop === 'flag' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="flag">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <select
            class="form-control form-control-sm"
            name="state"
            id="state"
            [(ngModel)]="queryParams.filter['flag']"
            (change)="filterSelectChange($event,'flag')">
            <option
              *ngFor="let option of selectState"
              [value]="option.value">
              {{option.name}}
            </option>
          </select>
        </ng-template>
        <ng-template
          let-row="row"
          let-flag="value"
          ngx-datatable-cell-template>
          {{flag || '-'}}
        </ng-template>
      </ngx-datatable-column>
      <!-- severity -->
      <ngx-datatable-column
        *ngIf="col.prop === 'PerceivedSeverity' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="PerceivedSeverity">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <select
            class="form-control form-control-sm"
            name="severity"
            id="severity"
            [(ngModel)]="queryParams.filter['severity']"
            (change)="filterSelectChange($event,'severity')">
            <option value>{{ 'ALARMS.ALLSEVERITY' | translate }}</option>
            <option
              *ngFor="let option of selectSeverity"
              [value]="option.value">
              {{option.name}}
            </option>
          </select>
        </ng-template>
        <ng-template
          let-row="row"
          let-PerceivedSeverity="value"
          ngx-datatable-cell-template>
          <div
            class="badge badge-pill"
            [ngClass]="{
                      'badge-light-warning': PerceivedSeverity.toLowerCase() =='major',
                      'badge-light-danger': PerceivedSeverity.toLowerCase() == 'critical',
                      'badge-light-secondary': PerceivedSeverity.toLowerCase() == 'indeterminate',
                      'badge-light-custom-gray': PerceivedSeverity.toLowerCase() == 'warning' || PerceivedSeverity.toLowerCase() == 'minor'
                    }">
            {{ PerceivedSeverity | titlecase }}
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- serial -->
      <ngx-datatable-column
        *ngIf="col.prop === 'serialNumber' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="serialNumber">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            class="form-control form-control-sm"
            placeholder="{{column.name | titlecase}}"
            [(ngModel)]="queryParams.filter['serialNumber']"
            (ngModelChange)="filterInputChange($event, 'serialNumber')">
        </ng-template>
        <ng-template
          let-row="row"
          let-serial="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            (click)="gotoDeviceIInfo(serial)"
            [textClass]="'text-truncate'"
            class="text-primary cursor-pointer"
            [content]="serial || '-'"
            [placement]="'left'"></app-beautify-content>
          <!-- <app-beautify-content
            [content]="serial || '-'"
            [placement]="'left'"></app-beautify-content> -->
        </ng-template>
      </ngx-datatable-column>
      <!-- Probable Cause -->
      <ngx-datatable-column
        *ngIf="col.prop === 'ProbableCause' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="ProbableCause">
        <ng-template
          let-row="row"
          let-ProbableCause="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="ProbableCause"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Event Time -->
      <ngx-datatable-column
        *ngIf="col.prop === 'EventTime' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="EventTime">
        <ng-template
          let-row="row"
          let-EventTime="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="EventTime | date:'MM/dd/yy, HH:mm:ss'"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Cleared Time -->
      <ngx-datatable-column
        *ngIf="col.prop === 'clearedTime' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="clearedTime">
        <ng-template
          let-row="row"
          let-clearedTime="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="clearedTime? (clearedTime | date:'MM/dd/yy, HH:mm:ss') :'N/A'"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- alarmID -->
      <ngx-datatable-column
        *ngIf="col.prop === 'AlarmIdentifier' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="AlarmIdentifier">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            class="form-control form-control-sm"
            placeholder="{{ col.translate | translate }}"
            [(ngModel)]="queryParams.filter['alarmId']"
            (ngModelChange)="filterInputChange($event, 'alarmId')">
        </ng-template>
        <ng-template
          let-row="row"
          let-AlarmIdentifier="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="AlarmIdentifier"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- eventType -->
      <ngx-datatable-column
        *ngIf="col.prop === 'EventType' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="EventType">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            class="form-control form-control-sm"
            placeholder="{{column.name | titlecase}}"
            [(ngModel)]="queryParams.filter['eventType']"
            (ngModelChange)="filterInputChange($event, 'eventType')">
        </ng-template>
        <ng-template
          let-row="row"
          let-EventType="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="EventType || '-'"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Group -->
      <ngx-datatable-column
        *ngIf="col.prop === 'devGroup' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="devGroup">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <select
            class="form-control form-control-sm"
            [(ngModel)]="queryParams.filter['group']"
            (change)="filterSelectChange($event,'group')">
            <option value>{{ 'ALARMS.ALLGROUPS' | translate }}</option>
            <option
              *ngFor="let option of selectGroup"
              [value]="option.id">
              {{option.name}}
            </option>
          </select>
        </ng-template>
        <ng-template
          let-row="row"
          let-devGroup="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="devGroup"
            [type]="'list'"
            [textClass]="'badge-light-warning'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Product -->
      <ngx-datatable-column
        *ngIf="col.prop === 'productName' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="productName">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <select
            class="form-control form-control-sm"
            [(ngModel)]="queryParams.filter['productName']"
            (change)="filterSelectChange($event,'productName')">
            <option value>{{ 'DEVICES.PRODUCT' | translate }}</option>
            <option
              *ngFor="let option of selectProduct"
              [value]="option.value">
              {{option.name}}
            </option>
          </select>
          <!-- <input
            type="text"
            class="form-control form-control-sm"
            placeholder="{{column.name | titlecase}}"
            [(ngModel)]="queryParams.filter['productName']"
            (ngModelChange)="filterInputChange($event)"> -->
        </ng-template>
        <ng-template
          let-row="row"
          let-index="rowIndex"
          let-productName="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="productName"
            [placement]="'right'"
            [textClass]="'badge badge-light-info'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- ackUser -->
      <ngx-datatable-column
        *ngIf="col.prop === 'ackUser' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="ackUser">
        <ng-template
          let-row="row"
          let-ackUser="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="ackUser || '-'"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- ackTime -->
      <ngx-datatable-column
        *ngIf="col.prop === 'ackTime' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="ackTime">
        <ng-template
          let-row="row"
          let-ackTime="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="ackTime | date:'MM/dd/yy, HH:mm:ss'"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
    </ng-container>
    <!-- ngx-datatable-footer -->
    <ngx-datatable-footer>
      <ng-template ngx-datatable-footer-template>
        <div class="col-12 d-flex align-items-start">
          <div
            class="page-count"
            style="padding: 0;">
            <span>{{pageCount}} total</span>
          </div>
          <div class="pager-vertical-center">
            <datatable-pager
              [pagerLeftArrowIcon]="'datatable-icon-left'"
              [pagerRightArrowIcon]="'datatable-icon-right'"
              [pagerPreviousIcon]="'datatable-icon-prev'"
              [pagerNextIcon]="'datatable-icon-skip'"
              [page]="queryParams.page"
              [size]="queryParams.size"
              [count]="pageCount"
              (change)="onFooterPage($event)">
            </datatable-pager>
          </div>
        </div>
      </ng-template>
    </ngx-datatable-footer>
  </ngx-datatable>
</core-card>
