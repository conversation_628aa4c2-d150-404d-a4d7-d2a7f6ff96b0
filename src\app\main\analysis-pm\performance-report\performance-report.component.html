<core-card
  [actions]="['columnDragula', 'reload']"
  [componentId]="accessor.componentId"
  [columnDragula]="tableOption"
  (changeColumn)="changeColumn($event)"
  [blockUIStatus]="loading"
  (events)="emittedEvents($event)">
  <h4 class="card-title">
    {{ accessor.name | translate }}
    <button
      *ngIf="!showSearchBar"
      type="button"
      class="btn btn-icon btn-outline-dark ml-50"
      style="padding: 5px"
      ngbTooltip="{{ 'PM.OPENSEARCHBAR' | translate }}"
      [openDelay]="300"
      container="body"
      placement="auto"
      (click)="openSearchBar()">
      <i [data-feather]="'search'"></i>
    </button>
    <button
      *ngIf="showSearchBar"
      type="button"
      class="btn btn-icon btn-outline-dark ml-50"
      style="padding: 5px"
      ngbTooltip="{{ 'PM.HIDESEARCHBAR' | translate }}"
      [openDelay]="300"
      container="body"
      placement="auto"
      (click)="closeSearchBar()">
      <i [data-feather]="'minus'"></i>
    </button>
  </h4>
  <!-- search bar -->
  <div
    *ngIf="showSearchBar"
    class="border-dark mb-1 pl-50 pr-50 pb-50 pt-50 searchBar"
    style="height: auto;">
    <div class="row align-items-end">
      <!-- Target -->
      <div
        class="col-lg-1 col-md-12 removePadding"
        [ngClass]="selectedTarget ? 'col-lg-1' : 'col-lg-3'">
        <span>{{ 'PM.TARGET' | translate }}</span>
        <select
          class="form-control form-control-sm"
          style="font-size: 12px;padding-left: 2px;"
          (change)="targetSelectChange($event,conditionList)"
          [disabled]="noPermission"
          [(ngModel)]="selectedTarget">
          <option value="all">ALL</option>
          <option value="sn">Serial Number</option>
          <option value="group">Group</option>
        </select>
      </div>
      <!-- sn -->
      <div
        class="col-lg-2 col-md-12 removePadding"
        *ngIf="selectedTarget=='sn' || selectedTarget=='all'">
        <div>
          <span>
            {{ 'PM.TARGETSERIALNUMBER' | translate }}
            <app-more-info [tooltipContent]="'COMMON.SN_SEPARATED_BY_COMMAS'"></app-more-info>
          </span>
          <input
            [disabled]="selectedTarget=='all' || noPermission"
            [(ngModel)]="serialNumber"
            type="text"
            trim
            placeholder="ALL"
            class="form-control form-control-sm">
        </div>
      </div>
      <!-- group -->
      <div
        class="col-lg-2 col-md-12 removePadding"
        *ngIf="selectedTarget=='group'">
        <span>{{ 'PM.TARGETGROUP' | translate }}</span>
        <select
          class="form-control form-control-sm"
          (change)="filterSelectChange($event,conditionList)"
          [disabled]="noPermission">
          <option value>{{ 'ALARMS.ALLGROUPS' | translate }}</option>
          <option
            *ngFor="let option of selectGroup"
            [value]="option.id">
            {{option.name}}
          </option>
        </select>
      </div>
      <!-- PM Parameter Name -->
      <div class="col-lg-2 col-md-12 removePadding">
        <span>{{ 'PM.PARAMNAME' | translate }}</span>
        <span class="text-warning ml-50">*</span>
        <ng-select
          class="column-select-filter ng-select-size-sm paramNameSelect"
          appendTo="body"
          [(ngModel)]="classify"
          [items]="selectPMParamName"
          placeholder="PM {{ 'PM.PARAMNAME' | translate }}"
          bindLabel="name"
          groupBy="group"
          [disabled]="noPermission"
          [clearable]="false"
          (change)="classifyChange($event,'manualParamPath')">
        </ng-select>
      </div>
      <!-- param Path -->
      <div class="col-lg-2 col-md-12  removePadding">
        <span>{{ 'PM.PARAMPATH' | translate }}</span>
        <span class="text-warning ml-50">*</span>
        <input
          #paramPathInput
          [ngClass]="{'has-error': verifyParamPath}"
          [(ngModel)]="paramPath"
          type="text"
          trim
          placeholder="{{ 'PM.PARAMPATH' | translate }}"
          class="form-control form-control-sm"
          [disabled]="!showManualParamPath || noPermission">
      </div>
      <!-- condition -->
      <div class="col-lg-1 col-md-12  removePadding">
        <span>{{ 'PM.CONDITION' | translate }}</span>
        <span class="text-warning ml-50">*</span>
        <ng-select
          class="column-select-filter ng-select-size-sm"
          appendTo="body"
          [(ngModel)]="condition"
          [items]="selectCondition"
          placeholder="{{ 'PM.CONDITION' | translate }}"
          bindLabel="name"
          [clearable]="false"
          [disabled]="classify?false:true||noPermission">
        </ng-select>
      </div>
      <!-- param value -->
      <div class="col-lg-2 col-md-12  removePadding">
        <span>{{ 'PM.PARAMVALUE' | translate }}</span>
        <span class="text-warning ml-50">*</span>
        <input
          [(ngModel)]="paramValue"
          type="text"
          trim
          placeholder="{{ 'PM.PARAMVALUE' | translate }}"
          class="form-control form-control-sm"
          [disabled]="classify?false:true||noPermission">
      </div>
      <!-- Duration -->
      <div class="col-lg-1 col-md-12 removePadding">
        <span>{{ 'PM.DURATION' | translate }}</span>
        <span class="text-warning ml-50">*</span>
        <ng-select
          class="column-select-filter ng-select-size-sm"
          appendTo="body"
          [(ngModel)]="duration"
          [items]="selectDuration"
          placeholder="{{ 'PM.DURATION' | translate }}"
          bindLabel="name"
          [clearable]="false"
          [disabled]="classify?false:true||noPermission"
          (change)="druationChange($event,conditionList)">
        </ng-select>
      </div>
      <!-- btn -->
      <div
        class="col-lg-1 col-md-12 removePadding"
        style=" display: flex;align-items: flex-end;">
        <!-- add param btn -->
        <button
          (click)="addNewCondition()"
          type="button"
          class="btn btn-primary mr-1"
          style="padding: 10px 10px"
          [disabled]="classify && paramPath && condition && isNumber(paramValue)?false:true || noPermission"
          ngbTooltip="{{ 'PM.ADDCONDITION' | translate }}"
          [openDelay]="300"
          container="body"
          placement="auto">
          <i [data-feather]="'plus'"></i>
        </button>
        <!-- search btn -->
        <button
          (click)="modalOpenSearchResult(modalSearchResult,'new',null,conditionList)"
          type="button"
          class="btn btn-primary"
          style="padding: 10px 10px"
          [disabled]="conditionList['metricParameters'].length != 0?false:true || noPermission"
          ngbTooltip="{{ 'PM.SEARCH' | translate }}"
          [openDelay]="300"
          container="body"
          placement="auto">
          <svg
            *ngIf="!searchSpinner"
            width="14"
            height="14">
            <use href="./../assets/fonts/added-icon.svg#tabler-icons-filter-search"></use>
          </svg>
          <span
            *ngIf="searchSpinner"
            class="spinner-border spinner-border-sm"
            role="status"
            aria-hidden="true"></span>
        </button>
      </div>
    </div>
    <!-- condition list -->
    <div
      *ngIf="conditionList['metricParameters'].length != 0 "
      class="mt-1"
      id="paramTableContainer">
      <div class="table-responsive overflow-y-hidden">
        <table
          class="table table-hover table-bordered"
          style="height: 30px;">
          <tbody class="paramTable">
            <tr *ngFor="let item of conditionList['metricParameters'];let i = index">
              <!-- toggle -->
              <ng-container *ngIf="i === 0 || conditionList['metricParameters'][i-1].operator !== item.operator">
                <td
                  *ngIf="conditionList['metricParameters'].length>1"
                  rowspan="2"
                  style="width: 5%;padding: 5px 2rem;">
                  <button
                    *ngIf="conditionList.operator == 'And'"
                    (click)="changeOperator('Or',conditionList)"
                    type="button"
                    [disabled]="readOnly"
                    class="btn btn-outline-info round btn-sm andOrBtn"
                    placement="top"
                    container="body"
                    ngbTooltip="Click to toggle to OR between Parameter"
                    rippleEffect>
                    AND
                  </button>
                  <button
                    *ngIf="conditionList.operator == 'Or'"
                    (click)="changeOperator('And',conditionList)"
                    type="button"
                    [disabled]="readOnly"
                    class="btn btn-outline-warning round btn-sm andOrBtn"
                    placement="top"
                    container="body"
                    ngbTooltip="Click to toggle to AND between Parameter"
                    rippleEffect>
                    OR
                  </button>
                </td>
              </ng-container>
              <!-- classify -->
              <td
                class="paramTableTrashLeftTd "
                style="padding: 5px 2rem;">
                <div
                  class="cursor-pointer text-truncate text-primary"
                  [hidden]="item.isEditValue"
                  (click)="editParam(i,conditionList)">
                  {{mappingName(item.name,selectPMParamName)?mappingName(item.name,selectPMParamName):item.name}}{{mappingName(item.condition,selectCondition)}}{{item.value}}
                </div>
                <div [hidden]="!item.isEditValue">
                  <div class="d-flex align-items-center">
                    <!-- edit classify -->
                    <div
                      class="mr-1"
                      style="width: 300px;">
                      <span>PM {{ 'PM.PARAMNAME' | translate }}</span>
                      <ng-select
                        class="column-select-filter ng-select-size-sm"
                        appendTo="body"
                        [(ngModel)]="editClassify"
                        [items]="selectPMParamName"
                        placeholder="PM {{ 'PM.PARAMNAME' | translate }}"
                        bindLabel="name"
                        groupBy="group"
                        [clearable]="false"
                        (change)="classifyChange($event,'editManualParamPath');">
                      </ng-select>
                    </div>
                    <!-- edit param path -->
                    <div
                      class="mr-1"
                      style="width: 200px;">
                      <span>{{ 'PM.PARAMPATH' | translate }}</span>
                      <input
                        #paramPath
                        [(ngModel)]="editParamPath"
                        type="text"
                        trim
                        placeholder="{{ 'PM.PARAMPATH' | translate }}"
                        class="form-control form-control-sm"
                        [disabled]="!showEditManualParamPath">
                    </div>
                    <!-- edit condition -->
                    <div
                      class="mr-1"
                      style="width: 100px;">
                      <span>{{ 'PM.CONDITION' | translate }}</span>
                      <ng-select
                        class="column-select-filter ng-select-size-sm"
                        appendTo="body"
                        [(ngModel)]="conditionMapping[editCondition]"
                        [items]="selectCondition"
                        placeholder="{{ 'PM.CONDITION' | translate }}"
                        bindLabel="name"
                        [clearable]="false">
                      </ng-select>
                    </div>
                    <!-- edit param value -->
                    <div
                      class="mr-1"
                      style="width: 150px;">
                      <span>{{ 'PM.PARAMVALUE' | translate }}</span>
                      <input
                        #paramValue
                        [(ngModel)]="editParamVlaue"
                        type="text"
                        trim
                        placeholder="{{ 'PM.PARAMVALUE' | translate }}"
                        class="form-control form-control-sm">
                    </div>
                    <button
                      type="button"
                      class="btn btn-icon btn-flat-success"
                      style="margin: 0 5px 0 5px; padding: 8px 10px 8px 8px;"
                      rippleEffect
                      (click)="saveEditParam(i,conditionList,editClassify,conditionMapping[editCondition],paramPath.value,paramValue.value);">
                      <div [data-feather]="'check'"></div>
                    </button>
                    <button
                      type="button"
                      class="btn btn-icon btn-flat-danger"
                      style="margin: 0;padding: 8px 10px 8px 8px;"
                      rippleEffect
                      (click)="cancelEditParam(i,conditionList)">
                      <div [data-feather]="'x'"></div>
                    </button>
                  </div>
                </div>
              </td>
              <td
                class="paramTableTrashTd"
                style="width: 5%;padding: 5px 2rem;">
                <button
                  (click)="delCondition(i,conditionList)"
                  ngbTooltip="{{ 'PM.DELETECONDITION' | translate }}"
                  container="body"
                  [disabled]="readOnly"
                  class="btn icon-btn btn-sm tableActionButton"
                  rippleEffect>
                  <span
                    [data-feather]="'trash-2'"
                    class="text-primary"></span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <ngx-datatable
    #tableRowDetails
    appDatatableRecalculate
    [datatable]="tableRowDetails"
    [rows]="performanceReportList"
    [rowHeight]="37"
    class="bootstrap core-bootstrap"
    [limit]="selectedOption"
    [columnMode]="ColumnMode.standard"
    [headerHeight]="45"
    [footerHeight]="45"
    [scrollbarH]="true"
    [scrollbarV]="true"
    [selectionType]="SelectionType.checkbox"
    (select)="onSelect($event)"
    (resize)="onResize($event)">
    <!-- detail content -->
    <ngx-datatable-row-detail [rowHeight]="120">
      <ng-template
        let-row="row"
        let-expanded="expanded"
        ngx-datatable-row-detail-template>
        <div class="pl-3 pr-3">
          <div
            *ngIf="row.metricParameters.length != 0"
            class="mt-1"
            id="paramTableContainer">
            <div class="table-responsive overflow-y-hidden">
              <table
                class="table table-bordered"
                style="height: 30px;">
                <tbody class="paramTable">
                  <tr>
                    <td
                      style="width: 5%;padding: 5px 2rem; background-color:rgba(161, 162, 162, 0.2)"
                      *ngIf="row.metricParameters.length>1">
                      Operator
                    </td>
                    <td style="padding: 5px 2rem; background-color:rgba(161, 162, 162, 0.2)">Parameter Name (Path)</td>
                    <td style="padding: 5px 2rem; background-color:rgba(161, 162, 162, 0.2)">Condition</td>
                  </tr>
                  <tr *ngFor="let item of row.metricParameters;let i = index">
                    <ng-container *ngIf="i === 0 || row.metricParameters[i-1].operator !== item.operator">
                      <td
                        *ngIf="row.metricParameters.length>1"
                        rowspan="2"
                        style="width: 5%;padding: 5px 2rem;">
                        <div
                          *ngIf="row.operator == 'And'"
                          type="text"
                          trim
                          class="btn-outline-info round btn-sm"
                          rippleEffect>
                          AND
                        </div>
                        <div
                          *ngIf="row.operator == 'Or'"
                          type="text"
                          trim
                          class="btn-outline-warning round btn-sm"
                          rippleEffect>
                          OR
                        </div>
                      </td>
                    </ng-container>
                    <td
                      class="paramTableTrashLeftTd "
                      style="padding: 5px 2rem;">
                      <div class=" text-truncate ">
                        {{mappingGroup(item.classifyName,selectPMParamName)=='Manual'?item.name:item.classifyName}} ({{item.name}})
                      </div>
                    </td>
                    <td
                      class="paramTableTrashLeftTd "
                      style="padding: 5px 2rem;">
                      <div class=" text-truncate ">
                        {{mappingGroup(item.classifyName,selectPMParamName)=='Manual'?item.name:item.classifyName}} {{mappingName(item.condition,selectCondition)}} {{item.value}}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </ng-template>
    </ngx-datatable-row-detail>
    <!-- checkbox -->
    <ngx-datatable-column
      [width]="46"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        ngx-datatable-header-template
        let-value="value"
        let-allRowsSelected="allRowsSelected"
        let-selectFn="selectFn">
        <div class="custom-control custom-checkbox">
          <input
            type="checkbox"
            class="custom-control-input"
            [checked]="allRowsSelected"
            (change)="selectFn(!allRowsSelected)"
            id="headerChkbxRef">
          <label
            class="custom-control-label"
            for="headerChkbxRef"></label>
        </div>
      </ng-template>
      <ng-template
        ngx-datatable-cell-template
        let-rowIndex="rowIndex"
        let-value="value"
        let-isSelected="isSelected"
        let-onCheckboxChangeFn="onCheckboxChangeFn">
        <div class="custom-control custom-checkbox">
          <input
            type="checkbox"
            class="custom-control-input"
            [checked]="isSelected"
            (change)="onCheckboxChangeFn($event)"
            id="rowChkbxRef{{ rowIndex }}">
          <label
            class="custom-control-label"
            for="rowChkbxRef{{ rowIndex }}"></label>
        </div>
      </ng-template>
    </ngx-datatable-column>
    <!-- action -->
    <ngx-datatable-column
      name=" "
      headerClass="tableActionHead"
      cellClass="tableActionCell"
      [width]="30"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        let-column="column"
        ngx-datatable-header-template>
        <div
          #multiDrop="ngbDropdown"
          ngbDropdown
          class="d-inline-block"
          [autoClose]="'outside'"
          container="body">
          <button
            ngbDropdownToggle
            type="button"
            class="btn btn-icon btn-sm hide-arrow tableActionButton cursor-pointer text-primary"
            container="body"
            placement="auto"
            ngbTooltip="{{ 'DEVICES.SELECTACTION' | translate }}"
            rippleEffect>
            <span [data-feather]="'more-vertical'"></span>
          </button>
          <div ngbDropdownMenu>
            <app-pm-refresh
              mode="multiMetrics"
              [noPermission]="noPermission"
              [selectedMetrics]="selected"
              (refreshEvt)="refreshRows(selected)">
            </app-pm-refresh>
            <app-pm-refresh
              mode="allMetrics"
              [noPermission]="noPermission"
              [selectedMetrics]="performanceReportList"
              (refreshEvt)="reloadMetricList($event)">
            </app-pm-refresh>
            <app-pm-download
              mode="multiMetrics"
              [selectedMetrics]="selected"
              [table]="'performanceReportList'"
              (refreshEvt)="reloadMetricList($event)">
            </app-pm-download>
            <app-pm-download
              mode="allMetrics"
              [selectedMetrics]="performanceReportList"
              [table]="'performanceReportList'"
              (refreshEvt)="reloadMetricList($event)">
            </app-pm-download>
            <app-pm-delete
              mode="multiMetrics"
              [noPermission]="noPermission"
              [selectedMetrics]="selected"
              [noPermission]="noPermission"
              (refreshEvt)="reloadMetricList($event)">
            </app-pm-delete>
            <app-pm-delete
              mode="allMetrics"
              [noPermission]="noPermission"
              [performanceReportList]="performanceReportList"
              [noPermission]="noPermission"
              (refreshEvt)="reloadMetricList($event)">
            </app-pm-delete>
          </div>
        </div>
      </ng-template>
      <!-- detail -->
      <ng-template
        let-row="row"
        let-expanded="expanded"
        ngx-datatable-cell-template>
        <a
          href="javascript:void(0)"
          class="text-body tableActionButton"
          [class.datatable-icon-right]="!expanded"
          [class.datatable-icon-down]="expanded"
          title="{{ 'COMMON.EXPANDCOLLROW' | translate }}"
          (click)="rowDetailsToggleExpand(row);">
        </a>
      </ng-template>
    </ngx-datatable-column>
    <!-- notification btn -->
    <ngx-datatable-column
      name=" "
      headerClass="tableActionHead"
      cellClass="tableActionCell"
      [width]="30"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        let-row="row"
        ngx-datatable-cell-template>
        <button
          type="button"
          class="btn icon-btn btn-sm tableActionButton"
          ngbTooltip="{{ 'PM.NOTIFICATIONTOOLTIP' | translate }}"
          container="body"
          (click)="convertDeviceAlarm(modalEdit, row)"
          [disabled]="selected.length!=0||noAlarmPermission"
          rippleEffect>
          <span
            [data-feather]="'mail'"
            class="text-primary">
          </span>
        </button>
      </ng-template>
    </ngx-datatable-column>
    <!-- refresh btn -->
    <ngx-datatable-column
      name=" "
      headerClass="tableActionHead"
      cellClass="tableActionCell"
      [width]="30"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        let-row="row"
        ngx-datatable-cell-template>
        <app-pm-refresh
          mode="singleMetric"
          [row]="row"
          [noPermission]="noPermission"
          [selectedMetrics]="selected"
          (refreshEvt)="refreshRows(row) ">
        </app-pm-refresh>
      </ng-template>
    </ngx-datatable-column>
    <ng-container *ngFor="let col of tableOption">
      <!-- Name -->
      <ngx-datatable-column
        *ngIf="col.prop === 'name' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="name">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            class="form-control form-control-sm"
            placeholder="{{column.name | titlecase}}"
            [(ngModel)]="filterMap.name"
            (keyup)="filterInput()">
        </ng-template>
        <ng-template
          let-row="row"
          let-name="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="name"
            [placement]="'top'"
            [textClass]="'text-truncate text-primary cursor-pointer'"
            (click)="modalOpenSearchResult(modalSearchResult,'report',row,null);"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Search Result -->
      <ngx-datatable-column
        *ngIf="col.prop === 'deviceCount' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="deviceCount">
        <ng-template
          let-row="row"
          let-deviceCount="value"
          ngx-datatable-cell-template>
          <div
            ngbTooltip="{{ 'PM.RESULTTOOLTIP' | translate }}"
            container="body">
            <div
              class="avatar bg-light-info avatar-sm"
              style="cursor: default;">
              <span
                *ngIf="row.loading"
                class="avatar-content spinner-border"
                role="status">
                <span class="sr-only">Loading...</span>
              </span>
              <span
                *ngIf="!row.loading"
                class="avatar-content">
                {{row.deviceCount}}
              </span>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Target -->
      <ngx-datatable-column
        *ngIf="col.prop === 'serialNumber' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="serialNumber">
        <ng-template
          let-row="row"
          let-serialNumber="value"
          ngx-datatable-cell-template>
          <div
            *ngIf="row.serialNumber"
            class="d-flex ">
            <div
              class="avatar bg-light-info avatar-sm mr-50"
              style="cursor: default;">
              <span class="avatar-content">
                {{row.serialNumber ? row.serialNumber.length : 0}}
              </span>
            </div>
            <div class="text-truncate">
              <div
                *ngFor="let item of row.serialNumber"
                class="badge badge-pill badge-light-success mr-50"
                placement="right"
                container="body"
                ngbTooltip="{{ row.serialNumber.length>1 ? row.serialNumber : null }}">
                {{item}}
              </div>
            </div>
          </div>
          <div *ngIf="row.groupName">
            <div
              class="avatar bg-light-info avatar-sm mr-50"
              style="cursor: default;">
              <span class="avatar-content">
                {{row.groupMember ? row.groupMember : 0}}
              </span>
            </div>
            <div class="badge badge-light-warning">{{row.groupName ? row.groupName : ''}}</div>
          </div>
          <div *ngIf="!row.groupName && !row.serialNumber">All</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Conditions -->
      <ngx-datatable-column
        *ngIf="col.prop === 'searchRule' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="searchRule">
        <ng-template
          let-row="row"
          let-searchRule="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="searchRule"
            [placement]="'top'"
            [textClass]="'text-truncate cursor-pointer'"
            (click)="rowDetailsToggleExpand(row);"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- description -->
      <ngx-datatable-column
        *ngIf="col.prop === 'description' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="false"
        prop="description">
        <ng-template
          let-row="row"
          let-description="value"
          ngx-datatable-cell-template>
          <div
            class="text-truncate"
            placement="top"
            container="body"
            ngbTooltip="{{ description }}">
            {{description || '-'}}
          </div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Duration -->
      <ngx-datatable-column
        *ngIf="col.prop === 'duration' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="false"
        prop="duration">
        <ng-template
          let-row="row"
          let-duration="value"
          ngx-datatable-cell-template>
          <div>{{duration}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- From -->
      <ngx-datatable-column
        *ngIf="col.prop === 'beginTime' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="true"
        prop="beginTime">
        <ng-template
          let-row="row"
          let-beginTime="value"
          ngx-datatable-cell-template>
          <div>{{beginTime | date:'MM/dd/yy'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- To -->
      <ngx-datatable-column
        *ngIf="col.prop === 'endTime' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="true"
        prop="endTime">
        <ng-template
          let-row="row"
          let-endTime="value"
          ngx-datatable-cell-template>
          <div>{{endTime | date:'MM/dd/yy'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Created By -->
      <ngx-datatable-column
        *ngIf="col.prop === 'creator' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="true"
        prop="creator">
        <ng-template
          let-row="row"
          let-creator="value"
          ngx-datatable-cell-template>
          <div>{{creator || '-'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Update Time -->
      <ngx-datatable-column
        *ngIf="col.prop === 'updated' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        [sortable]="true"
        prop="updated">
        <ng-template
          let-row="row"
          let-updated="value"
          ngx-datatable-cell-template>
          <div>{{updated | date:'MM/dd/yy, HH:mm:ss'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Tracking -->
      <ngx-datatable-column
        *ngIf="col.prop === 'isAllowed' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="isAllowed">
        <ng-template
          let-row="row"
          let-rowIndex="rowIndex"
          let-isAllowed="value"
          ngx-datatable-cell-template>
          <div
            class="custom-control custom-control-primary custom-switch"
            style="min-height: 1.7rem;">
            <input
              type="checkbox"
              [checked]="isAllowed"
              (change)="allowTracking(row)"
              [disabled]="noPermission"
              class="custom-control-input"
              id="customSwitch{{rowIndex}}">
            <label
              class="custom-control-label"
              for="customSwitch{{rowIndex}}"></label>
          </div>
        </ng-template>
      </ngx-datatable-column>
    </ng-container>
    <!-- delete btn -->
    <ngx-datatable-column
      name=" "
      headerClass="tableActionHead"
      cellClass="tableActionCell"
      [width]="60"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        let-row="row"
        ngx-datatable-cell-template>
        <app-pm-delete
          mode="singleMetric"
          [row]="row"
          [noPermission]="noPermission"
          [selectedMetric]="row"
          (refreshEvt)="reloadMetricList($event)">
        </app-pm-delete>
      </ng-template>
    </ngx-datatable-column>
  </ngx-datatable>
</core-card>
<!-- search result Modal -->
<ng-template
  #modalSearchResult
  let-modal>
  <div class="modal-header">
    <h5
      class="modal-title"
      id="myModalLabel160">
      <span [ngClass]="{ 'modal-title-name': searchResultConditionList.name }">{{ 'PM.SEARCHRESULT' | translate }}</span>
      <span
        *ngIf="searchResultConditionList.name"
        class="badge badge-light-info mr-50 ml-75">
        {{searchResultConditionList.name}}
      </span>
      <button
      *ngIf="searchResultConditionList.name"
        type="button"
        style="padding: 2px;"
        class="btn btn-icon btn-flat-primary"
        (click)="openSaveMetricModal(modalName,'update',searchResultConditionList)"
        rippleEffect>
        <span [data-feather]="'edit'"></span>
      </button>
    </h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click');clearSelected()"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    *blockUI="'searchResultBlock'"
    #searchModal
    class="modal-body h-100"
    tabindex="0"
    ngbAutofocus>
    <!-- search bar -->
    <div
      #searchBarDiv
      class="border-dark mb-1 pl-50 pr-50 pb-50 pt-50 searchBar d-flex  "
      style="height: 70px;flex-direction: row;justify-content: space-between;align-items: center;">
      <!-- condition list -->
      <div
        *ngIf="searchResultConditionList['metricParameters']?searchResultConditionList['metricParameters'].length!=0?true:false:false"
        style="width: 70%;"
        class="mr-1">
        <span>{{ 'PM.CONDITION' | translate }}</span>
        <div
          class=" d-flex flex-wrap border"
          style="padding: 3px 5px;">
          <div
            *ngFor="let item of searchResultConditionList['metricParameters'];let i = index"
            class="d-flex">
            <div
              class="mr-1"
              style="display: flex; align-items: center;">
              <div
                *ngIf="searchResultConditionList.operator == 'And' && i != 0"
                type="text"
                trim
                class="btn-outline-info round btn-sm"
                rippleEffect>
                AND
              </div>
              <div
                *ngIf="searchResultConditionList.operator == 'Or' && i != 0"
                type="text"
                trim
                class="btn-outline-warning round btn-sm"
                rippleEffect>
                OR
              </div>
            </div>
            <div
              class="mr-1"
              style="display: flex;align-items: center;">
              {{mappingGroup(item.classifyName,selectPMParamName)=='Manual'?item.name:item.classifyName}} {{mappingName(item.condition,selectCondition)}} {{item.value}}
            </div>
          </div>
        </div>
      </div>
      <div
        class="d-flex flex-wrap justify-content-end"
        style="width: 30%;">
        <div
          class="mr-1"
          style="width: 150px;">
          <span>{{ 'PM.DURATION' | translate }}</span>
          <ng-select
            class="column-select-filter ng-select-size-sm"
            appendTo="body"
            [(ngModel)]="searchDuration"
            [items]="selectDuration"
            placeholder="{{ 'PM.DURATION' | translate }}"
            bindLabel="name"
            [clearable]="false"
            (change)="searchMetric(searchResultConditionList)">
          </ng-select>
        </div>
        <!-- re-search btn -->
        <div
          class="mr-1"
          style="display: flex;align-items: end;">
          <button
            (click)="searchMetric(searchResultConditionList)"
            type="button"
            class="btn btn-primary"
            style="padding: 10px 10px"
            ngbTooltip="{{ 'PM.RESEARCH' | translate }}"
            [openDelay]="300"
            [disabled]="noPermission"
            container="body"
            placement="auto">
            <svg
              width="14"
              height="14">
              <use href="./../assets/fonts/added-icon.svg#tabler-icons-filter-research"></use>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <div style="height: 400px">
      <ngx-datatable
        #searchResultListTableRowDetails
        [datatable]="searchResultListTableRowDetails"
        [rows]="merticRuleResult"
        [rowHeight]="40"
        class="bootstrap core-bootstrap"
        [limit]="selectedOption"
        [columnMode]="ColumnMode.force"
        [headerHeight]="45"
        [footerHeight]="50"
        [scrollbarH]="true"
        [scrollbarV]="true"
        [(selected)]="selectedDevice"
        [selectionType]="SelectionType.checkbox"
        (select)="onSelectDevice($event)">
        <!-- checkbox -->
        <ngx-datatable-column
          [width]="46"
          [sortable]="false"
          [canAutoResize]="false"
          [draggable]="false"
          [resizeable]="false">
          <ng-template
            ngx-datatable-header-template
            let-value="value"
            let-allRowsSelected="allRowsSelected"
            let-selectFn="selectFn">
            <div class="custom-control custom-checkbox">
              <input
                type="checkbox"
                class="custom-control-input"
                [checked]="allRowsSelected"
                (change)="selectFn(!allRowsSelected)"
                id="headerDeviceListChkbxRef">
              <label
                class="custom-control-label"
                for="headerDeviceListChkbxRef"></label>
            </div>
          </ng-template>
          <ng-template
            ngx-datatable-cell-template
            let-rowIndex="rowIndex"
            let-value="value"
            let-isSelected="isSelected"
            let-onCheckboxChangeFn="onCheckboxChangeFn">
            <div class="custom-control custom-checkbox">
              <input
                type="checkbox"
                class="custom-control-input"
                [checked]="isSelected"
                (change)="onCheckboxChangeFn($event)"
                id="rowDeviceListChkbxRef{{ rowIndex }}">
              <label
                class="custom-control-label"
                for="rowDeviceListChkbxRef{{ rowIndex }}"></label>
            </div>
          </ng-template>
        </ngx-datatable-column>
        <!-- action / view chart -->
        <ngx-datatable-column
          name=" "
          headerClass="tableActionHead"
          cellClass="tableActionCell"
          [width]="30"
          [sortable]="false"
          [canAutoResize]="false"
          [draggable]="false"
          [resizeable]="false">
          <!-- action -->
          <ng-template
            let-column="column"
            ngx-datatable-header-template>
            <div
              #multiDrop="ngbDropdown"
              ngbDropdown
              class="d-inline-block"
              [autoClose]="'outside'"
              container="body">
              <button
                ngbDropdownToggle
                type="button"
                class="btn btn-icon btn-sm hide-arrow tableActionButton cursor-pointer"
                container="body"
                placement="auto"
                ngbTooltip="{{ 'DEVICES.SELECTACTION' | translate }}"
                rippleEffect>
                <span
                  class="text-primary"
                  [data-feather]="'more-vertical'"></span>
              </button>
              <div ngbDropdownMenu>
                <!-- view chart multi -->
                <!-- <a
                (click)=" searchResultViewChart(modalChart,'multi',selectedDevice,searchResultConditionList)"
                href="javascript:void(0)"
                ngbDropdownItem
                class="d-flex align-items-center"
                [disabled]="selectedDevice.length==0">
                <i class="fa-solid fa-chart-line mr-50"></i>
                {{ 'PM.VIEWCHART' | translate }}
              </a> -->
                <!-- addon group -->
                <app-addon-group
                  mode="multiPMDevices"
                  [selectedDevices]="selectedDevice"
                  (addonGroupEvt)="getMetricList()"
                  (click)="closeDrop(multiDrop)">
                </app-addon-group>
                <!-- download multiMetrics -->
                <app-pm-download
                  mode="multiMetrics"
                  [selectedMetrics]="selectedDevice"
                  [ruleName]="searchResultConditionList.name"
                  [table]="'merticRuleResult'"
                  (refreshEvt)="reloadMetricList($event)">
                </app-pm-download>
                <!-- download allMetrics -->
                <app-pm-download
                  mode="allMetrics"
                  [selectedMetrics]="merticRuleResult"
                  [ruleName]="searchResultConditionList.name"
                  [table]="'merticRuleResult'"
                  (refreshEvt)="reloadMetricList($event)">
                </app-pm-download>
              </div>
            </div>
          </ng-template>
          <!-- view chart single -->
          <ng-template
            let-row="row"
            ngx-datatable-cell-template>
            <div>
              <button
                (click)="searchResultViewChart(modalChart,'single',row,searchResultConditionList)"
                type="button"
                class="btn icon-btn btn-sm hide-arrow tableActionButton"
                ngbTooltip="{{ 'PM.VIEWCHART' | translate }}"
                placement="top"
                container="body"
                rippleEffect>
                <svg
                  class="text-primary"
                  width="14px"
                  height="14px">
                  <use href="../../../../../assets/fonts/added-icon.svg#unicons-chart-line"></use>
                </svg>
              </button>
            </div>
          </ng-template>
        </ngx-datatable-column>
        <!-- serial number -->
        <ngx-datatable-column
          name="{{ 'PM.SERIALNUMBER' | translate }}"
          prop="serialNumber"
          [maxWidth]="200">
          <ng-template
            let-row="row"
            let-serialNumber="value"
            ngx-datatable-cell-template>
            <div
              class="text-primary cursor-pointer"
              (click)="gotoDeviceIInfo(serialNumber)">
              {{serialNumber}}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <!-- param -->
        <ng-container *ngFor="let param of merticRuleResultParamArr">
          <ngx-datatable-column
            name="{{param}} ({{'PM.LASTMACHING' | translate}})"
            [sortable]="false">
            <ng-template
              let-row="row"
              ngx-datatable-cell-template>
              <div>
                {{mappingValue(param,selectPMParamName)?row[mappingValue(param,selectPMParamName)]:row[param]}}
              </div>
            </ng-template>
          </ngx-datatable-column>
        </ng-container>
        <!-- time (beginTime~endTime) -->
        <ngx-datatable-column
          [width]="150"
          [canAutoResize]="false"
          [sortable]="true"
          name="{{ 'PM.LASTMACHINGTIME' | translate }}"
          prop="endTime">
          <ng-template
            let-row="row"
            let-endTime="value"
            ngx-datatable-cell-template>
            {{endTime | date:'MM/dd/yy, HH:mm'}}
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
  <div class="modal-footer d-flex">
    <button
      type="button"
      class="btn btn-primary mr-auto"
      placement="top"
      container="body"
      ngbTooltip="{{ 'PM.VIEWALLCHART' | translate }}"
      [disabled]="merticRuleResult.length==0 || selectedDevice.length>20 || selectedDevice.length==0"
      (click)="searchResultViewChart(modalChart,'multi',selectedDevice,searchResultConditionList)">
      {{ 'PM.CHART' | translate }}
    </button>
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Close click');initValue();clearSelected()">
      {{ 'PM.CLOSE' | translate }}
    </button>
    <button
      *ngIf="resultMode=='new'"
      type="button"
      class="btn btn-primary"
      (click)="openSaveMetricModal(modalName,'save',null,merticRuleResult)">
      {{ 'PM.SAVE' | translate }}
    </button>
    <button
      *ngIf="resultMode=='report'"
      type="button"
      class="btn btn-primary"
      (click)="updateMetric(searchResultConditionList,searchMetricRes)">
      {{ 'PM.SAVE' | translate }}
    </button>
  </div>
</ng-template>
<!-- chart Modal -->
<ng-template
  #modalChart
  let-modal>
  <div class="modal-header">
    <h5 class="modal-title w-100">
      <div class="d-flex justify-content-between align-items-center">
        <span>
          PM {{ 'PM.CHART' | translate }}
        </span>
        <div>
          <select 
            class="form-control mr-50"
            style="min-width: 80px;"  
            [(ngModel)]="measureType"
            (change)="changeMeasureType()">
            <option value="avg">AVG.</option>
            <option value="max">MAX.</option>
            <option value="min">MIN.</option>
          </select>
        </div>
      </div>
    </h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    *blockUI="'chartBlock'"
    tabindex="0"
    ngbAutofocus>
    <!-- chart -->
    <div class="d-flex flex-wrap">
      <div
        *ngFor="let item of series;let i = index"
        class="mb-1"
        [ngClass]="series.length > 1 ? 'col-6' : 'col-12'">
        <core-card [actions]="'[]'">
          <h4 class="card-title w-100">
            <div class="d-flex justify-content-between align-items-center">
              <span>
                {{mappingName(item[0].xname,selectPMParamName)? mappingName(item[0].xname,selectPMParamName):item[0].xname }}
              </span>
            </div>
          </h4>
          <div 
            *ngIf="series?.length && originalData?.length" 
            class="card-body">
            <app-pm-line-chart
              [path]="'analysis'"
              [title]="mappingName(item[0].xname,selectPMParamName)? mappingName(item[0].xname,selectPMParamName):item[0].xname"
              [chartData]="item"
              [originalData]="getOriginalByXname(item[0]?.xname)"
              [chartBeginTime]="searchResultConditionList.beginTime"
              [chartEndTime]="searchResultConditionList.endTime">
            </app-pm-line-chart>
          </div>
        </core-card>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Close click')">
      {{ 'PM.CLOSE' | translate }}
    </button>
  </div>
</ng-template>
<!-- save Modal -->
<ng-template
  #modalName
  let-modal>
  <div class="modal-header">
    <h4
      *ngIf="editMetricRuleNameMode=='save'"
      class="modal-title"
      id="myModalLabel1">
      {{ 'PM.SAVERULE' | translate }}
    </h4>
    <h4
      *ngIf="editMetricRuleNameMode=='update'"
      class="modal-title"
      id="myModalLabel1">
      {{ 'PM.UPDATERULE' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <form action="#">
      <label>{{ 'PM.NAME' | translate }}</label>
      <span class="text-warning ml-50">*</span>
      <div class="form-group">
          <input
            #inputMetricName
            [(ngModel)]="metricRuleName"
            type="text"
            trim
            name="metricRuleName"
            placeholder="Name"
            class="form-control form-control-sm"
            (keyup)="performanceReportNameCheck(metricRuleName)">
          <small *ngIf="performanceReportNameError" class="text-danger">
            {{ performanceReportNameError }}
          </small>
      </div>
      <label for="cpe-limit-vertical">{{ 'PM.DESCRIPTION' | translate }}</label>
      <!-- <fieldset class="form-group"> -->
      <textarea
        #inputMetricDescription
        [(ngModel)]="metricRuleDescription"
        class="form-control"
        name="{{ 'PM.DESCRIPTION' | translate }}"
        rows="3"
        [maxlength]="100"
        placeholder="{{ 'PM.DESCRIPTION' | translate }}"></textarea>
      <!-- </fieldset> -->
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="editMetricRuleNameMode=='save'?saveMetric(conditionList,searchMetricRes,metricRuleName,metricRuleDescription,editMetricRuleNameMode,modal):saveMetric(searchResultConditionList,searchMetricRes,metricRuleName,metricRuleDescription,editMetricRuleNameMode,modal)"
      type="button"
      class="btn btn-primary"
      [disabled]="!metricRuleName || performanceReportNameError"
      rippleEffect>
      {{ 'PM.SAVE' | translate }}
    </button>
  </div>
</ng-template>
<!-- notification Modal -->
<ng-template
  #modalEdit
  let-modal>
  <app-events-notification-edit-form
    class="modal-content"
    (dismissEvt)="dismiss($event, modal)"
    [pmNotificationData]="pmNotificationData"
    [editData]="editData"></app-events-notification-edit-form>
</ng-template>
<!-- progress Modal -->
<ng-template
  #progressModal
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      {{ 'PM.REFRESHLOADING' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <div class="progress-wrapper">
      <ngb-progressbar
        showValue="true"
        type="primary"
        [striped]="true"
        [height]="'2rem'"
        [value]="progress"></ngb-progressbar>
    </div>
  </div>
</ng-template>
