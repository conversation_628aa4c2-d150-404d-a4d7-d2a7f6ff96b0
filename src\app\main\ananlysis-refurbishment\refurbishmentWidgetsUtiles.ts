import { Widget } from 'app/layout/widget';
import { RefurbishmentHistoryComponent } from './refurbishment-history/refurbishment-history.component';
const refurbishmentWidgets: Widget[] = [
    {
        componentId: 'Analysis/RefurbishmentHistoryComponent',
        component: RefurbishmentHistoryComponent,
        name: 'ANALYSIS.REFURBISHMENETHISTORY',
        description: 'ANALYSIS.REFURBISHMENETHISTORY',
        class: 'analysis',
        subClass: 'refurbishStatistics',
        render: false,
        hidden: false,
        cols: 36,
        rows: 15,
        y: 0,
        x: 0,
        minItemCols: 18,
        minItemRows: 15,
        grade: '',
        dmsType: 'table',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    }
]

export { refurbishmentWidgets }