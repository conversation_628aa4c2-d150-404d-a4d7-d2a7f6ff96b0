import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';

import { BehaviorSubject, Observable } from 'rxjs';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { AuthenticationService } from 'app/auth/service';

@Injectable({
  providedIn: 'root'
})
export class AnalysisProvisioningService {
  // Public
  public availStatisticsProductNames: any;
  public onAvailStatisticsProductNamesChanged: BehaviorSubject<any>;

  constructor(
    private _httpClient: HttpClient,
    private _gridService: GridSystemService,
    private _authService: AuthenticationService
  ) {
    // Set the defaults
    this.onAvailStatisticsProductNamesChanged = new BehaviorSubject(null);
  }

  /**
   * Resolver
   *
   * @param {ActivatedRouteSnapshot} route
   * @param {RouterStateSnapshot} state
   * @returns {Observable<any> | Promise<any> | any}
   */
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
    return new Promise<void>((resolve, reject) => {
      if (this._authService.isAdmin) {
        this.getAnalysisProvisioningProductName().then(() => {
          resolve();
        }, (err) => { });
      } else {
        resolve();
      }
    });
  }

  /**
  * Get analysis-provisioning
  */
  getAnalysisProvisioningProductName(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/analysis/provisioningStatistics/availStatisticsProductNames`).subscribe({
        next: (response: any) => {
          const resWithAll = [...response];
          const sortedRes = resWithAll.sort((a, b) => {
            if (a.toLowerCase() < b.toLowerCase()) return -1;
            if (a.toLowerCase() > b.toLowerCase()) return 1;
            return 0;
          });
          this.availStatisticsProductNames = ["All", ...sortedRes];
          this.onAvailStatisticsProductNamesChanged.next(this.availStatisticsProductNames);
          resolve(this.availStatisticsProductNames);
        },
        error: reject
      });
    });
  }
  /**
  * Report analysis-provisioning
  */
  reportAnalysisProvisioning(productName: string, type: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/analysis/provisioningStatistics/${productName}/${type}/report`, {}).subscribe({
        next: (response: any) => {
          if (typeof response === 'string') {
            response = JSON.parse(response);
          }
          resolve(response);
        },
        error: reject
      });
    });
  }

  getSelectedProductName(id: string): any {
    return this._gridService.getComponentData(id)
  }

  saveSelectedProductName(id: string, data: any) {
    return this._gridService.setComponentData(id, data);
  }
}
