import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { TranslateService } from '@ngx-translate/core';
import { CareService } from 'app/main/care/care.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import Swal from 'sweetalert2';
import { AuthenticationService } from 'app/auth/service';

@UntilDestroy()
@Component({
  selector: 'app-upload-log',
  templateUrl: './upload-log.component.html',
  styleUrls: ['./upload-log.component.scss']
})
export class UploadLogComponent extends Unsubscribe implements OnInit {
  @Output() loadingEvt = new EventEmitter<string>();
  public loading: boolean = false
  public currentDeviceSN: string;
  public deviceId : string;
  @Input() editMode: boolean;
  public CONFIRMUPLOADLOG = this.translateService.instant('DEVICES.ACTION.CONFIRMUPLOADLOG');
  public DOUPLOADLOG = this.translateService.instant('DEVICES.ACTION.DOUPLOADLOG');
  public UPLOADLOG = this.translateService.instant('DEVICES.ACTION.UPLOADLOG');
  public UPLOADLOGFAIL = this.translateService.instant('DEVICES.ACTION.UPLOADLOGFAIL');
  public UPLOADLOGSUCCMESSAGE = this.translateService.instant('DEVICES.ACTION.UPLOADLOGSUCCMESSAGE');

  public UPLOAD = this.translateService.instant('DEVICES.UPLOAD');
  public LOG = this.translateService.instant('DEVICES.LOG');
  public assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
  public assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
  public assignOperationFail = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL');
  public assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
  public assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
  public assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
  public assignFailMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL_MESSAGE');
  public ACTION_CONFIRM = this.translateService.instant('DEVICES.ACTION.ACTION_CONFIRM');
  
  constructor(
    private translateService: TranslateService,
    private  _careService: CareService,
    private _toastrUtilsService: ToastrUtilsService,
    private _authenticationService: AuthenticationService,
  ) {
    super();
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CONFIRMUPLOADLOG = this.translateService.instant('DEVICES.ACTION.CONFIRMUPLOADLOG');
      this.DOUPLOADLOG = this.translateService.instant('DEVICES.ACTION.DOUPLOADLOG');
      this.UPLOADLOG = this.translateService.instant('DEVICES.ACTION.UPLOADLOG');
      this.UPLOADLOGFAIL = this.translateService.instant('DEVICES.ACTION.UPLOADLOGFAIL');
      this.UPLOADLOGSUCCMESSAGE = this.translateService.instant('DEVICES.ACTION.UPLOADLOGSUCCMESSAGE');
      this.ACTION_CONFIRM = this.translateService.instant('DEVICES.ACTION.ACTION_CONFIRM');
      this.UPLOAD = this.translateService.instant('DEVICES.UPLOAD');
      this.LOG = this.translateService.instant('DEVICES.LOG');
      this.assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
      this.assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
      this.assignOperationFail = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL');
      this.assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
      this.assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
      this.assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
      this.assignFailMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL_MESSAGE');
    })
  }

  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'deviceAdmin', 'write');
  }

  loadingChange(cal) {
    this.loadingEvt.emit(cal);
  }

  uploadLog(){
    this.loading = true
    this.loadingChange('true')
    Swal.fire({
      title: this.UPLOAD + this.currentDeviceSN + this.LOG,
      html: `${this.DOUPLOADLOG}<br>${this.ACTION_CONFIRM}`,
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._careService.uploadLog(this.deviceId).then(() => {
          this.crq(this.deviceId, this.currentDeviceSN)
        }).catch((res) => {
          if (res == 'OK') {
            this.crq(this.deviceId, this.currentDeviceSN)
          } else {
            this.loading = false
            this.loadingChange('false')
            this._toastrUtilsService.showErrorMessage(this.assignOperationFail, `${this.currentDeviceSN} ${this.assignFailMsg} \n ${res.error}`);
          }
        });
      } else {
        this.loading = false
        this.loadingChange('false')
      }
    });
  }

  crq(deviceId, sn) {
    this._careService.connectionRequest(deviceId).then((res) => {
      if (String(res) == 'true') {
        this.loading = false
        this.loadingChange('false')
        this._toastrUtilsService.showSuccessMessage(this.assignOperationSucc, `${sn}  ${this.assignSuccessMsg}`)
      } else {
        this.loading = false
        this.loadingChange('false')
        this._toastrUtilsService.showWarningMessage(this.assignOperation, `${this.assignWarningMsg1} ${sn} ${this.assignWarningMsg2}`);
      }
    }).catch((res) => {
      this.loading = false
      this.loadingChange('false')
      this._toastrUtilsService.showWarningMessage(this.assignOperation, `${this.assignWarningMsg1} ${sn} ${this.assignWarningMsg2}`);
    });
  }

  getDeviceId(): void {
    this.customSubscribe(this._careService.careDeviceChanged, res => {   
      if (res && res.id) {
        this.deviceId = res.id;
        if (res.serialNumber){
          this.currentDeviceSN = res.serialNumber; 
        }else{
          this.currentDeviceSN = res.deviceIdStruct.serialNumber;
        }
      }else{
        this.deviceId = '';
      }
    })
  }

  ngOnInit(): void {
    this.getDeviceId()
  }

}
