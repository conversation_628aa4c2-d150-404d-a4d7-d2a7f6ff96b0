import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, Input, OnInit } from '@angular/core';
import { DashboardService } from 'app/main/dashboard/dashboard.service';
import { Router } from '@angular/router';
import { timer, Subscription } from 'rxjs';


@UntilDestroy()
@Component({
  selector: 'app-group-numbers',
  templateUrl: './group-numbers.component.html',
  styleUrls: ['./group-numbers.component.scss']
})
export class GroupNumbersComponent implements OnInit {
  @Input() accessor: any;
  public totalGroups: any = 0;
  public loading: boolean;
  public iconloading: boolean = false;

  constructor(
    private _dashboardService: DashboardService,
    private _router: Router,
  ) {
    this.getTotalGroups();
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getTotalGroups();
        break;
    }
  }

  viewGroupList() {
    this.iconloading = true
    this._router.navigate(['/groups']);
  }

  getTotalGroups() {
    this.loading = true
    this._dashboardService.getTotalGroups().pipe(untilDestroyed(this)).subscribe({
      next: res => {
        this.totalGroups = res
      },
      error: error => {
        console.log(error);
      },
      complete: () => {
        this.loading = false
      }
    })
  }

  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getTotalGroups();
    });
  }

  ngOnDestroy(): void {
  }

}
