import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DataStoreService {
  public _queryParams: BehaviorSubject<any>;
  public _queryName: BehaviorSubject<any>;
  public deviceInfo: BehaviorSubject<any>;
  private dataStore: {
    queryParams: {}
    queryName: string
  };

  constructor() {
    this.dataStore = { queryParams: {}, queryName: "" };
    this._queryParams = new BehaviorSubject({});
    this._queryName = new BehaviorSubject({});
    this.deviceInfo = new BehaviorSubject({});
  }

  getQueryParams(path) {
    return this._queryParams.getValue()[path] || {};
  }

  setQueryParams(path, queryParams) {
    const param = {
      [path]: queryParams
    }
    this.dataStore.queryParams = param;
    this._queryParams.next(param);
  }

  getQueryName(path) {
    return this._queryName.getValue()[path] || {};
  }

  setQueryName(path, name) {
    this.dataStore.queryName = name;
    let newStore = Object.assign({}, this._queryParams.getValue(), { [path]: name });
    this._queryName.next(newStore);
  }

  setDeviceInfo(info: any) {
    this.deviceInfo.next(info);
  }

  getDeviceInfo(key?: string) {
    const info = this.deviceInfo.getValue() || {};
    return key ? info[key] : info;
  }

  updateDeviceInfo(param: any) {
    const info = this.deviceInfo.getValue() || {};
    const newInfo = Object.assign({}, info, param);
    this.deviceInfo.next(newInfo);
  }
}
