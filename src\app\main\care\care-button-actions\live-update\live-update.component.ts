import { Component, OnInit, Input, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { CareService } from 'app/main/care/care.service';
import { DeviceActionsService } from 'app/main/devices/device-actions/device-actions.service';
import { DataStoreService } from 'app/main/commonService/data-store.service';
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';
import { AuthenticationService } from 'app/auth/service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-live-update',
  templateUrl: './live-update.component.html',
  styleUrls: ['./live-update.component.scss']
})
export class LiveUpdateComponent extends Unsubscribe implements OnInit {
  @Output() loadingEvt = new EventEmitter<string>();
  
  @Input() editMode: boolean;
  public deviceId: string;
  public loading: boolean = false
  public currentDeviceSN: string;
  public protocol: string;
  public liveSuccess = this.translateService.instant('DEVICES.ACTION.LIVESUCCESS');
  public liveFail = this.translateService.instant('DEVICES.ACTION.LIVEFAIL');

  constructor(
    private translateService: TranslateService,
    private _careService: CareService,
    private _deviceActionsService: DeviceActionsService,
    private _dataStore: DataStoreService,
    private _toastrUtilsService: ToastrUtilsService,
    private _authenticationService: AuthenticationService,
  ) {
    super();
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
    this.liveSuccess = this.translateService.instant('DEVICES.ACTION.LIVESUCCESS');
    this.liveFail = this.translateService.instant('DEVICES.ACTION.LIVEFAIL');
    })
  }

  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'deviceAdmin', 'write');
  }
  loadingChange(cal) {
    this.loadingEvt.emit(cal);
  }
  getDeviceId(): void {
    this.customSubscribe(this._careService.careDeviceChanged, res => {
      if (res && res.id) {
        this.deviceId = res.id;
        this.protocol = res.protocol;
        if (res.serialNumber) {
          this.currentDeviceSN = res.serialNumber;
        } else {
          this.currentDeviceSN = res.deviceIdStruct.serialNumber;
        }
      } else {
        this.deviceId = '';
      }
    })
  }

  liveUpdateDevice() {
    this.loading = true
    this._deviceActionsService.connectionRequest(this.deviceId).then((res) => {
      if (String(res) == 'true') {
        if (['usp', 'netconf'].includes(this.protocol) && ['Inactive', 'Offline'].includes(this._dataStore.getDeviceInfo('status'))) {
          this._dataStore.updateDeviceInfo({ status: 'Online' });
        }
        this.loading = false
        this._toastrUtilsService.showSuccessMessage(this.liveSuccess, `S/N: ${this.currentDeviceSN}`)
      } else {
        if (['usp', 'netconf'].includes(this.protocol) && ['Active', 'Online'].includes(this._dataStore.getDeviceInfo('status'))) {
          this._dataStore.updateDeviceInfo({ status: 'Offline' });
        }
        this.loading = false
        this._toastrUtilsService.showErrorMessage(this.liveFail, `S/N: ${this.currentDeviceSN}`);
      }
    }).catch((res) => {
      this.loading = false
      this._toastrUtilsService.showErrorMessage(this.liveFail, res.error);
    });

  }

  ngOnInit(): void {
    this.getDeviceId()
  }
}
