import { Component, Input, OnInit } from '@angular/core';
import { ColumnMode } from '@almaobservatory/ngx-datatable';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { AnalysisProvisioningService } from 'app/main/analysis/analysis-provisioning/analysis-provisioning.service';

@Component({
  selector: 'app-online-device-status',
  templateUrl: './online-device-status.component.html',
  styleUrls: ['./online-device-status.component.scss']
})
export class OnlineDeviceStatusComponent extends Unsubscribe {
  @Input() accessor: any;
  public blockUIStatus = false;

  constructor(
    private _analysisProvisioningService: AnalysisProvisioningService,
  ) {
    super()
  }

  public componentId = "Analysis/" + this.constructor.name;
  public productNameSelect: any
  public selectedProductName = 'All'

  public selectedOption = 15;
  public ColumnMode = ColumnMode;
  public selected = [];

  public tableOnlineDatas = [];
  public pieOptions = [];
  public provisioningOnlineMappingName = {
    'Production Type': ['Production Unit', 'Non-Production Unit'],
    'XMPP Status': ['XMPP Connected', 'XMPP DisConnected'],
    'Firmware Status': ['Software Image Synchronized', 'Software Image UnSynchronized'],
    'IMS Registration Status': ['IMS Registered', 'IMS UnRegistered'],
    'SIM Status': ['SIM Connected ', 'SIM DisConnected'],
    'IPSec Tunnel Status': ['IPSec Connected', 'IPSec DisConnected'],
    'Has E911': ['Has E911 Address', 'E911 Address Missing'],

    'Battery InCharging Status': ['InCharging', 'Not Charging'],
    '2G Status': ['2.4G Up', '2.4G Down'],
    '5G Status': ['5G Up', '5G Down'],
    'Band Steering': ['Band Steering Enabled', 'Band Steering Disabled'],
    'Client Hosts Number': ['Less Than', 'More Than']
  };

  changeFn(selected) {
    if (selected === undefined) {
      selected = 'All';
    }
    this.selectedProductName = selected
    // console.log(this.selectedProductName);
    this._analysisProvisioningService.saveSelectedProductName(this.componentId, { productName: selected });
    this.reportAnalysisProvisioningRes(this.selectedProductName)
  }

  /**
    * get AnalysisProvisioning product name
    */
  getAnalysisProvisioningProductName() {
    this.customSubscribe(this._analysisProvisioningService.onAvailStatisticsProductNamesChanged, res=> {
      if (res === null) {
        this._analysisProvisioningService.getAnalysisProvisioningProductName().then()
      } else {
        this.productNameSelect = res;
      }
    })
  }
  /**
    * report AnalysisProvisioning
    */
  reportAnalysisProvisioningRes(productName) {
    this.blockUIStatus = true;
    this._analysisProvisioningService.reportAnalysisProvisioning(productName, 'online').then((res) => {
      // console.log(res)
      this.parseOnlineProvisionStatusData(res);
    }).finally(() => {
      this.blockUIStatus = false;
    });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.reportAnalysisProvisioningRes(this.selectedProductName)
        break;
    }
  };

  parseOnlineProvisionStatusData = function (data) {
    this.tableOnlineDatas = [];
    var m_onlineNum = data["Online Devices"];
    var xmppOnline = data["XMPP Connected"] || 0;
    var xmppOffline = m_onlineNum > xmppOnline ? m_onlineNum - xmppOnline : 0;
    this.tableOnlineDatas.push({
      name: "Online Devices",
      count: m_onlineNum,
      rate: this.takeTwoDecimalPercent(m_onlineNum, m_onlineNum)
    });
    this.tableOnlineDatas.push({
      name: "XMPP Connected",
      count: xmppOnline,
      rate: this.takeTwoDecimalPercent(xmppOnline, m_onlineNum)
    });
    this.tableOnlineDatas.push({
      name: "XMPP DisConnected",
      count: xmppOffline,
      rate: this.takeTwoDecimalPercent(xmppOffline, m_onlineNum)
    });

    // console.log(this.tableOnlineDatas);
  };

  takeTwoDecimalPercent = function (data, total) {
    if (total === 0 || data < 0) {
      return '0%'
    }
    if (data > total) {
      return '100%';
    }

    let percentage = Math.round(data * 10000 / total) / 100;
    if (total != data && percentage == 100) {
      return '99.99%';
    }
    if (data > 0 && percentage == 0) {
      return '<0.01%'
    }
    return percentage + '%';
  }

  ngOnInit(): void {
    this.componentId = this.accessor?.componentId;
    let data = this._analysisProvisioningService.getSelectedProductName(this.componentId);
    this.selectedProductName = data && data.productName || 'All';
    this.getAnalysisProvisioningProductName()
    this.reportAnalysisProvisioningRes(this.selectedProductName)
    // console.log(this.tableOnlineDatas)
  }

}
