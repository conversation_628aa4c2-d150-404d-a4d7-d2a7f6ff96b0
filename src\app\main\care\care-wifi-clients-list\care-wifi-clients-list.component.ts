import { Component, Input, OnDestroy, OnInit, ViewChild, ViewEncapsulation, AfterViewChecked, ChangeDetectorRef } from '@angular/core';
import { DatatableCustomizeService } from 'app/main/commonService/datatable-customize.service';
import { ColumnMode, DatatableComponent, SelectionType } from '@almaobservatory/ngx-datatable';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CareService } from 'app/main/care/care.service';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, Subject } from 'rxjs';
import { ResizeObservableService } from 'app/main/commonService/resize-observable.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { debounceTime } from 'rxjs/operators';
@UntilDestroy()
@Component({
  selector: 'app-care-wifi-clients-list',
  templateUrl: './care-wifi-clients-list.component.html',
  styleUrls: ['./care-wifi-clients-list.component.scss']
})
export class CareWifiClientsListComponent extends Unsubscribe implements OnInit, OnDestroy, AfterViewChecked {
  @Input() accessor: any;
  @ViewChild(DatatableComponent) tableRowDetails: DatatableComponent;
  public personalTheme;
  public blockUIStatus = false;
  public deviceId: string;

  public ColumnMode = ColumnMode;
  public selectedOption = 10;
  public rows: any = [];

  public selected = [];
  public tableOption = [
    { name: 'MAC', prop: 'MAC', translate: 'DEVICES.MAC', width: 160, flexGrow: 160, columnStatus: true },
    { name: 'RSSI(dBm)', prop: 'Signal', translate: 'DEVICES.RSSI', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'DownlinkRate', prop: 'DownlinkRate', translate: 'DEVICES.DOWNLINK_RATE', width: 120, flexGrow: 120, columnStatus: true },
    { name: 'UplinkRate', prop: 'UplinkRate', translate: 'DEVICES.UPLINK_RATE', width: 120, flexGrow: 120, columnStatus: true },
    { name: 'SSID', prop: 'SSID', translate: 'DEVICES.SSID', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'BSSID', prop: 'BSSID', translate: 'DEVICES.BSSID', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Band', prop: 'Band', translate: 'DEVICES.BAND', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Bandwidth', prop: 'Bandwidth', translate: 'DEVICES.BANDWIDTH', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Mode', prop: 'Mode', translate: 'DEVICES.MODE', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'AssociationTime', prop: 'AssociationTime', translate: 'DEVICES.CONNECTTIME', width: 140, flexGrow: 140, columnStatus: true },
  ];
  public tableWidth = 0;
  public gridScrollHeight = 0;
  public scrollSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  public tableOptionStatus = false;

  public mac: any;
  public tempData: any;
  public isShowNoData = true;

  public filterMap = {
    MAC: "",
    SSID: "",
    BSSID: ""
  }
  constructor(
    private modalService: NgbModal,
    private _careService: CareService,
    private route: ActivatedRoute,
    private _datatableCustomizeService: DatatableCustomizeService,
    private _resizeObservableService: ResizeObservableService,
    private cdr: ChangeDetectorRef
  ) {
    super();
    this.customSubscribe(route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  changeColumn(event): void {
    if (event.cloumns) {
      this.tableOption = [...event.cloumns];
    }
  }

  /**
 * For ref only, log selected values
 *
 * @param selected
 */
  onSelect({ selected }) {
    // console.log(selected)
    this.selected = selected;
  }

  filterInput(event, columnProp) {
    const val = event.target.value;
    this.filterMap[columnProp] = val
    const temp = this.tempData.filter(row => {
      const isMACMatch = row.MAC.toLowerCase().indexOf(this.filterMap.MAC.toLowerCase()) > -1 || !this.filterMap.MAC.toLowerCase()
      const isSSIDMatch = row.SSID.toLowerCase().indexOf(this.filterMap.SSID.toLowerCase()) > -1 || !this.filterMap.SSID.toLowerCase()
      const isBSSIDMatch = row.BSSID.toLowerCase().indexOf(this.filterMap.BSSID.toLowerCase()) > -1 || !this.filterMap.BSSID.toLowerCase()
      //  const isModeMatch = row.Mode.toLowerCase().indexOf(this.filterMap.Mode) > -1 || !this.filterMap.Mode
      return isMACMatch && isSSIDMatch && isBSSIDMatch;
    });
    this.rows = temp;
  }

  clearFilters() {
    // 重置 filterMap 來清空 input
    this.filterMap = {
      MAC: "",
      SSID: "",
      BSSID: ""
    };
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getDeviceClientListRes(this.deviceId)
        break;
    }
  };

  public chartData = [];
  openChartModal(modalChart, mac) {
    this.blockUIStatus = true;
    this.isShowNoData = false
    this.mac = mac
    this._careService.getLinkRateHistory(this.deviceId, mac).then(res => {
      // console.log(res)
      this.chartData = Object.values(res) ? Object.values(res)[0] : []
      // console.log(this.chartData)
    }).catch((err) => {
      console.error(err)
      this.isShowNoData = true
    }).finally(() => {
      this.blockUIStatus = false;
      this.isShowNoData = this.chartData.length > 0 ? false : true
      this.modalService.open(modalChart, {
        backdrop: "static",
        size: 'lg'
      });
    });
  }


  getDeviceClientListRes(deviceId) {
    this.blockUIStatus = true;
    this._careService.getClientsList(deviceId).pipe(untilDestroyed(this)).subscribe({
      next: res => {
        this.rows = res;
        this.tempData = this.rows;
      },
      error: error => {
        console.log(error);
      },
      complete: () => {
        this.blockUIStatus = false;
        this.clearFilters(); // 清空 filterMap
      }
    })
  }

  getDeviceId(): void {
    this._careService.careDeviceChanged.pipe(untilDestroyed(this)).subscribe(res => {
      if (res && res.id) {
        this.deviceId = res.id
        this.getDeviceClientListRes(this.deviceId)
      } else {
        this.rows = []
      }
    })
  }

  ngOnInit(): void {
    this.tableOptionStatus = !!this._datatableCustomizeService.getTableOption(this.accessor.componentId);
    this.tableOption = this._datatableCustomizeService.mergeOption(this.accessor.componentId, this.tableOption);
    const dueTime = this.tableOptionStatus ? 400 : 0;
    this.scrollSubject.pipe(untilDestroyed(this), debounceTime(dueTime)).subscribe(res => {
      if (res.gridScrollHeight && res.tableWidth && !this._resizeObservableService.windowResizeState) {
        this.tableOption = this._datatableCustomizeService.formatColumn(this.tableRowDetails, this.tableOption, this.tableWidth, this.accessor);
      }
    })
    this.getDeviceId()
  }

  /**
 * On destroy
 */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._careService.clientCountObservable = null
  }

  ngAfterViewChecked(): void {
    const { scrollHeight, innerWidth: tableWidth } = this.tableRowDetails.bodyComponent;
    const { scrollHeight: gridScrollHeight } = this.accessor.gridRef?.el;
    if (((gridScrollHeight && gridScrollHeight > this.gridScrollHeight) || (tableWidth && tableWidth !== this.tableWidth)) && scrollHeight) {
      this.tableWidth = tableWidth;
      this.gridScrollHeight = gridScrollHeight;
      this.scrollSubject.next({ gridScrollHeight, tableWidth });
    }
    this.cdr.detectChanges();
  }


}
