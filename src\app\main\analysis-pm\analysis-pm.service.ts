import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { BehaviorSubject, Observable, throwError, of, forkJoin, Subject, merge, interval } from 'rxjs';
import { map, catchError, tap, share, shareReplay, switchMap } from "rxjs/operators";
import { AuthenticationService } from 'app/auth/service';


@Injectable({
  providedIn: 'root'
})
export class AnalysisPmService {
  public personalThemeObservable: Observable<any>;

  constructor(
    private _httpClient: HttpClient,
    private authService: AuthenticationService
  ) {
  }

  /**
 * Resolver
 *
 * @param {ActivatedRouteSnapshot} route
 * @param {RouterStateSnapshot} state
 * @returns {Observable<any> | Promise<any> | any}
 */
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
    return new Promise<void>((resolve, reject) => {
      resolve();
    });
  }

  getPersonalTheme(): Observable<any[]> {
    if (this.personalThemeObservable) {
      return this.personalThemeObservable;
    } else {
      this.personalThemeObservable = this._httpClient.get(`nbi/addOn/personalTheme`).pipe(
        share(),
        catchError(error => throwError(() => error))
      );
      return this.personalThemeObservable;
    }
  }

  /**
  * Get device-list
  */
  getDeviceList(params): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/device/generalData/deviceList', params).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getDeviceGroupsList(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/device/groupAdmin/groupNameList').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getGroupInfo(groupId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/groupAdmin/${groupId}/groupInfo`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  postDeviceData(serialNumber: string, param: any): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/pmkpiCounter/devicePM?serial=${serialNumber}`, param).subscribe({
        next: (response: any) => resolve(response),
        error: reject
      });
    });
  }
  postDeviceDataByMeasureType(serialNumber: string, param: any, measureType = 'avg'): Promise<any[]> {
    const url = `nbi/v2/device/pmkpiCounter/devicePM?serial=${serialNumber}&measure=${measureType}`;
    return new Promise((resolve, reject) => {
      this._httpClient.post(url, param).subscribe({
        next: (response: any) => resolve(response),
        error: reject
      });
    });
  }


  postGroupData(groupId, param): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/groupAdmin/${groupId}/metricOverview`, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  postGroupDataByMeasureType(groupId, param, measureType = 'avg'): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const url = `nbi/v2/device/groupAdmin/${groupId}/metricOverview?measure=${measureType}`;
      this._httpClient.post(url, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getMetricList(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/pmStatistics/MetricRuleList').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getMetricRule(metricId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/analysis/pmStatistics/MetricRule/${metricId}`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  viewMetricRule(metricId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/analysis/pmStatistics/MetricRule/${metricId}/view`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  addMetric(param): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/analysis/pmStatistics/MetricRuleAdd`, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  updateMetric(metricId, param): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/analysis/pmStatistics/MetricRule/${metricId}`, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  deleteMetric(metricId: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.delete(`nbi/analysis/pmStatistics/MetricRule/${metricId}`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  deleteMultiMetric(metricIdArr): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.put('nbi/analysis/pmStatistics/MetricRuleList', metricIdArr).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  deleteAllMetric(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.delete('nbi/analysis/pmStatistics/MetricRuleList').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  searchMetric(param): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/analysis/pmStatistics/MetricRule/search`, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  searchMetricByMeasureType(param, measureType = 'avg'): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const url = `nbi/v2/analysis/pmStatistics/MetricRule/search?measure=${measureType}`;
      this._httpClient.post(url, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  private readonly SHOW_SEARCH_BAR_KEY = 'showSearchBar';

  getShowSearchBar(): boolean {
    const storedValue = localStorage.getItem(this.SHOW_SEARCH_BAR_KEY);
    return storedValue ? JSON.parse(storedValue) : true; // 默认为 true
  }

  setShowSearchBar(value: boolean): void {
    localStorage.setItem(this.SHOW_SEARCH_BAR_KEY, JSON.stringify(value));
  }


  // allowTracking(id: string, allow: boolean): Observable<any[]> {
  //   return this._httpClient.put(`pm/nbi/MetricRule/${id}/${!allow ? 'activate' : 'deactivate'}`, null)
  //     .pipe(map((resp: any) => resp),
  //       catchError(error => throwError(() => error))
  //     )
  // }

  allowTracking(id: string, allow: boolean): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/analysis/pmStatistics/MetricRule/${id}/${!allow ? 'activate' : 'deactivate'}`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  pmDeviceAlarm(param): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/pmkpiCounter/devicePM24Abn`, param).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  putResearch(): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.put(`nbi/analysis/pmStatistics/MetricRuleList/refresh`, null).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getExpiredCount(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/pmStatistics/MetricRuleList/expiredCount').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

}
