import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { BehaviorSubject, Observable, throwError, Subject } from 'rxjs';
import { map, catchError, tap, share, shareReplay } from "rxjs/operators";
import { rejects } from 'assert';
// @Injectable()
@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  /**
   * Constructor
   *
   * @param {HttpClient} _httpClient
   */
  public onDaysChanged: Subject<number>;
  public actAlarmObservable: Observable<any>;

  public statisticsOfTotalClientsData: any;
  public statisticsOfTotalClientsObservable: Observable<any>;
  constructor(
    private _httpClient: HttpClient,
  ) {
    this.onDaysChanged = new Subject();
  }

  // /**
  //  * Resolver
  //  *
  //  * @param {ActivatedRouteSnapshot} route
  //  * @param {RouterStateSnapshot} state
  //  * @returns {Observable<any> | Promise<any> | any}
  //  */
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
    //   return new Promise<void>((resolve, reject) => {
    //     resolve();
    //     Promise.all([
    //       this.getTotalDevices(),
    //       this.getAlarms(),
    //       this.getTotalUsers(),
    //       this.getProduct(),
    //     ]).then((res) => { }, (err) => { });;
    //   });
    // return this._groupListService.getDeviceGroupsList().subscribe()
  }

  getDaysItem() {
    return [{ key: 'DASHBOARD.AVGSESSIONS.LAST7DAYS', value: 7, icons: 'seven-days' }, { key: 'DASHBOARD.AVGSESSIONS.LAST15DAYS', value: 15, icons: 'fifteen-days' }, { key: 'DASHBOARD.AVGSESSIONS.LAST30DAYS', value: 30, icons: 'thirty-days' }]
  }

  /**
   * Get groups location
   */
  getallGroupAddress(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/networkLocation/allGroupAddress`).subscribe({
        next: (response: any) => {
          resolve(response)
        },
        error: reject
      })
    })
  }

  /**
   * Get statistics of total clients
   */
  getStatisticsOfTotalClients(days: number = 7): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/wifiSpecific/wifi/wifiAssociateDevInfo?days=${days}`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
   * Get dashboard-total-clients
   */
  getTotalClients(): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/device/wifiSpecific/wifi/count').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
   * Get dashboard-total-devices
   */
  getTotalDevices(deviceListType?): Promise<any> {
    let url = deviceListType ? `nbi/device/generalData/deviceAllCount?deviceListType=${deviceListType}` : 'nbi/device/generalData/deviceAllCount'
    return new Promise((resolve, reject) => {
      this._httpClient.get(url).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
   * Get dashboard-total-devices
   */
  getProduct(): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/device/generalData/registeredDeviceCount').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getOnlineProduct(): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/device/generalData/count/online').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
 * Get dashboard-total-users
 */
  getTotalUsers(): Promise<number> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/user/accountAdmin/onlineUserCount').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }


  /**
 * Get dashboard-total-alarms
 */
  getAlarms(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/alarm/deviceAlarm/deviceAlarmAllCount').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getActAlarms(): Observable<any[]> {
    if (this.actAlarmObservable) {
      return this.actAlarmObservable;
    } else {
      this.actAlarmObservable = this._httpClient.get('nbi/alarm/deviceAlarm/deviceActAlarmAllCount').pipe(
        share(),
        catchError(error => throwError(() => error))
      );
      return this.actAlarmObservable;
    }
  }


  /**
 * Get dashboard-total-groups
 */
  getTotalGroups(): Observable<any[]> {
    return this._httpClient.get('nbi/device/generalData/groupAllCount').pipe(tap((resp: any) => resp))
  }


  getUEcount(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/cellularSpecific/productNumOfUE`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getSystemInformation(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/system/systemInformation`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }


  getLocation(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/networkLocation/locationInfo`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }
}
