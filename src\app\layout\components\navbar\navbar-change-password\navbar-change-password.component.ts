import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { HttpClient } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';

@UntilDestroy()
@Component({
  selector: 'app-navbar-change-password',
  templateUrl: './navbar-change-password.component.html',
  styleUrls: ['./navbar-change-password.component.css']
})
export class NavbarChangePasswordComponent implements OnInit {
  @Output() dismissEvt = new EventEmitter<string>();
  @Input() modal: any;
  @Input() username: any;

  public userData:any = {
    name: "",
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  }

  public loading: boolean = false;
  public basicOldPwdShow: boolean = false;
  public basicNewPwdShow: boolean = false;
  public basicConfirmPwdShow: boolean = false;

  constructor (
    private _httpClient: HttpClient,
    private toastr: ToastrUtilsService,
    private translateService: TranslateService
  ) { }

  public FAIL = this.translateService.instant('DEVICES.ACTION.FAIL');
  public SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
  public ERROR = this.translateService.instant('DEVICES.ACTION.ERROR');

  ngOnInit() {
    this.userData.name = this.username;
  }

  onSubmit(form) {
    if (form.form && form.form.status === "VALID") {
      this.loading = true;
      return new Promise((resolve, reject) => {
        this._httpClient.put('nbi/common/user', this.userData, {'responseType': 'text'}).pipe(untilDestroyed(this)).subscribe({
          next: (response: any) => {
            resolve(response);
          },
          error: reject
        });
      }).then(res => {
        this.toastr.showSuccessMessage(this.SUCCESS, `User ${this.userData.name} change password successfully.`);
        this.dismissEvt.emit('Cross click');
      }, err => {
        this.toastr.showErrorMessage(this.ERROR, `User ${this.userData.name} change password failed.` + err.error);
      }).finally(() => {
        this.loading = false;
      });
    } else {
      return true;
    }
  }
}
