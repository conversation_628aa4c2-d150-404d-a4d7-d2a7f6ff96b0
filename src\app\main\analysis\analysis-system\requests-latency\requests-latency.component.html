<core-card
  [actions]="['reload', 'duration', 'maximize', 'download', 'measure']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  [selectedIcon]="selectedIcon"
  [dayItems]="dayItems"
  [selectedMeasure]="selectedMeasure"
  [measureItems]="measureItems"
  (events)="emittedEvents($event)">
  <h4
    class="card-title d-flex flex-fill justify-content-between align-items-center"
    style="width: calc(100% - 130px)">
    <span
      class="text-truncate"
      ngbTooltip="{{ accessor.name | translate }}"
      container="body">
      {{ accessor.name | translate }}
    </span>
    <div class="d-flex mr-75 header-icon">
      <!-- node -->
      <div
        ngbDropdown
        [placement]="'left-top'"
        container="body"
        class="d-flex align-items-center dropup dropdown-icon-wrapper shadow-none pointer">
        <div
          ngbDropdownToggle
          class="d-flex align-items-center shadow-none"
          ngbTooltip="{{ 'ALARMS.SELECTNODE' | translate }}"
          container="body"
          type="button"
          placement="bottom">
          <svg
            width="14px"
            height="14px"
            class="'dropdown-icon'">
            <use href="./../assets/fonts/added-icon.svg#node"></use>
          </svg>
        </div>
        <div
          ngbDropdownMenu
          class="dropdown-menu">
          <div
            *ngFor="let item of nodeItems"
            ngbDropdownItem
            class="draggable"
            (click)="changeNode(item)">
            {{ item }}
          </div>
        </div>
      </div>
      <!-- day -->
      <!-- <div
        ngbDropdown
        [placement]="'bottom'"
        container="body"
        class="btn-group d-flex align-items-center dropup dropdown-icon-wrapper shadow-none pointer">
        <div
          ngbDropdownToggle
          class="d-flex align-items-center shadow-none"
          ngbTooltip="{{ 'ANALYSIS.SELECTDURATION' | translate }}"
          container="body"
          placement="bottom">
          <svg
            width="14px"
            height="14px"
            class="dropdown-icon">
            <use [attr.href]="'./../assets/fonts/added-icon.svg#' + selectedIcon"></use>
          </svg>
        </div>
        <div
          ngbDropdownMenu
          class="dropdown-menu">
          <div
            *ngFor="let item of dayItems"
            ngbDropdownItem
            class="draggable"
            (click)="changeFn(item)">
            {{ item.key }}
          </div>
        </div>
      </div> -->
    </div>
  </h4>
  <div class="card-body card-transaction overflow-hidden">
    <div
      *ngIf="chartData"
      class="w-100 h-100"
      #requestLatencyRef>
      <app-line-echarts
        [chartRef]="requestLatencyRef"
        [chartData]="chartData"
        [isShowNoData]="isShowNoData"
        [chartGroup]="'analysis'"
        [title]="'SystemStatistics-RequestLatency'"
        [unit]="'ms'"
        [downloadData]="downloadData"
        [openModal]="openModalFlag"></app-line-echarts>
    </div>
  </div>
</core-card>
