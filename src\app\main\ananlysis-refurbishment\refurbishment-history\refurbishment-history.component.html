<core-card
  [actions]="['columnDragula', 'reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  [columnDragula]="tableOption"
  (events)="emittedEvents($event);"
  (changeColumn)="changeColumn($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <ngx-datatable
    #tableRowDetails
    appDatatableRecalculate
    [datatable]="tableRowDetails"
    [rows]="rows"
    [rowHeight]="37"
    class="bootstrap core-bootstrap"
    [limit]="selectedOption"
    [columnMode]="ColumnMode.force"
    [headerHeight]="50"
    [footerHeight]="45"
    [scrollbarH]="true"
    [scrollbarV]="true"
    [selected]="selected"
    [selectionType]="SelectionType.checkbox"
    (select)="onSelect($event)">
    <!-- checkbox -->
    <!-- <ngx-datatable-column
      [width]="50"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        ngx-datatable-header-template
        let-value="value"
        let-allRowsSelected="allRowsSelected"
        let-selectFn="selectFn">
        <div class="custom-control custom-checkbox">
          <input
            type="checkbox"
            class="custom-control-input"
            [checked]="allRowsSelected"
            (change)="selectFn(!allRowsSelected)"
            id="fileChkbxRef">
          <label
            class="custom-control-label"
            for="fileChkbxRef"></label>
        </div>
      </ng-template>
      <ng-template
        ngx-datatable-cell-template
        let-rowIndex="rowIndex"
        let-value="value"
        let-isSelected="isSelected"
        let-onCheckboxChangeFn="onCheckboxChangeFn">
        <div class="custom-control custom-checkbox">
          <input
            type="checkbox"
            class="custom-control-input"
            [checked]="isSelected"
            (change)="onCheckboxChangeFn($event)"
            id="fileRowChkbxRef{{ rowIndex }}">
          <label
            class="custom-control-label"
            for="fileRowChkbxRef{{ rowIndex }}"></label>
        </div>
      </ng-template>
    </ngx-datatable-column> -->
    <!-- action -->
    <ngx-datatable-column
      name=" "
      headerClass="tableActionHead"
      cellClass="tableActionCell"
      [width]="30"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        let-column="column"
        ngx-datatable-header-template>
        <div
          #multiDrop="ngbDropdown"
          ngbDropdown
          class="d-inline-block"
          [autoClose]="'outside'"
          container="body">
          <button
            ngbDropdownToggle
            type="button"
            disabled
            class="btn btn-icon btn-sm hide-arrow tableActionButton cursor-pointer"
            container="body"
            placement="auto"
            
            rippleEffect>
            <!-- <span
              [data-feather]="'more-vertical'"
              class="text-primary"></span> -->
          </button>
          <div ngbDropdownMenu>
            <a
              (click)="download();"
              href="javascript:void(0)"
              ngbDropdownItem
              class="d-flex align-items-center">
              <i
                data-feather="download-cloud"
                class="font-medium-1 mr-50 font-weight-bold"></i>
              {{ 'COMMON.DOWNLOAD' | translate}}
            </a>
          </div>
        </div>
      </ng-template>
      <ng-template
        let-row="row"
        ngx-datatable-cell-template>
      </ng-template>
    </ngx-datatable-column>
    <ng-container *ngFor="let col of tableOption">
      <!-- SerialNumber -->
      <ngx-datatable-column
        *ngIf="col.prop === 'serial' && col.columnStatus"
        name="{{ col.translate | translate }}"
        prop="SerialNumber"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            class="form-control form-control-sm"
            placeholder="{{column.name | titlecase}}"
            [(ngModel)]="queryParams.filter['serialNumber']"
            (keyup)="filterInputChange($event)">
        </ng-template>
        <ng-template
          let-row="row"
          let-SerialNumber="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="SerialNumber"
            [placement]="'right'"
            [textClass]="'text-truncate'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- OUI -->
      <ngx-datatable-column
        *ngIf="col.prop === 'OUI' && col.columnStatus"
        name="{{ col.translate | translate }}"
        prop="OUI"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false">
        <ng-template
          let-row="row"
          let-OUI="value"
          ngx-datatable-cell-template>
          <!-- <div
          popoverTitle="File type"
          [ngbPopover]="fileType"
          triggers="mouseenter:mouseleave"
          container="body"
          class="text-truncate">
          {{fileType}}
        </div> -->
          <app-beautify-content
            [content]="OUI"
            [placement]="'right'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- ProductName -->
      <ngx-datatable-column
        *ngIf="col.prop === 'productName' && col.columnStatus"
        name="{{ col.translate | translate }}"
        prop="productName"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false">
        <ng-template
          let-row="row"
          let-productName="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="productName"
            [placement]="'right'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- ProductClass -->
      <!-- <ngx-datatable-column
        *ngIf="col.prop === 'ProductClass' && col.columnStatus"
        name="{{ col.translate | translate }}"
        prop="ProductClass"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            class="form-control form-control-sm"
            [(ngModel)]="filterMap['ProductClass']"
            placeholder="{{column.name | titlecase}}"
            (keyup)="filterInput()">
        </ng-template>
        <ng-template
          let-row="row"
          let-ProductClass="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="ProductClass"
            [placement]="'right'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column> -->
      <!-- refurbishmentTime -->
      <ngx-datatable-column
        *ngIf="col.prop === 'refurbishmentTime' && col.columnStatus"
        name="{{ col.translate | translate }}"
        prop="refurbishmentTime"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false">
        <ng-template
          let-row="row"
          let-refurbishmentTime="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="refurbishmentTime | date:'MM/dd/yy, HH:mm:ss'"
            [placement]="'right'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Last Connected Time -->
      <ngx-datatable-column
        *ngIf="col.prop === 'lastConnected' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="lastConnected">
        <ng-template
          let-row="row"
          let-lastConnected="value"
          ngx-datatable-cell-template>
          <app-beautify-content [content]="lastConnected | date:'MM/dd/yy, HH:mm:ss'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Established Time -->
      <ngx-datatable-column
        *ngIf="col.prop === 'established' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="established">
        <ng-template
          let-row="row"
          let-established="value"
          ngx-datatable-cell-template>
          <app-beautify-content [content]="established | date:'MM/dd/yy, HH:mm:ss'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Tag -->
      <ngx-datatable-column
        *ngIf="col.prop === 'tags' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="tags"
        [sortable]="false">
        <ng-template
          let-row="row"
          let-tags="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="tags"
            [type]="'list'"
            [textClass]="'badge-light-success pointer'"
            [blank]="false"
            [icon]="'tag'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- Label -->
      <ngx-datatable-column
        *ngIf="col.prop === 'label' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        name="{{ col.translate | translate }}"
        prop="label"
        [sortable]="false">
       <ng-template
          let-row="row"
          let-label="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="[label]"
            [type]="'list'"
            [textClass]="label ? 'badge-light-warning pointer' : null"
            [blank]="false"
            [icon]="label ? 'bookmark' : null">
          </app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
    </ng-container>
    <!-- ngx-datatable-footer -->
    <ngx-datatable-footer>
      <ng-template ngx-datatable-footer-template>
        <div class="col-12 d-flex align-items-start">
          <div
            class="page-count"
            style="padding: 0;">
            <span>
              {{pageCount}} total
            </span>
          </div>
          <div class="pager-vertical-center d-flex">
            <datatable-pager
              #dataTablePagerComponent
              [pagerLeftArrowIcon]="'datatable-icon-left'"
              [pagerRightArrowIcon]="'datatable-icon-right'"
              [pagerPreviousIcon]="'datatable-icon-prev'"
              [pagerNextIcon]="'datatable-icon-skip'"
              [page]="queryParams.page"
              [size]="queryParams.size"
              [count]="pageCount"
              (change)="onFooterPage($event)">
            </datatable-pager>
          </div>
        </div>
      </ng-template>
    </ngx-datatable-footer>
  </ngx-datatable>
</core-card>
