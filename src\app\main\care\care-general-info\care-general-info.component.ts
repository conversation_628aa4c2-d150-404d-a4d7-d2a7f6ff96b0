import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input } from '@angular/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import moment from 'moment'
import { CareService } from 'app/main/care/care.service'

import{CalculatorWidthService} from 'app/main/commonService/calculator-width.service'
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { ActivatedRoute } from '@angular/router';
import { error } from 'console';
@UntilDestroy()
@Component({
  selector: 'app-care-general-info',
  templateUrl: './care-general-info.component.html',
  styleUrls: ['./care-general-info.component.scss']
})
export class CareGeneralInfoComponent extends Unsubscribe implements OnInit {

  @Input() accessor: any;
  public blockUIStatus = false;
  public generalInfoItems = []


  public componentWidth: number = 0;

  public deviceId: string;
  public selectedDeviceRow: any;



  constructor(
    private _careService: CareService,
    private _calculatorWidthService: CalculatorWidthService,
    private _gridSystemService: GridSystemService,
    private route: ActivatedRoute,
    
  ) {
    super();
    
   
  }

 

   /**
     * Check if response of reported data is data
     * @param value 
     * @returns 
     */
   checkInvalidDate(value) {
    if (typeof value == 'string') {
      return moment(value, 'YYYY-MM-DDTHH:mm:ss.SSSZ', true).isValid() ? true : moment(value, 'YYYY-MM-DDTHH:mm:ssZ', true).isValid() ? true : false
    }
  }

   /**
   * Uptime formatter
   * @param time 
   * @returns 
   */

  public careInfoNameMapping = {
    'label': 'Label',
    'tags': 'Tag',
    'deviceInfo.SerialNumber': 'Serial Number',
    'IMEI': 'IMEI',
    'deviceInfo.ProductClass': 'Product Class',
    'ModelName': 'Model Name',
    'deviceInfo.OUI': 'OUI',
    'deviceInfo.Manufacturer': 'Manufacturer',
    'MAC': 'MAC',
    'GlobalIPAddress': 'Global IP',
    'lanIp': 'Local IP',
    'FirstInstallTime': 'First Install Time',
    'lastInform': 'Last Inform',
    'SoftwareVersion': 'Software Version',
    'HardwareVersion': 'Hardware Version',
    'UpTime': 'UpTime',
    'CPUUsage': 'CPU Usage',
    'MemoryStatusTotal': 'Memory Status Total',
    'MemoryUsage': 'Memory Usage',
    // 'MemoryStatusFree': 'Memory Usage',
    // 'MemoryStatus': 'Memory Status',
    'Location': 'Location',
    'TimeZone': 'Time Zone',
    'CertificateExpiration': 'Certificate Expiration',
    'MSISDN': 'MSISDN',
    'ICCID': 'ICCID',
    // "ConnectStatus": "Battery Status",
    // "Temperature":"Battery Temperature",
    // "Level": "Battery Level",
    // "CycleCount": "Battery Cycle Count",
    // "InCharging": "Battery Charger State"
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getDeviceInfoRes(this.deviceId);
        break;
    }
  };

  findTag(res) {
    let tagArr = res.find(item => {
      return item.name == 'tags'
    }).reportedValue
    if (!tagArr || tagArr.length == 0) {
      return false
    } else {
      return true
    }
  }
  findLabel(res) {
    let label = res.find(item => {
      return item.name == 'label'
    }).reportedValue
    if (!label) {
      return false
    } else {
      return true
    }
  }
  formatUptime(time) {
    if (time) {
      var num = 0;
      var unit = '';
      if (time > 3600 * 24) {
        num = Math.floor(time / (3600 * 24));
        if (num > 1) {
          unit = 'days';
        } else {
          unit = 'day';
        }
      } else if (time > 3600) {
        num = Math.floor(time / 3600);
        if (num > 1) {
          unit = 'hours';
        } else {
          unit = 'hour';
        }
      } else if (time > 60) {
        num = Math.floor(time / 60);
        if (num > 1) {
          unit = 'minutes';
        } else {
          unit = 'minute';
        }
      } else {
        num = time;
        if (num > 1) {
          unit = 'seconds';
        } else {
          unit = 'second';
        }
      }
      return num + ' ' + unit;
    } else {
      return '0';
    }
  }

  getDeviceId(): void {
    this._careService.careDeviceChanged.pipe(untilDestroyed(this)).subscribe(res => {
          if (res && res.id) {
            this.deviceId = res.id
            this.getDeviceInfoRes(this.deviceId)
          }else{
            // debugger
            let baseArry = []
            baseArry = Object.keys(this.careInfoNameMapping).map(item => {
              return {
                "name": item, "reportedValue": ''
              }
            })
            this.generalInfoItems = baseArry.filter(item => {
              return item.name != 'MSISDN' && item.name != 'ICCID' && item.name != 'tags' && item.name != 'MemoryStatus' && item.name != 'MemoryStatusFree' && item.name != 'MemoryStatusTotal' && item.name != 'CPUUsage'
            })
            this.generalInfoItems = this.sortWidgets(this.generalInfoItems);
          }
      })

    }
  

   /**
 * get Device Info
 */
   public ifTag: boolean
   public ifLabel: boolean
   public label: any
  getDeviceInfoRes(deviceId) {
    this.blockUIStatus = true;
    this._careService.getDeviceInfo(deviceId).then((res: any) => {
      this.blockUIStatus = false;
      let memoryStatusTotal = 0
      let memoryStatusFree = 0
      let memoryStatus = ""
      res.forEach(item => {
        if (item.name == 'UpTime') {
          item.reportedValue = this.formatUptime(item.reportedValue)
        }
      })
      // console.log(res)

      this.generalInfoItems = res.filter(item => {
        return this.careInfoNameMapping[item.name] && item.name != 'MSISDN' && item.name != 'ICCID' && item.name != 'tags' && item.name != 'MemoryStatus' && item.name != 'MemoryStatusFree' && item.name != 'MemoryStatusTotal' && item.name != 'CPUUsage'
      })
      // console.log(this.generalInfoItems)

      this.ifTag = this.findTag(res)
      this.ifLabel = this.findLabel(res)

      this.label = res.find(item => {
        return item.name == 'label'
      }).reportedValue


      // reOrderResAccordingToMappingTable
      function reOrderResAccordingToMappingTable(arr, mapping) {
        let mappingArr = Object.entries(mapping);
        arr.sort(function (a, b) {
          let indexA = mappingArr.findIndex(([key, value]) => key === a.name);
          let indexB = mappingArr.findIndex(([key, value]) => key === b.name);
          if (indexA === -1) {
            indexA = mapping.length;
          }
          if (indexB === -1) {
            indexB = mapping.length;
          }
          return indexA - indexB;
        });
      }
      reOrderResAccordingToMappingTable(this.generalInfoItems, this.careInfoNameMapping)
      this.generalInfoItems = this.sortWidgets(this.generalInfoItems);
      // console.log(this.generalInfoItems)
    }).catch((err) => {
      this.blockUIStatus = false;
      console.log(err);
    })
  }

  sortWidgets(value: any[]): any[] {
    const order = ['deviceInfo.SerialNumber', 'IMEI', 'deviceInfo.ProductClass' , 'label', 'ModelName', 'deviceInfo.OUI', 'deviceInfo.Manufacturer', 'MAC', 'GlobalIPAddress', 'lanIp', 'SoftwareVersion', 'HardwareVersion', 'FirstInstallTime', 'lastInform', 'UpTime', 'TimeZone', 'CertificateExpiration']
    return value.sort((a, b) => {
      const indexA = order.indexOf(a.name);
      const indexB = order.indexOf(b.name);
      if (indexA === -1) return 1; // a.key not in order, place it at the end
      if (indexB === -1) return -1; // b.key not in order, place it at the end
      return indexA - indexB;
    });
  }

  //将当前宽度值传入进行对比
  // calculatorWidth() {
  //   // console.log('this.componentWidth=',this.componentWidth)
  //   return this._calculatorWidthService.calculatorWidth(this.componentWidth)
  //   }
  getWidth() {
    return this._calculatorWidthService.calculateWidth(this.componentWidth, this.generalInfoItems.length);
  }
  //视图初始化之后先获取当前的模块的宽度值
  ngAfterViewInit(): void {
    this.customSubscribe(this._gridSystemService.onGridsterItemComponentInfoChanged, res => {
      this.componentWidth = res['CareModule/CareGeneralInfoComponent'] ? res['CareModule/CareGeneralInfoComponent']['width'] : 0
      // console.log('this.componentWidth=',this.componentWidth)
    })
  }

  ngOnInit(): void {
    this.getDeviceId()
  }



}
