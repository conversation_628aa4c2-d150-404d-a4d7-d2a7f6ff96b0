import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';
import { TranslateModule } from '@ngx-translate/core';
import { CoreCommonModule } from '@core/common.module';
import { AuthGuard, ThemeStatus } from 'app/auth/helpers';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxDatatableModule } from '@almaobservatory/ngx-datatable';

import { SharedModule } from '../shared/shared.module';
import { ChartModule } from '../shared/chart/chart.module';
import { GridsterModule } from 'angular-gridster2';
import { CoreCardModule } from '@core/components/core-card/core-card.module';
import { EventsModule } from '../events/events.module';
import { DeviceModule } from '../shared/device/device.module';
import { PmChartModule } from '../shared/pm-chart/pm-chart.module';

import { PersonalThemeService } from '@core/services/personal-theme.service';

import { AnalysisPmComponent } from './analysis-pm.component';
import { PerformanceReportComponent } from './performance-report/performance-report.component';
import { PmDeleteComponent } from './pm-actions/pm-delete/pm-delete.component';
import { PmAddonGroupComponent } from './pm-actions/pm-addon-group/pm-addon-group.component';
import { PmRefreshComponent } from './pm-actions/pm-refresh/pm-refresh.component';
import { PmDownloadComponent } from './pm-actions/pm-download/pm-download.component'
import { AnalysisPmSharedModule } from './analysis-pm-shared.module';

const routes = [
  {
    path: '',
    component: AnalysisPmComponent,
    canActivate: [AuthGuard],
    canDeactivate: [ThemeStatus],
    data: { animation: 'AnalysisPmComponent' },
    resolve: { pts: PersonalThemeService }
  }
];

@NgModule({
  declarations: [
    AnalysisPmComponent,
    PerformanceReportComponent,
    PmDeleteComponent,
    PmAddonGroupComponent,
    PmRefreshComponent,
    PmDownloadComponent,
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ContentHeaderModule,
    TranslateModule,
    CoreCommonModule,
    SharedModule,
    ChartModule, // Needed for main analysis components
    GridsterModule, // Needed for dashboard layout
    CoreCardModule, // Needed for PerformanceReportComponent
    NgSelectModule, // Needed for PerformanceReportComponent
    NgxDatatableModule, // Needed for PerformanceReportComponent
    EventsModule, // Needed for event-related functionality
    DeviceModule, // Needed for device-related functionality
    AnalysisPmSharedModule, // Includes PmChartModule, BlockUIModule, etc.
    PmChartModule,
  ],
  exports: [
  ]
})
export class AnalysisPmModule { }
