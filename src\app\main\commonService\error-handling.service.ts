// error-handling.service.ts
import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UnauthorisedModalComponent } from 'app/main/shared/unauthorised-modal/unauthorised-modal.component';
import { BehaviorSubject } from 'rxjs';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlingService {
  private alreadyShown401Error = false;
  public alreadyShown401ErrorSource = new BehaviorSubject<boolean>(false);
  constructor(
    private modalService: NgbModal,
    private _router: Router, 
  ) { }

  handle401Error(errorDetails: any) {
    if (!this.alreadyShown401Error) {
      this.alreadyShown401Error = true;
      const modalRef = this.modalService.open(UnauthorisedModalComponent, { backdrop: 'static', centered: true, size: 'sm' });
      modalRef.componentInstance.errorDetails = errorDetails;
      modalRef.componentInstance.close = () => this.closeAllModals();
    }
  }

  closeAllModals() {
    if(!window.location.href.includes('/callback?token=')){
      this._router.navigate(['/pages/authentication/login-v2']);
    }
    this.modalService.dismissAll();
  }

  show401Error() {
    this.alreadyShown401ErrorSource.next(true);
  }
  
  hide401Error() {
    this.alreadyShown401ErrorSource.next(false);
  }
}