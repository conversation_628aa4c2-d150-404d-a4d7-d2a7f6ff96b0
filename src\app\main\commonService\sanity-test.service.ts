import { Injectable } from '@angular/core';
import xss from "xss";

@Injectable({
  providedIn: 'root'
})
export class SanityTestService {
  public validatorRuleMap = {
    //          'example': [
    //                  { required: true, not: '', msg: 'name is required and has a value'},
    //                  { type: 'string', msg: 'name should be string type'},
    //                  { length: [7,10], msg: 'name length should equal 7-10'},
    //                  { regex: "^[A-Za-z0-9_]+$", msg: 'name length should equal 7-10'},
    //                  { range: [0, 100], msg: 'v1 should be range 0 to 100'},      
    //                  {function: test ,msg: 'param can not pass func'}             
    //           ],
    'name': [
      { required: true, not: '', msg: 'name is required and has a value' },
      { type: 'string', msg: 'name should be string type' },
      { regex: /^[a-zA-Z0-9_\-\s]{0,64}$/, msg: 'Only 1-64 characters of letters, numbers, and special characters (_ - spaces) are allowed' }
    ],
    'groupName': [
      { required: true, not: '', msg: 'name is required and has a value' },
      { type: 'string', msg: 'groupName should be string type' },
      { regex: /^[a-zA-Z0-9_\-\s]{0,64}$/, msg: 'Only 1-64 characters of letters, numbers, and special characters (_ - spaces) are allowed' }
    ],
    'version': [
      { type: 'string', msg: 'version should be string type' },
      { regex: /^[a-zA-Z0-9_\-\s\.]{1,16}$/, msg: 'Only 1-16 characters of letters numbers and special symbols ( _ - .) allowed.' }
    ],
    'softwareVersion': [
      { type: 'string', msg: 'softwareVersion should be string type' },
      { regex: /^[a-zA-Z0-9_\-\s\.]{1,32}$/, msg: 'Only 1-32 characters of letters numbers and special symbols ( _ - .) allowed.' }
    ],
    'serialNumber': [
      { required: true, not: '', msg: 'serialNumber is required and has a value' },
      { type: 'string', msg: 'serialNumber should be string type' },
      { regex: /^[A-Za-z0-9_:]{1,64}$/, msg: 'only 1-64 characters of letters,numbers,_, : are allowed' }
    ],
    'configSerialNumber': [
      { type: 'string', msg: 'serialNumber should be string type' },
      { regex: /^[A-Za-z0-9_:]{1,64}$/, msg: 'only 1-64 characters of letters,numbers,_, : are allowed' }
    ],
    'OUI': [
      { required: true, not: '', msg: 'OUI is required and has a value' },
      { type: 'string', msg: 'OUI should be string type' },
      { regex: /^[a-fA-F0-9]{6}$/, msg: 'only 6 characters of hexadecimal letters and numbers are allowed' }
    ],
    'configOUI': [
      { type: 'string', msg: 'OUI should be string type' },
      { regex: /^[a-fA-F0-9]{6}$/, msg: 'only 6 characters of hexadecimal letters and numbers are allowed' }
    ],
    'productClass': [
      { required: true, not: '', msg: 'productClass is required and has a value' },
      { type: 'string', msg: 'productClass should be string type' },
      { regex: /^[^]{1,64}$/, msg: 'Only 1-64 characters are allowed' }
    ],
    'configProductClass': [
      { type: 'string', msg: 'productClass should be string type' },
      { regex: /^[^]{1,64}$/, msg: 'Only 1-64 characters are allowed' }
    ],
    'cpeLimit': [
      { required: true, not: '', msg: 'cpeLimit is required and has a value' },
      { regex: /^([1-9]\d{0,6}|10000000)$/, msg: 'It must be a number and less than 10 million' }
    ],
    'username': [
      { required: true, not: '', msg: 'Userame is required and has a value' },
      { type: 'string', msg: 'Userame should be string type' },
      { regex: /^[a-zA-Z0-9@!#?$/\\_\-\.]{0,128}$/, msg: 'only 0-128 characters of letters numbers and special symbols ( @ ! # ? $ / \ _ - .) allowed' }
    ],
    'password': [
      { required: true, not: '', msg: 'Password is required and has a value' },
      { type: 'string', msg: 'Password should be string type' },
      { regex: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,128}$/, msg: 'At least 8 characters, including at least one letter, one number and one special character' }
    ],
    'value': [
      { required: true, not: '', msg: 'value is required and has a value' },
      { type: 'string', msg: 'value should be string type' },
      { regex: /^[A-Za-z0-9!\*^%&',;=?$\x22]{8,128}$/, msg: 'only 8-128 characters of letters,numbers,*,^,%,&,\',,,;,=,?,$, \/ are allowed' }
    ],
    'url': [
      { required: true, not: '', msg: 'URL is required and has a value' },
      { type: 'string', msg: 'URL should be string type' },
      { length: [0, 256], msg: 'URL length should equal 0-256' },
      { regex: /^((([A-Za-z]{3,9}:(?:\/\/)?)(?:[\-;:&=\+\$,\w]+@)?[A-Za-z0-9\.\-]+|(?:www\.|[\-;:&=\+\$,\w]+@)[A-Za-z0-9\.\-]+)((?:\/[\+~%\/\.\w\-_]*)?\??(?:[\-\+=&;%@\.\w_]*)#?(?:[\.\!\/\\\w]*))?)/, msg: 'Invalid url, url must start with http, https, ftp or ftps' }
    ],
    'domain': [
      { required: true, not: '', msg: 'Domain is required and has a value' },
      { type: 'string', msg: 'Domain should be string type' },
      { length: [0, 256], msg: 'Domain length should equal 0-256' },
      { regex: /[a-zA-Z0-9][a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][a-zA-Z0-9]{0,62})+$/, msg: 'Invalid domain' }
    ],
    'mail': [
      { length: [0, 128], msg: 'Mail length should equal 0-128' },
      { regex: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/, msg: 'Invalid mail' }
    ],
    'port': [
      { required: true, not: '', msg: 'Port is required and has a value' },
      { regex: /^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-4])$/, msg: 'Port should be range 1 to 65534' }
    ],
    'timeout': [
      { required: true, not: '', msg: 'timeout is required and has a value' },
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid timeout' },
      { range: [10, 3600], msg: 'timeout should be range 10 to 3600' },
    ],
    'transaction_timeout': [
      { required: true, not: '', msg: 'timeout is required and has a value' },
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid timeout' },
      { range: [5, 600], msg: 'timeout should be range 5 to 600' },
    ],
    'request_timeout': [
      { required: true, not: '', msg: 'timeout is required and has a value' },
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid timeout' },
      { range: [5000, 600000], msg: 'timeout should be range 5000 to 600000' },

    ],
    'Address': [
      { required: true, not: '', msg: 'Domain is required and has a value' },
      { regex: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/, msg: 'IP address input error.' }
    ],
    'server_report_period': [
      { required: true, not: '', msg: 'Server Report Period is required and has a value' },
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid Server Report Period' },
      { range: [0, 30], msg: 'Server Report Period should be range 0 to 30' },
    ],
    'header': [
      { type: 'string', msg: 'header should be string type' },
      { regex: /^[a-zA-Z0-9_\-\s]{0,64}$/, msg: 'only 0-64 characters of letters,numbers,-,_ and spacesare allowed' }
    ],
    'number_repeats': [
      { required: true, not: '', msg: 'Number of Repeats is required and has a value' },
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid Number of Repeats' },
      { range: [1, 100], msg: 'Number of Repeats should be range 1 to 100' },
    ],
    'resource': [
      { required: true, not: '', msg: 'Resource is required and has a value' },
      { type: 'string', msg: 'Resource should be string type' },
      { length: [0, 64], msg: 'Resource length should equal 0-64' },
    ],
    'nodeUpgradePassword': [
      { required: true, not: '', msg: 'password is required and has a value' },
      { regex: /^.{1,68}$/, msg: 'password length should equal 1-68' }
    ],
    'DeviceOid': [
      { required: true, not: '', msg: 'DeviceOid is required and has a value' },
      { type: 'string', msg: 'DeviceOid should be string type' },
      { length: [0, 128], msg: 'DeviceOid length should equal 0-128' },
    ],
    'message': [
      { type: 'string', msg: 'message should be string type' },
      { length: [0, 256], msg: 'message length should equal 0-256' },
    ],
    'description': [
      { required: true, not: '', msg: 'description is required and has a value' },
      { type: 'string', msg: 'description should be string type' },
      { length: [0, 512], msg: 'description length should equal 0-512' },
    ],
    'objPath': [
      { type: 'string', msg: 'objPath should be string type' },
      { length: [0, 256], msg: 'objPath length should equal 0-256' },
    ],
    'deviceParameterName': [
      { type: 'string', msg: 'deviceParameterName should be string type' },
      { length: [0, 512], msg: 'deviceParameterName length should equal 0-512' },
    ],
    'deviceParameterValue': [
      { length: [0, 512], msg: 'deviceParameterValue length should equal 0-512' },
    ],
    'value-string': [
      { type: 'string', msg: 'value should be string type' },
      { length: [0, 512], msg: 'value length should equal 0-512' },
    ],
    'value-unsignedInt': [
      { regex: /^(?:0|[1-9][0-9]?|4294967295)$/, msg: 'value should be range 0 to 4294967295' }
    ],
    'value-int': [
      { required: true, not: '', msg: 'value is required and has a value' },
      { regex: /^([1-9][0-9]{0,1}|4294967295)$/, msg: 'value should be range 0 to 4294967295' }
    ],
    'acs_username': [
      { required: false, not: '', msg: 'username is required and has a value' },
      // { length: [0, 64], msg: 'username length should equal 0-64' },
      { regex: /^.{1,64}$/, msg: 'username length should equal 1-64' }
    ],
    'acs_password': [
      { required: false, not: '', msg: 'password is required and has a value' },
      // { length: [0, 128], msg: 'password length should equal 0-128' },
      { regex: /^.{1,128}$/, msg: 'password length should equal 1-128' }
    ],

    'ipv6_url': [
      { required: true, not: '', msg: 'URL is required and has a value' },
      { type: 'string', msg: 'URL should be string type' },
      { length: [0, 256], msg: 'URL length should equal 0-256' },
      { function: this.checkIpv6, msg: 'Invalid url, url must start with http, https, ftp or ftps' }
    ],
    'configurationName': [
      { type: 'string', msg: 'configurationName should be string type' },
      { length: [0, 64], msg: 'configurationName length should equal 0-64' },
    ],
    'monitoringName': [
      { type: 'string', msg: 'monitoringName should be string type' },
      { length: [0, 64], msg: 'monitoringName length should equal 0-64' },
    ],
    'vendorSpecificEvent': [
      { type: 'string', msg: 'customized event name should be string type' },
      { length: [0, 64], msg: 'customized event name length should equal 0-64' },
    ],
    'deviceLabel': [
      { type: 'string', msg: 'deviceLabel should be string type' },
      { regex: /^[a-zA-Z0-9_\-\.\s]{0,32}$/, msg: 'only 0-32 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'deviceTag': [
      { type: 'string', msg: 'deviceTag should be string type' },
      { regex: /^[a-zA-Z0-9_\-\.\s]{0,32}$/, msg: 'only 0-32 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'groupTag': [
      { type: 'string', msg: 'groupTag should be string type' },
      { regex: /^[a-zA-Z0-9_\-\.\s]{0,32}$/, msg: 'only 0-32 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'productTag': [
      { type: 'string', msg: 'productTag should be string type' },
      { regex: /^[a-zA-Z0-9_\-\.\s]{0,32}$/, msg: 'only 0-32 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'profileTag': [
      { type: 'string', msg: 'profileTag should be string type' },
      { regex: /^[a-zA-Z0-9_\-\.\s]{0,32}$/, msg: 'only 0-32 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'notificationTag': [
      { type: 'string', msg: 'notificationTag should be string type' },
      { regex: /^[a-zA-Z0-9_\-\.\s]{0,32}$/, msg: 'only 0-32 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'workflowTag': [
      { type: 'string', msg: 'workflowTag should be string type' },
      { regex: /^[a-zA-Z0-9_\-\.\s]{0,32}$/, msg: 'only 0-32 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'deviceHost': [
      { type: 'string', msg: 'URL should be string type' },
      { length: [0, 256], msg: 'URL length should equal 0-256' },
      { regex: /^((ht|f)tps?):\/\/[\w\-]+(\.[\w\-]+)+([\w\-.,@?^=%&:\/~+#]*[\w\-@?^=%&\/~+#])?$/, msg: 'Invalid url, url must start with http, https, ftp or ftps' }
    ],
    'devicePort': [
      { regex: /^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-4])$/, msg: 'Port should be range 1 to 65534' }
    ],
    'fileDescription': [
      { length: [0, 256], msg: 'description length should equal 0-256' },
    ],
    'fileName': [
      { regex: /^[a-zA-Z0-9()_\-\.\(\)\+\:\s]{0,64}$/, msg: 'only 0-64 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'isNormalString': [
      { regex: /^[a-zA-Z0-9_\-\s\.,]{0,512}$/, msg: 'only 0-512 characters of letters,numbers,-,_,. and spaces are allowed' }
    ],
    'isNormalText': [
      { regex: /^[a-zA-Z0-9_\-\s.,#*()\[\]\\\/=@!]{0,512}$/, msg: 'only 0-512 characters of letters, numbers, -, _, ., ,, #, *, (,), [, ], \\, /, =, @ and ! are allowed' }
    ],
    'isUsername': [
      { regex: /^[A-Za-z0-9@!#?$/\\_\-\.]{0,128}$/, msg: 'only 0-128 characters of letters numbers and special symbols ( @ ! # ? $ / \ _ - .) allowed' }
    ],
    'isPasswd': [
      { regex: /^[A-Za-z0-9\-!\*^%&@#Z',;=?$/\\_\s\.]{0,128}$/, msg: 'only 0-128 characters of letters numbers and special symbols ( * ^ % & , ; = @ ! # ? $ / \ _ - .) allowed' }
    ],
    'isStrongPasswd': [
      { regex: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,128}$/, msg: 'At least 8 characters, including at least one letter, one number and one special character' }
    ],
    'isURL': [
      { regex: /^((([A-Za-z]{3,9}|sftp):(?:\/\/)?)((?:[\-;:&=\+\$,\w]+@)?(?:[A-Za-z0-9\.\-]+|\[[A-Fa-f0-9:]+\])(?::\d{1,5})?|(?:www\.|[\-;:&=\+\$,\w]+@)(?:[A-Za-z0-9\.\-]+|\[[A-Fa-f0-9:]+\])(?::\d{1,5})?))((?:\/[\+~%\/\.\w\-_]*)?(:\/[\+~%\/\.\w\-_]*)?\??(?:[\-\+=&;%@\.\w_]*)#?(?:[\.\!\/\\\w]*))?$/, msg: 'Invalid Url' }
    ],
    'isDomain': [
      { regex: /^(([a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+)|(([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4}|([0-9A-Fa-f]{1,4}:){1,6}:([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4}|::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4}|([0-9A-Fa-f]{1,4}:){1,7}:))$/, msg: 'Invalid domain' }
    ],
    'isHost': [
      { regex: /^(([a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+)|(([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4}|([0-9A-Fa-f]{1,4}:){1,6}:([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4}|::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4}|([0-9A-Fa-f]{1,4}:){1,7}:))$/, msg: 'Invalid host' }
    ],
    'isNumberNonZero': [
      { regex: /^[1-9]\d*$/, msg: 'Invalid number' }
    ],
    'isNumber': [
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid number' },
    ],
    'logoutTimeoutNumber': [
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid number' },
      { range: [60, 86400], msg: 'Logout timeout range is from 60 to 86400' },
    ],
    'isPort': [
      { regex: /^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-4])$/, msg: 'Port should be range 1 to 65534' }
    ],
    'netconfRetryInterval': [
      { required: true, not: '', msg: 'Retry interval is required and has a value' },
      { regex: /^(0|([1-9][0-9]*))$/, msg: 'Invalid number' },
      { range: [60, 86400], msg: 'Retry interval should be range 60 to 86400' },
    ],
    'dir': [
      { required: true, not: '', msg: 'dir is required and has a value' },
      { regex: /^.{1,128}$/, msg: 'Matches any string between 1 and 128 characters in length' }
    ],
    'isNumbers_0_60': [
      { regex: /^(0|[1-5]?\d|60)$/, msg: 'Only integers between 0 and 60 can be entered' }
    ],
    'pmMertricRuleName': [
      { regex: /^[a-zA-Z0-9\-_ ]{1,64}$/, msg: 'Only 1-64 characters of letters,numbers,-,_ are allowed' }
    ],
    'label': [
      { required: true, not: '', msg: 'Label is required' },
      { regex: /^[a-zA-Z0-9\-_. ]{1,64}$/, msg: 'Only 1-64 characters of letters,numbers,-,_,. are allowed' }
    ],
    'hashKeyRequired': [
      { required: true, not: '', msg: 'HashKey is required and has a value' },
      { regex: /^[0-9a-f]{128}$/, msg: 'HashKey must 128 length' }
    ],
  };
  constructor() { }

  checkType(data, type) {
    switch (type) {
      case "string":
        return 'string' == typeof data;
      case "boolean":
        return 'boolean' == typeof data;
      case "number":
        return 'number' == typeof data;
      case "object":
        return 'object' == typeof data;
      case "array":
        return ('object' == typeof data) && (data instanceof Array);
    }
    return false;
  };

  checkLength(data, mLength) {
    if ('number' == typeof mLength) {
      if (('string' == typeof data) && data.length == mLength) {
        return true;
      }
    } else if (('object' == typeof mLength) && (mLength instanceof Array)) {
      if (('string' == typeof data) && data.length >= mLength[0] && data.length <= mLength[1]) {
        return true;
      }
    }
    return false;
  };

  checkRange(data, mRange) {
    data = data * 1;
    if (('object' == typeof mRange) && (mRange instanceof Array)) {
      if (('number' == typeof data) && data >= mRange[0] && data <= mRange[1]) {
        return true;
      }
    }
    return false;
  };

  checkIpv6(data) {
    let pFlag = false
    let kFlag = false
    if (data.indexOf("http:") == 0 || data.indexOf("https:") == 0 || data.indexOf("ftp:") == 0) {
      pFlag = true
    }
    if (data.indexOf("[") > 0 && data.indexOf("]") > 0) {
      kFlag = true
    }
    let Flag = 0
    if (pFlag == true && kFlag == true) {
      Flag = 1
    }
    switch (Flag) {
      case 1:
        break;
      default:
        return false
    }

    let str = data.slice(data.indexOf("[") + 1, data.indexOf("]"));
    return str.match(/:/g).length <= 7
      && /::/.test(str)
      ? /^([\da-f]{1,4}(:|::)){1,6}[\da-f]{1,4}$/i.test(str)
      : /^([\da-f]{1,4}:){7}[\da-f]{1,4}$/i.test(str);
  }

  checkFunction(data, func) {
    if ('function' == typeof func && func(data)) {
      return true;
    }
    return false;
  };

  check(name, data_obj) {
    var has_err = false;
    var rule_datas = this.validatorRuleMap[name];
    for (var i in rule_datas) {
      var rule_data = rule_datas[i];
      for (var item in rule_data) {
        var m_rule_element = rule_data[item];
        switch (item) {
          case "required":
            if (data_obj == null) {
              has_err = true;
            }
            break;
          case "type":
            // console.log("type:" + m_rule_element);
            if (false == this.checkType(data_obj, m_rule_element)) {
              has_err = true;
            }
            break;
          case "length":
            if (false == this.checkLength(data_obj, m_rule_element)) {
              has_err = true;
            }
            break;
          case "range":
            if (false == this.checkRange(data_obj, m_rule_element)) {
              has_err = true;
            }
            break;
          case "not":
            if (data_obj === m_rule_element) {
              has_err = true;
            }
            break;
          case "regex":
            var regex = null;
            if ('object' == typeof m_rule_element) {
              regex = m_rule_element;
            } else if ('string' == typeof m_rule_element) {
              regex = new RegExp(m_rule_element)
            }

            if (regex && (false == regex.test(data_obj))) {
              has_err = true;
            }
            break;
          case "function":
            if (false == this.checkFunction(data_obj, m_rule_element)) {
              has_err = true;
            }
            break;
        }

        if (has_err) {
          return (rule_data.msg ? rule_data.msg : "invalid input");
        }
      };
    }
    return null;
  }


  isXssStr(context) {
    return context != xss(context)
  }
}
