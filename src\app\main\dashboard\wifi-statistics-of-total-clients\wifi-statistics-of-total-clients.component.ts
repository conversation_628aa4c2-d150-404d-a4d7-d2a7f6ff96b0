import { Component, OnInit,Input,OnDestroy,ChangeDetectorRef } from '@angular/core';
import { DashboardService } from '../dashboard.service';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { TranslateService } from '@ngx-translate/core';
import { AnalysisDeviceService } from 'app/main/analysis/analysis-device/analysis-device.service';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment'


@Component({
  selector: 'app-wifi-statistics-of-total-clients',
  templateUrl: './wifi-statistics-of-total-clients.component.html',
  styleUrls: ['./wifi-statistics-of-total-clients.component.scss']
})
export class WifiStatisticsOfTotalClientsComponent extends Unsubscribe implements OnInit,OnDestroy {

  @Input() accessor: any;
  public dayItems: any[];
  public days: number;
  public personalTheme;
  public loading=false;
	public chartData=[]
    public isShowNoData=true
  selectedIcon: string = '';
  constructor(
    private _dashboardService:DashboardService,
    private _analysisDeviceService: AnalysisDeviceService,
    translateService: TranslateService,
    private _route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
  ) {
    super();
    this.dayItems = _dashboardService.getDaysItem().map(item => { return { key: translateService.instant(item.key), value: item.value, icons: item.icons } });
    this.days = this.dayItems[0].value;
    this.selectedIcon = this.dayItems[0]?.icons;
    this.customSubscribe(_dashboardService.onDaysChanged, days => {
      this.days = days;
      this.selectedIcon = this.dayItems.find(item => item.value === days)?.icons || '';
      this.getStatisticsOfTotalClients();
    })
    // this.getStatisticsOfTotalClients();
    // console.log(this.selectedIcon)
    // this.getTotalClients()
    this.customSubscribe(_route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  public tooltip = [
    {
      RSSI: 'DASHBOARD.EXCELLENT',
      description: 'DASHBOARD.EXCELLENT_DESCRIPTION',
      type: ''
    },
    {
      RSSI: 'DASHBOARD.GOOD',
      description: 'DASHBOARD.GOOD_DESCRIPTION',
      type: ''
    },
    {
      RSSI: 'DASHBOARD.POOR',
      description: 'DASHBOARD.POOR_DESCRIPTION',
      type: ''
    }
  ]
    public filteredData
    public tempData

  changeFn(item: any) {
    this.selectedIcon = item.icons;
    this.days = item.value;
    this._dashboardService.onDaysChanged.next(this.days);
    
    // this.getStatisticsOfTotalClients()
  }


  ngOnInit(): void {
    this.getStatisticsOfTotalClients()
  }
//   public sum2g5gClientsWithTimeSeries

	public timeArray = [];


    getStatisticsOfTotalClients(){
        this.loading=true;
        this.isShowNoData = false
        // console.log(this.days)
        this.timeArray = []
    this._dashboardService.getStatisticsOfTotalClients(this.days).then(res=>{
        this.chartData=[];
      this.tempData=[];

        let deviceClientsWithTimeSeries = [];
        Object.values(res).forEach(function (item) {
            item.forEach(function (item2) {
                deviceClientsWithTimeSeries.push(item2)
            })
        })
        // console.log('deviceClientsWithTimeSeries', deviceClientsWithTimeSeries)

        let sum2g5gClientsWithTimeSeries = [];
        deviceClientsWithTimeSeries.forEach(function (item) {
            if (item.hasOwnProperty('2g') == false) {
                item['2g'] = { excellent: 0, good: 0, poor: 0 }
            };
            if (item.hasOwnProperty('5g') == false) {
                item['5g'] = { excellent: 0, good: 0, poor: 0 }
            };
            if (item.hasOwnProperty('6g') == false) {
                item['6g'] = { excellent: 0, good: 0, poor: 0 }
            };
            sum2g5gClientsWithTimeSeries.push(
                {
                    totalExcellent:
                        item['2g'].excellent +
                        item['5g'].excellent +
                        item['6g'].excellent,
                    totalGood:
                        item['2g'].good +
                        item['5g'].good +
                        item['6g'].good,
                    totalPoor:
                        item['2g'].poor +
                        item['5g'].poor +
                        item['6g'].poor,
                    established: item.established

                }
            )
        // }
        })
        // console.log('sum2g5gClientsWithTimeSeries', sum2g5gClientsWithTimeSeries)
        sum2g5gClientsWithTimeSeries =
            sum2g5gClientsWithTimeSeries.reduce(
                function (acc, cur) {
                    var prev = acc.find(
                        function (item) { return item.established
                            === cur.established
                        });
                    if (prev) {
                        prev.totalExcellent += cur.totalExcellent;
                        prev.totalGood += cur.totalGood;
                        prev.totalPoor += cur.totalPoor;
                    } else {
                        acc.push(cur);
                    }
                    return acc;
                }, []).sort(function (a, b) {
                    // console.log(a.time)
                    return new Date(a.established
                    ).getTime() - new Date(b.established
                    ).getTime();
                })
                            .map(function (item) {
                    return {
                        totalClients: { value: [new Date(new Date(item.established
                        )).getTime(), item.totalExcellent + item.totalGood + item.totalPoor] },
                        'Excellent RSSI': { value: [new Date(new Date(item.established
                        )).getTime(), item.totalExcellent] },
                        'Good RSSI': { value: [new Date(new Date(item.established
                        )).getTime(), item.totalGood] },
                        'Poor RSSI': { value: [new Date(new Date(item.established
                        )).getTime(), item.totalPoor] },
                                            
                    }
                })

        // console.log(sum2g5gClientsWithTimeSeries)

		
	    const names = ['Excellent RSSI', 'Good RSSI', 'Poor RSSI'];

        names.forEach(name => {
        
      const dataPoints = sum2g5gClientsWithTimeSeries.map(item => {
        const timestamp = item[name].value[0]
        const count = item[name].value[1];
        return [timestamp, count];
      });


      const data = {
        name: name,
        data: dataPoints,
        stack: "total",
        type: "bar",
        smooth: true,
        sampling: "lttb"
      };

      

      this.chartData.push(data);
		
  	    })
          let oneDay = 86400000;
          let dateToday = new Date(new Date().toLocaleDateString()).getTime();
          let dateStart = dateToday - oneDay * (this.days - 1);
          let timeArray = [];

      for (let i = 0; i < this.days; i++) {
          timeArray.push(dateStart + i * oneDay);
          this.timeArray.push(moment(dateStart + i * oneDay).format("MM/DD"));
      }

      this.tempData = this.chartData.map(series => ({
          // ...series,
          
          name: series.name,
          data: series.data.filter(([timestamp]) => timeArray.includes(new Date(new Date(timestamp).toLocaleDateString()).getTime())),
          stack: "total",
          type: "bar",
          smooth: true,
          sampling: "lttb"
      }));
      this.filteredData=this.tempData

      // console.log(this.filteredData)
      // console.log(timeArray)

        //   this.getDataInDuration();
      // console.log(this.chartData)
      this.isShowNoData = this.filteredData.length > 0 ? false : true
      this.cdr.detectChanges();

      this.loading = false;
    }, () => {
        this.loading = false;
      })
    }

    
    // getDataInDuration(){

            
    // }

    

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getStatisticsOfTotalClients();
        break;
      case 'maximize':
        this.triggerModal()
        break;
      case 'download':
        this.triggerDownloadData()
        break;
    
    }
  }

  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }

}
