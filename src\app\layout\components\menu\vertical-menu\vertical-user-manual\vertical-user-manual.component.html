<ng-container *ngFor="let item of menu">
  <!-- section -->
  <li
    core-menu-vertical-section
    *ngIf="item.type == 'section'"
    [item]="item"
    class="navigation-header"></li>

  <!-- item -->
  <li
    core-menu-vertical-item
    *ngIf="item.type == 'item'"
    [item]="item"
    [ngClass]="{ disabled: editMode || item.disabled === true, 'cursor-not-allowed': editMode }"
    [routerLinkActive]="!item.openInNewTab ? 'active' : ''"
    [routerLinkActiveOptions]="{ exact: item.exactMatch || false }">
        <span
      [routerLink]="item.openInNewTab ? [] : [item.url]"
      class="d-none"></span>
      </li>
  <!-- collapsible -->
  <li
    core-menu-vertical-collapsible
    *ngIf="item.type == 'collapsible'"
    [item]="item"
    [editMode]="editMode"
    class="nav-item has-sub"></li>
</ng-container>
