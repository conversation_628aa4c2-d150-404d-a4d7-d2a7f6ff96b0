<section class="modern-horizontal-wizard app-ecommerce-details">
  <form class="form form-vertical" #changeRebootNodeForm="ngForm">
    <div class="row">
      <!-- Hide user avatar -->
      <div class="col-md-12">
        <div class="form-group">
          <label class="form-label" for="basic-icon-default-uname">
            {{ 'USERS.AMPNODE' | translate }}
          </label>
          <ng-select
            [items]="nodesList"
            [clearable]="false"
            [closeOnSelect]="true"
            [searchable]="false"
            appendTo="body"
            bindLabel="acsNodeName"
            bindValue="acsNodeName"
            labelForId="product-vertical"
            name="product-vertical"
            required
            placeholder="{{ 'USERS.AMPNODE' | translate }}"
            [(ngModel)]="nodesName"
          >
          </ng-select>
        </div>
      </div>

      <div class="col-md-12">
        <div class="form-group">
          <label class="form-label" for="basic-icon-default-password">
            {{ 'USERS.SUPERADMINPASSWORD' | translate }}
          </label>
          <div class="input-group form-password-toggle mb-2">
            <input
              [type]="basicNewPwdShow ? 'text' : 'password'"
              trim="blur"
              class="form-control"
              id="basic-default-password1"
              placeholder="{{ 'USERS.SUPERADMINPASSWORD' | translate }}"
              name="user-new-password"
              [(ngModel)]="password"
              [class.error]="
                changeRebootNodeForm.submitted && RebootNodeRef.invalid
              "
              #RebootNodeRef="ngModel"
              [required]="true"
              aria-label="password"
              aria-describedby="basic-default-password1"
            />
            <!-- pattern="(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,128}" -->
            <div
              class="input-group-append"
              (click)="basicNewPwdShow = !basicNewPwdShow"
            >
              <span class="input-group-text cursor-pointer">
                <i
                  class="feather"
                  [ngClass]="{
                    'icon-eye-off': basicNewPwdShow,
                    'icon-eye': !basicNewPwdShow
                  }"
                ></i>
              </span>
            </div>
          </div>
          <span
            *ngIf="changeRebootNodeForm.submitted && RebootNodeRef.invalid"
            class="invalid-form"
          >
            <small
              class="form-text text-danger"
              *ngIf="RebootNodeRef.errors.required"
            >
              {{ "COMMON.FIELDREQUIRED" | translate }}
            </small>
          </span>
        </div>
      </div>
      <div class="col-12 d-flex justify-content-end">
        <button
          type="submit"
          class="btn btn-success btn-next"
          (click)="onSubmit(changeRebootNodeForm)"
          [disabled]="loading"
          rippleEffect
        >
          <span
            *ngIf="loading"
            class="spinner-border spinner-border-sm mr-1"
          ></span>
          <span class="align-middle d-sm-inline-block d-none">
            {{ "USERS.SUBMIT" | translate }}
          </span>
          <i class="align-middle ml-sm-25 ml-0"></i>
        </button>
      </div>
    </div>
  </form>
</section>
