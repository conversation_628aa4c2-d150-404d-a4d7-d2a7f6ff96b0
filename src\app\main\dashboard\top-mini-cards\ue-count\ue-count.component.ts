import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { timer, Subscription } from 'rxjs';
import { DashboardService } from '../../dashboard.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-ue-count',
  templateUrl: './ue-count.component.html',
  styleUrls: ['./ue-count.component.scss']
})
export class UeCountComponent extends Unsubscribe {
  @Input() accessor: any;
  public blockUIStatus = false;
  public title;
  public serialNumber;
  public UEcount = 0
  public personalTheme;

  constructor(
    private route: ActivatedRoute,
    private _dashboardService: DashboardService,
    private modalService: NgbModal,
    private translateService: TranslateService
  ) {
    super();
    this.getUEcount()
    this.customSubscribe(route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getUEcount();
        break;
    }
  };

  public chartData
  getUEcount() {
    this.blockUIStatus = true;
    this._dashboardService.getUEcount().then((res: any) => {
      // console.log(res)
      this.UEcount = res.num
      this.chartData = res.data.map(pd => {
        return { name: pd.product, value: pd.ueCount }
      })
    }, error => {
      console.log(error);
    }).finally(() => {
      this.blockUIStatus = false;
    });
  }

  // modal Open Backdrop Disabled
  modalOpenBD(modalBD) {
    this.modalService.open(modalBD, {
      backdrop: false,
      size: 'lg',
      scrollable: true
    });
  }

  ngOnInit(): void {
    this.title = this.translateService.instant(this.accessor.name);
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getUEcount();
    });
  }

  ngOnDestroy(): void {
  }

}
