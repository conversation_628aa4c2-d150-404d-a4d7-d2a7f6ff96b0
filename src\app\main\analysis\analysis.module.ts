import { NgModule } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';
import { TranslateModule } from '@ngx-translate/core';
import { CoreCommonModule } from '@core/common.module';
import { AuthGuard, ThemeStatus } from 'app/auth/helpers';

import { SharedModule } from '../shared/shared.module';
import { ChartModule } from '../shared/chart/chart.module'
import { EchartsModule } from '../shared/echarts/echarts.module'
import { GridsterModule } from 'angular-gridster2';
import { CoreCardModule } from '@core/components/core-card/core-card.module';

import { NgxDatatableModule } from '@almaobservatory/ngx-datatable';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar-portable';

// analysis-device
import { AnalysisDeviceComponent } from './analysis-device/analysis-device.component';
import { TotalDeviceComponent } from './analysis-device/total-device/total-device.component';
import { OnlineDeviceComponent } from './analysis-device/online-device/online-device.component';
import { NewDeviceComponent } from './analysis-device/new-device/new-device.component';
import { SessionsComponent } from './analysis-device/sessions/sessions.component';
import { EventCodeComponent } from './analysis-device/event-code/event-code.component';

// analysis-system
import { AnalysisSystemComponent } from './analysis-system/analysis-system.component';
import { SessionsDurationComponent } from './analysis-system/sessions-duration/sessions-duration.component';
import { SessionsRateComponent } from './analysis-system/sessions-rate/sessions-rate.component';
import { RequestsLatencyComponent } from './analysis-system/requests-latency/requests-latency.component';
import { RequestsRateComponent } from './analysis-system/requests-rate/requests-rate.component';
import { RequestsParsingComponent } from './analysis-system/requests-parsing/requests-parsing.component';
import { CpuUsageComponent } from './analysis-system/cpu-usage/cpu-usage.component';
import { FreeDiskComponent } from './analysis-system/free-disk/free-disk.component';
import { FreeMemoryComponent } from './analysis-system/free-memory/free-memory.component';
import { MemoryUtilizationComponent } from './analysis-system/memory-utilization/memory-utilization.component';
import { DiskUtilizationComponent } from './analysis-system/disk-utilization/disk-utilization.component';

// analysis-provicioning
import { AnalysisProvisioningComponent } from './analysis-provisioning/analysis-provisioning.component';
import { TotalDeviceStatusComponent } from './analysis-provisioning/total-device-status/total-device-status.component';
import { OnlineDeviceStatusComponent } from './analysis-provisioning/online-device-status/online-device-status.component';
import { SoftwareVersionDistributionComponent } from './analysis-provisioning/software-version-distribution/software-version-distribution.component';
import { ProvisioningCodeDistributionComponent } from './analysis-provisioning/provisioning-code-distribution/provisioning-code-distribution.component';
import { XmppStatusComponent } from './analysis-provisioning/xmpp-status/xmpp-status.component';
import { ImsRegistrationStatusComponent } from './analysis-provisioning/ims-registration-status/ims-registration-status.component';
import { SimStatusComponent } from './analysis-provisioning/sim-status/sim-status.component';
import { IpSecTunnelStatusComponent } from './analysis-provisioning/ip-sec-tunnel-status/ip-sec-tunnel-status.component';
import { DataBaseStatusComponent } from './analysis-system/data-base-status/data-base-status.component';
import { DataXmppStatusComponent } from './analysis-system/data-xmpp-status/data-xmpp-status.component';

// service
import { PersonalThemeService } from '@core/services/personal-theme.service';
import { DataPmStatusComponent } from './analysis-system/data-pm-status/data-pm-status.component';

//refurbishment
import { AnalysisRefurbishmentModule } from '../ananlysis-refurbishment/ananlysis-refurbishment.module';
import { AnanlysisRefurbishmentComponent } from '../ananlysis-refurbishment/ananlysis-refurbishment.component';


const routes = [
  {
    path: 'system',
    component: AnalysisSystemComponent,
    canActivate: [AuthGuard],
    canDeactivate: [ThemeStatus],
    data: { animation: 'AnalysisSystemComponent' },
    resolve: {
      pts: PersonalThemeService
    }
  },
  {
    path: 'device',
    component: AnalysisDeviceComponent,
    canActivate: [AuthGuard],
    canDeactivate: [ThemeStatus],
    data: { animation: 'AnalysisDeviceComponent' },
    resolve: {
      pts: PersonalThemeService
    }
  },
  {
    path: 'provisioning',
    component: AnalysisProvisioningComponent,
    canActivate: [AuthGuard],
    canDeactivate: [ThemeStatus],
    data: { animation: 'AnalysisProvisioningComponent' },
    resolve: {
      pts: PersonalThemeService
    }
  },
  {
    path: 'refurbishment',
    component: AnanlysisRefurbishmentComponent,
    canActivate: [AuthGuard],
    canDeactivate: [ThemeStatus],
    data: { animation: 'AnanlysisRefurbishmentComponent' },
    resolve: {
      pts: PersonalThemeService
    }
  },
];



@NgModule({
  declarations: [
    AnalysisSystemComponent,
    AnalysisDeviceComponent,
    AnalysisProvisioningComponent,
    TotalDeviceComponent,
    OnlineDeviceComponent,
    NewDeviceComponent,
    SessionsComponent,
    EventCodeComponent,
    SessionsDurationComponent,
    SessionsRateComponent,
    RequestsLatencyComponent,
    RequestsRateComponent,
    RequestsParsingComponent,
    CpuUsageComponent,
    FreeDiskComponent,
    TotalDeviceStatusComponent,
    OnlineDeviceStatusComponent,
    SoftwareVersionDistributionComponent,
    ProvisioningCodeDistributionComponent,
    XmppStatusComponent,
    ImsRegistrationStatusComponent,
    SimStatusComponent,
    IpSecTunnelStatusComponent,
    DataBaseStatusComponent,
    DataXmppStatusComponent,
    DataPmStatusComponent,
    FreeMemoryComponent,
    MemoryUtilizationComponent,
    DiskUtilizationComponent,
    AnanlysisRefurbishmentComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ContentHeaderModule,
    TranslateModule,
    CoreCommonModule,
    SharedModule,
    ChartModule,
    EchartsModule,
    GridsterModule,
    CoreCardModule,
    NgxDatatableModule,
    NgSelectModule,
    PerfectScrollbarModule,
    AnalysisRefurbishmentModule
  ],
  exports: [
    AnalysisSystemComponent,
    AnalysisDeviceComponent,
    AnalysisProvisioningComponent,
    TotalDeviceComponent,
    OnlineDeviceComponent,
    NewDeviceComponent,
    SessionsComponent,
    EventCodeComponent,
    SessionsDurationComponent,
    SessionsRateComponent,
    RequestsLatencyComponent,
    RequestsRateComponent,
    RequestsParsingComponent,
    CpuUsageComponent,
    FreeDiskComponent,
    TotalDeviceStatusComponent,
    OnlineDeviceStatusComponent,
    SoftwareVersionDistributionComponent,
    ProvisioningCodeDistributionComponent,
    XmppStatusComponent,
    ImsRegistrationStatusComponent,
    SimStatusComponent,
    IpSecTunnelStatusComponent,
    AnanlysisRefurbishmentComponent
  ]
})
export class AnalysisModule { }
