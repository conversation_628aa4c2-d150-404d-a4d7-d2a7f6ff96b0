import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, Subject } from 'rxjs';
import { environment } from 'environments/environment';
import { User } from 'app/auth/models';
import { ActivatedRoute } from "@angular/router";
import { map, catchError, tap, share, debounceTime, switchMap, shareReplay, finalize } from "rxjs/operators";
import { StorageService } from 'app/main/commonService/storage.service';
import { GetPmUrlService } from 'app/main/analysis-pm/get-pm-url.service';
import { GetExternalServerUrlService } from 'app/main/commonService/get-external-server-url.service'
import { AuthenticationService } from 'app/auth/service';

@Injectable({ providedIn: 'root' })
export class UserService {

  public defaultMap = {
    "Protocol": "protocol",
    "Notification": "notification",
    "Reports": "reports",
    "Log": "log",
    "Events": "events",
    "Statistics": "StatisticsComponent",
    "Scheduling": "scheduling",
    "Xmpp": "XMPPComponent",
    "Advanced": "AdvancedComponent",
    "ConnectivityTest": "SpeedTestComponent",
    "GeoLocation": "GeoLocation",
    "Script": "ScriptsComponent",
    "CSR": "CSR",
    "VendorNodes": "VendorNodes",
    "DataCollection": "DataCollection",
    "Group": "GroupListComponent",
    "Product": "ProductProfilesComponent",
    "Workflow": "WorkFlowsComponent",
    "Monitoring": "EventsNotificationComponent",
    "UserAccount": "UserAccountComponent",
    "Capacity": "Capacity",
    "LogRetention": "LogRetention",
    "StatisticsRetention": "StatisticsRetention",
    "Roles": "AclRoleComponent",
    "5GC": "5GC",
    "NIDS": "NIDS",
    "CBSD": "CBSD",
    "5GM": "5GM"
  }
  public authority = {}
  public onAuthorityChanged: BehaviorSubject<any>;

  public getInforms = null;
  public validForDate = true;
  public Token = 1;
  public formatInformationChanged: BehaviorSubject<any>;
  /**
   *
   * @param {HttpClient} _http
   */
  constructor(
    private _http: HttpClient,
    private route: ActivatedRoute,
    private _storageService: StorageService,
    private _getPmUrlService: GetPmUrlService,
    private _getExternalServerUrlService: GetExternalServerUrlService,
    private _authenticationService: AuthenticationService,
  ) {
    this.onAuthorityChanged = new BehaviorSubject({});
    this.formatInformationChanged = new BehaviorSubject({});
  }

  //  public get currentUIValue(): any {
  //   return this.currentUISubject.value;
  // }

  check(className: string, subClassName: string, type: string = 'read'): boolean {
    return this._authenticationService.check(className, subClassName, type)
  }

  /**
   * Get all users
   */
  getAll() {
    return this._http.get<User[]>(`${environment.apiUrl}/users`);
  }

  /**
   * Get user by id
   */
  getById(id: number) {
    return this._http.get<User>(`${environment.apiUrl}/users/${id}`);
  }

  formatAddon(AddOn) {
    // AddOn = {
    //   // "5GC": [],
    //   "Advanced": [],
    //   "CSR": [],
    //   "Capacity": ********,
    //   "ConnectivityTest": [],
    //   "DataCollection": 50,
    //   "Events": ["system", "device"],
    //   "GeoLocation": [],
    //   "Group": 1000,
    //   "Log": ["session", "operation"],
    //   "LogRetention": 7,
    //   "Monitoring": 100,
    //   // "NIDS": [],
    //   "Notification": ["mail", "snmp"],
    //   "Product": 100,
    //   "Protocol": ["cwmp", "netconf", "callhome"],
    //   "Reports": ["summary", "server", "device"],
    //   "Roles": [],
    //   "Scheduling": [],
    //   "Script": [],
    //   "Statistics": [],
    //   "StatisticsRetention": 30,
    //   "UserAccount": 1000,
    //   "VendorNodes": ["X_TMOBILE"],
    //   "Workflow": ********,
    //   "Xmpp": [],
    //   "validForDate": true,
    //   "Token":0
    // }
    if (AddOn) {
      let data = {}
      Object.keys(AddOn).map((obj, idx) => {
        if (this.defaultMap.hasOwnProperty(obj)) {
          data[this.defaultMap[obj]] = Object.values(AddOn)[idx]
        }
      })
      this.authority = data
      this.onAuthorityChanged.next(this.authority)
      // localStorage.setItem('authority', JSON.stringify(this.authority));
      this._storageService.set('authority', this.authority);
    }
    return AddOn
  }

  private informationCache$: Observable<any> | null = null;
  getInformation(): Observable<any> {
    if (!this.informationCache$) {
      this.informationCache$ = this._http.get('nbi/common/information').pipe(
        map((resp: any) => {
          this._storageService.set('apiKey', resp.apiKey);
          this._storageService.set('locationType', resp.locationType);
          if (!resp.AddOn) {
            resp.AddOn = {};
          }
          resp.AddOn.validForDate = resp.validForDate;
          resp.AddOn.Token = resp.Token;
          this.formatInformationChanged.next(resp.AddOn);

          // console.log(this.formatAddon(resp.AddOn));
          return this.formatAddon(resp.AddOn);
        }),
        finalize(() => {
          // 清除快取以便後續的請求重新發送
          this.informationCache$ = null;
        }),
        catchError(error => {
          this.informationCache$ = null; // 發生錯誤時也清除快取
          return throwError(() => error);
        }),
        shareReplay(1) // 保留最近一次的回應，供同一時間的訂閱者使用
      );
    }
    return this.informationCache$;
  }

  checkLicenceKey(prop: string): Observable<boolean> {
    return this.getInformation().pipe(
      map((response: any) => {
        const lowerCaseProp = prop.toLowerCase();
        return Object.keys(response).some((key) => key.toLowerCase() === lowerCaseProp);
      })
    );
  }

  // checkLicenceKey(prop: [string, string?]): Observable<boolean> {
  //   const key = prop[0];
  //   const attribute = prop[1];
  //   return this.getInformation().pipe(
  //     map((response: any) => {
  //       const licence = response;
  //       if (!licence || !licence.hasOwnProperty(key)) {
  //         return false;
  //       }
  //       if (attribute) {
  //         return licence[key].includes(attribute);
  //       }
  //       return true;
  //     })
  //   );
  // }


  getPmUrl() {
    this._http.get('nbi/analysis/pmStatistics/getPMattributes').subscribe(resp => {
      let pmUrl = resp['telemetry.server.url'];
      this._getPmUrlService.changePMurl(pmUrl)
    })
  }

  getExternalServerUrl() {
    this._http.get('nbi/analysis/provisioningStatistics/getAttributes').subscribe(resp => {
      // console.log(resp)
      let type_5gc = resp['5gc.server.type'];
      let url_5gc = resp['5gc.server.url'];
      this._getExternalServerUrlService.change5GCurl('type', type_5gc)
      this._getExternalServerUrlService.change5GCurl('url', url_5gc)
    })
  }

  getServerTime(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._http.get('nbi/common/time', { responseType: "text" }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }


  pageController(attribute, navbar) {
    // this.authority = Object.keys(this.authority).length > 0 ? this.authority : JSON.parse(localStorage.getItem('authority')) || {}
    this.authority = Object.keys(this.authority).length > 0 ? this.authority : this._storageService.get('authority') || {}
    if (Array.isArray(this.authority[attribute])) {
      return this.authority[attribute].includes(navbar)
    } else {
      return !!this.authority[attribute]
    }
  }

  getPageAuthority(attribute) {
    // this.authority = Object.keys(this.authority).length > 0 ? this.authority : JSON.parse(localStorage.getItem('authority')) || {}
    this.authority = Object.keys(this.authority).length > 0 ? this.authority : this._storageService.get('authority') || {}
    return new Promise((resolve, reject) => {
      resolve(this.authority[attribute]);
    });
  }

  canActivateRoute(attribute) {
    // this.authority = Object.keys(this.authority).length > 0 ? this.authority : JSON.parse(localStorage.getItem('authority')) || {}
    this.authority = Object.keys(this.authority).length > 0 ? this.authority : this._storageService.get('authority') || {}
    return this.authority.hasOwnProperty(attribute)
  }

  removeUnauthorizedWidgets(widgets) {
    let path = this.route.snapshot['_routerState'].url
    let sessionDisplay = this.pageController("log", 'session')
    let operationLogDisplay = this.pageController("log", 'operation')
    let deviceReportLogDisplay = this.pageController("reports", 'device')
    let groupListDisplayInDashboardAndDeviceInfo = this.canActivateRoute("GroupListComponent")
    let deviceEventsListDisplayInDashboardAndDeviceInfo = this.pageController("events", "device")
    let systemEventsListDisplayInDashboard = this.pageController("events", "system")
    let XMPPDisplayInDashboard = this.canActivateRoute("XMPPComponent")
    let dataCollectionDisplayInDeviceinfo = this.canActivateRoute("DataCollection")
    let mailDisplayInPreference = this.pageController("notification", 'mail')
    let snmpDisplayInPreference = this.pageController("notification", 'snmp')
    let reportsDisplayInPreference = this.canActivateRoute("reports")
    let logsDisplayInPreference = this.canActivateRoute("log")
    let statisticsDisplayInPreferenceAndDashBoard = this.canActivateRoute("StatisticsComponent")
    let eventsDisplayInPreferenceAndDashBoard = this.canActivateRoute("events")
    let speedTestComponentDisplay = this.canActivateRoute("SpeedTestComponent")
    let geoLocationDisplay = this.canActivateRoute("GeoLocation")
    let uspDisplay = this.pageController("protocol", 'usp')
    let netconfDisplay = this.pageController("protocol", 'netconf')
    let summaryReportLogDisplay = this.pageController("reports", 'summary')
    let fiveGCDisplay = this.canActivateRoute("5GC")
    let NIDSDisplay = this.canActivateRoute("NIDS")
    let CBSDDisplay = this.canActivateRoute("CBSD")
    let workflowDisplay = this.canActivateRoute("WorkFlowsComponent")
    widgets.forEach(item => {
      // if (item.widgets) {
      item.widgets = item.widgets.filter((widget) => {
        if (path.endsWith("dashboard")) {
          return widgets.filter(item => {
            return (widget.componentId == 'GroupsModule/GroupListTableComponent' && groupListDisplayInDashboardAndDeviceInfo)
              || (widget.componentId == 'EventsModule/EventsDeviceListComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
              || (widget.componentId == 'DashboardModule/DashboardTotalAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
              || (widget.componentId == 'DashboardModule/DashboardAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
              || (widget.componentId == 'SystemModule/SystemEventsListComponent' && systemEventsListDisplayInDashboard)
              || (widget.componentId == 'Analysis/XmppStatusComponent' && XMPPDisplayInDashboard)
              || (widget.componentId == 'Analysis/OnlineDeviceStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'Analysis/ImsRegistrationStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'Analysis/SimStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'Analysis/IpSecTunnelStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'Analysis/SoftwareVersionDistributionComponent' && statisticsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'Analysis/ProvisioningCodeDistributionComponent' && statisticsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'Analysis/ProvisioningCodeDistributionComponent' && statisticsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'GroupsModule/DashboardCoverageMapComponent' && groupListDisplayInDashboardAndDeviceInfo)
              || (widget.componentId == 'DashboardModule/GroupNumbersComponent' && groupListDisplayInDashboardAndDeviceInfo)
              || (widget.componentId == 'GroupsModule/GorupListComponent' && groupListDisplayInDashboardAndDeviceInfo)
              || (widget.componentId == 'EventsModule/AlarmListComponent' && eventsDisplayInPreferenceAndDashBoard)
              || (widget.componentId == 'DashboardModule/GroupsLocationComponent' && groupListDisplayInDashboardAndDeviceInfo)
              // || (widget.componentId == 'DashboardModule/DashboardTotalAlarmCountComponent' && eventsDisplayInPreferenceAndDashBoard)
              // || (widget.componentId == 'DashboardModule/DashboardAlarmCountComponent' && eventsDisplayInPreferenceAndDashBoard)
              || ((widget.componentId != 'GroupsModule/GroupListTableComponent')
                && (widget.componentId != 'EventsModule/EventsDeviceListComponent')
                && (widget.componentId != 'SystemModule/SystemEventsListComponent')
                && (widget.componentId != 'Analysis/XmppStatusComponent')
                && (widget.componentId != 'Analysis/OnlineDeviceStatusComponent')
                && (widget.componentId != 'Analysis/ImsRegistrationStatusComponent')
                && (widget.componentId != 'Analysis/SimStatusComponent')
                && (widget.componentId != 'Analysis/IpSecTunnelStatusComponent')
                && (widget.componentId != 'Analysis/SoftwareVersionDistributionComponent')
                && (widget.componentId != 'Analysis/ProvisioningCodeDistributionComponent')
                && (widget.componentId != 'GroupsModule/DashboardCoverageMapComponent')
                && (widget.componentId != 'DashboardModule/GroupNumbersComponent')
                && (widget.componentId != 'DashboardModule/DashboardTotalAlarmCountComponent')
                && (widget.componentId != 'DashboardModule/DashboardAlarmCountComponent')
                && (widget.componentId != 'GroupsModule/GorupListComponent')
                && (widget.componentId != 'EventsModule/AlarmListComponent')
                && (widget.componentId != 'DashboardModule/GroupsLocationComponent')
              )
          })
        }

        if (path.endsWith("device-logs")) {
          return (widget.componentId == 'DevicesModule/SessionsLogComponent' && sessionDisplay)
            || (widget.componentId == 'DevicesModule/OperationsLogComponent' && operationLogDisplay)
            || (widget.componentId == 'DevicesModule/PendingOperationsLogComponent' && operationLogDisplay)
            || (widget.componentId == 'DevicesModule/ReportsLogComponent' && deviceReportLogDisplay)
            // || (widget.componentId == 'DevicesModule/ConnectivityTestLogComponent' && speedTestComponentDisplay)
            || ((widget.componentId != 'DevicesModule/SessionsLogComponent')
              && (widget.componentId != 'DevicesModule/OperationsLogComponent')
              && (widget.componentId != 'DevicesModule/PendingOperationsLogComponent')
              && (widget.componentId != 'DevicesModule/ReportsLogComponent')
              // && (widget.componentId != 'DevicesModule/ConnectivityTestLogComponent')
            )
        }

        if (path.endsWith("device-info")) {
          return (widget.componentId == 'DevicesModule/DataCollectStatusComponent' && dataCollectionDisplayInDeviceinfo)
            || (widget.componentId == 'DevicesModule/DeviceRfMapComponent' && groupListDisplayInDashboardAndDeviceInfo)
            || (widget.componentId == 'DevicesModule/MapComponent' && geoLocationDisplay)
            || (widget.componentId == 'DevicesModule/DeviceTotalAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
            || (widget.componentId == 'DevicesModule/ConnectionHistoryComponent' && sessionDisplay)
            || (widget.componentId == 'DevicesModule/UeFiveGCComponent' && fiveGCDisplay)
            || (widget.componentId == 'DevicesModule/UeNidsComponent' && NIDSDisplay)
            || (widget.componentId == 'DevicesModule/WorkflowListComponent' && workflowDisplay)
            || ((widget.componentId != 'DevicesModule/DataCollectStatusComponent')
              && (widget.componentId != 'DevicesModule/DeviceRfMapComponent')
              && (widget.componentId != 'DevicesModule/MapComponent')
              && (widget.componentId != 'DevicesModule/DeviceTotalAlarmCountComponent')
              && (widget.componentId != 'DevicesModule/ConnectionHistoryComponent')
              && (widget.componentId != 'DevicesModule/UeFiveGCComponent')
              && (widget.componentId != 'DevicesModule/UeNidsComponent')
              && (widget.componentId != 'DevicesModule/WorkflowListComponent')
            )
        }

        if (path.endsWith("device-advance")) {
          return (widget.componentId == 'DevicesModule/SpeedTestComponent' && speedTestComponentDisplay)
            || (widget.componentId == 'DevicesModule/CbsdStatusComponent' && CBSDDisplay)
            || (widget.componentId == 'DevicesModule/CbsdConfigsComponent' && CBSDDisplay)
            || (widget.componentId == 'DevicesModule/TerminalComponent' && XMPPDisplayInDashboard)
            || ((widget.componentId != 'DevicesModule/SpeedTestComponent')
              && (widget.componentId != 'DevicesModule/CbsdStatusComponent')
              && (widget.componentId != 'DevicesModule/CbsdConfigsComponent')
              && (widget.componentId != 'DevicesModule/TerminalComponent')
            )
        }

        if (path.endsWith("group-info")) {
          return (widget.componentId == 'GroupsModule/GroupTotalAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
            || (widget.componentId != 'GroupsModule/GroupTotalAlarmCountComponent')
        }

        if (path.endsWith("preference")) {
          return (widget.componentId == 'SystemModule/SnmptrapNotificationsComponent' && snmpDisplayInPreference)
            || (widget.componentId == 'SystemModule/SmtpNotificationsComponent' && mailDisplayInPreference)
            || (widget.componentId == 'SystemModule/FaultmgmtPreferencesComponent' && eventsDisplayInPreferenceAndDashBoard)
            || (widget.componentId == 'SystemModule/ReportsPreferencesComponent' && reportsDisplayInPreference)
            || (widget.componentId == 'SystemModule/LogconfigPreferencesComponent' && logsDisplayInPreference)
            || (widget.componentId == 'SystemModule/StatisticsPreferencesComponent' && statisticsDisplayInPreferenceAndDashBoard)
            || ((widget.componentId != 'SystemModule/SnmptrapNotificationsComponent')
              && (widget.componentId != 'SystemModule/SmtpNotificationsComponent')
              && (widget.componentId != 'SystemModule/FaultmgmtPreferencesComponent')
              && (widget.componentId != 'SystemModule/ReportsPreferencesComponent')
              && (widget.componentId != 'SystemModule/LogconfigPreferencesComponent')
              && (widget.componentId != 'SystemModule/StatisticsPreferencesComponent')
            )
        }

        if (path.endsWith("system/setting")) {
          return (widget.componentId == 'SystemModule/SystemMqttComponent' && uspDisplay)
            || (widget.componentId == 'SystemModule/SystemNetconfComponent' && netconfDisplay)
            || (widget.componentId == 'SystemModule/SystemSummaryReportComponent' && summaryReportLogDisplay)
            || (widget.componentId == 'SystemModule/SystemFiveGCComponent' && fiveGCDisplay)
            || (widget.componentId == 'SystemModule/SystemNetworkIntrusionDetectionComponent' && NIDSDisplay)
            || (widget.componentId == 'SystemModule/SystemMapComponent' && geoLocationDisplay)
            || ((widget.componentId != 'SystemModule/SystemMqttComponent')
              && (widget.componentId != 'SystemModule/SystemNetconfComponent')
              && (widget.componentId != 'SystemModule/SystemSummaryReportComponent')
              && (widget.componentId != 'SystemModule/SystemFiveGCComponent')
              && (widget.componentId != 'SystemModule/SystemNetworkIntrusionDetectionComponent')
              && (widget.componentId != 'SystemModule/SystemMapComponent')
            )
        }

        if (path.endsWith("analysis/system")) {
          return (widget.componentId == 'Analysis/DataXmppStatusComponent' && XMPPDisplayInDashboard)
            || (widget.componentId != 'Analysis/DataXmppStatusComponent')
        }
        return widget
      })

    })
    return widgets
  }

  removeUnauthorizedComponents(widgets) {
    let path = this.route.snapshot['_routerState'].url
    let sessionDisplay = this.pageController("log", 'session')
    let operationLogDisplay = this.pageController("log", 'operation')
    let deviceReportLogDisplay = this.pageController("reports", 'device')
    let groupListDisplayInDashboardAndDeviceInfo = this.canActivateRoute("GroupListComponent")
    let deviceEventsListDisplayInDashboardAndDeviceInfo = this.pageController("events", "device")
    let systemEventsListDisplayInDashboard = this.pageController("events", "system")
    let XMPPDisplayInDashboard = this.canActivateRoute("XMPPComponent")
    let dataCollectionDisplayInDeviceinfo = this.canActivateRoute("DataCollection")
    let mailDisplayInPreference = this.pageController("notification", 'mail')
    let snmpDisplayInPreference = this.pageController("notification", 'snmp')
    let reportsDisplayInPreference = this.canActivateRoute("reports")
    let logsDisplayInPreference = this.canActivateRoute("log")
    let statisticsDisplayInPreferenceAndDashBoard = this.canActivateRoute("StatisticsComponent")
    let eventsDisplayInPreferenceAndDashBoard = this.canActivateRoute("events")
    let speedTestComponentDisplay = this.canActivateRoute("SpeedTestComponent")
    let geoLocationDisplay = this.canActivateRoute("GeoLocation")
    let uspDisplay = this.pageController("protocol", 'usp')
    let netconfDisplay = this.pageController("protocol", 'netconf')
    let summaryReportLogDisplay = this.pageController("reports", 'summary')
    let fiveGCDisplay = this.canActivateRoute("5GC")
    let NIDSDisplay = this.canActivateRoute("NIDS")
    let CBSDDisplay = this.canActivateRoute("CBSD")
    let workflowDisplay = this.canActivateRoute("WorkFlowsComponent")
    if (path.endsWith("dashboard")) {
      return widgets.filter(item => {
        return (item.componentId == 'GroupsModule/GroupListTableComponent' && groupListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'EventsModule/EventsDeviceListComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'DashboardModule/DashboardTotalAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'DashboardModule/DashboardAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'SystemModule/SystemEventsListComponent' && systemEventsListDisplayInDashboard)
          || (item.componentId == 'Analysis/XmppStatusComponent' && XMPPDisplayInDashboard)
          || (item.componentId == 'Analysis/OnlineDeviceStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'Analysis/ImsRegistrationStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'Analysis/SimStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'Analysis/IpSecTunnelStatusComponent' && statisticsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'Analysis/SoftwareVersionDistributionComponent' && statisticsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'Analysis/ProvisioningCodeDistributionComponent' && statisticsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'GroupsModule/DashboardCoverageMapComponent' && groupListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'DashboardModule/GroupNumbersComponent' && groupListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'GroupsModule/GorupListComponent' && groupListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'EventsModule/AlarmListComponent' && eventsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'DashboardModule/GroupsLocationComponent' && groupListDisplayInDashboardAndDeviceInfo)
          // || (item.componentId == 'DashboardModule/DashboardTotalAlarmCountComponent' && eventsDisplayInPreferenceAndDashBoard)
          // || (item.componentId == 'DashboardModule/DashboardAlarmCountComponent' && eventsDisplayInPreferenceAndDashBoard)
          || ((item.componentId != 'GroupsModule/GroupListTableComponent')
            && (item.componentId != 'EventsModule/EventsDeviceListComponent')
            && (item.componentId != 'SystemModule/SystemEventsListComponent')
            && (item.componentId != 'Analysis/XmppStatusComponent')
            && (item.componentId != 'Analysis/OnlineDeviceStatusComponent')
            && (item.componentId != 'Analysis/ImsRegistrationStatusComponent')
            && (item.componentId != 'Analysis/SimStatusComponent')
            && (item.componentId != 'Analysis/IpSecTunnelStatusComponent')
            && (item.componentId != 'Analysis/SoftwareVersionDistributionComponent')
            && (item.componentId != 'Analysis/ProvisioningCodeDistributionComponent')
            && (item.componentId != 'GroupsModule/DashboardCoverageMapComponent')
            && (item.componentId != 'DashboardModule/GroupNumbersComponent')
            && (item.componentId != 'DashboardModule/DashboardTotalAlarmCountComponent')
            && (item.componentId != 'DashboardModule/DashboardAlarmCountComponent')
            && (item.componentId != 'GroupsModule/GorupListComponent')
            && (item.componentId != 'EventsModule/AlarmListComponent')
            && (item.componentId != 'DashboardModule/GroupsLocationComponent')
          )
      })
    }

    if (path.endsWith("device-logs")) {
      return widgets.filter(item => {
        return (item.componentId == 'DevicesModule/SessionsLogComponent' && sessionDisplay)
          || (item.componentId == 'DevicesModule/OperationsLogComponent' && operationLogDisplay)
          || (item.componentId == 'DevicesModule/PendingOperationsLogComponent' && operationLogDisplay)
          || (item.componentId == 'DevicesModule/ReportsLogComponent' && deviceReportLogDisplay)
          // || (item.componentId == 'DevicesModule/ConnectivityTestLogComponent' && speedTestComponentDisplay)
          || ((item.componentId != 'DevicesModule/SessionsLogComponent')
            && (item.componentId != 'DevicesModule/OperationsLogComponent')
            && (item.componentId != 'DevicesModule/PendingOperationsLogComponent')
            && (item.componentId != 'DevicesModule/ReportsLogComponent')
            // && (item.componentId != 'DevicesModule/ConnectivityTestLogComponent')
          )
      })
    }

    if (path.endsWith("device-info")) {
      return widgets.filter(item => {
        return (item.componentId == 'DevicesModule/DataCollectStatusComponent' && dataCollectionDisplayInDeviceinfo)
          || (item.componentId == 'DevicesModule/DeviceRfMapComponent' && groupListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'DevicesModule/MapComponent' && geoLocationDisplay)
          || (item.componentId == 'DevicesModule/DeviceTotalAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
          || (item.componentId == 'DevicesModule/ConnectionHistoryComponent' && sessionDisplay)
          || (item.componentId == 'DevicesModule/UeFiveGCComponent' && fiveGCDisplay)
          || (item.componentId == 'DevicesModule/UeNidsComponent' && NIDSDisplay)
          || (item.componentId == 'DevicesModule/WorkflowListComponent' && workflowDisplay)
          || ((item.componentId != 'DevicesModule/DataCollectStatusComponent')
            && (item.componentId != 'DevicesModule/DeviceRfMapComponent')
            && (item.componentId != 'DevicesModule/MapComponent')
            && (item.componentId != 'DevicesModule/DeviceTotalAlarmCountComponent')
            && (item.componentId != 'DevicesModule/ConnectionHistoryComponent')
            && (item.componentId != 'DevicesModule/UeFiveGCComponent')
            && (item.componentId != 'DevicesModule/UeNidsComponent')
            && (item.componentId != 'DevicesModule/WorkflowListComponent')
          )
      })
    }

    if (path.endsWith("device-advance")) {
      return widgets.filter(item => {
        return (item.componentId == 'DevicesModule/SpeedTestComponent' && speedTestComponentDisplay)
          || (item.componentId == 'DevicesModule/CbsdConfigsComponent' && CBSDDisplay)
          || (item.componentId == 'DevicesModule/CbsdStatusComponent' && CBSDDisplay)
          || (item.componentId == 'DevicesModule/TerminalComponent' && XMPPDisplayInDashboard)
          || ((item.componentId != 'DevicesModule/SpeedTestComponent')
            && (item.componentId != 'DevicesModule/CbsdConfigsComponent')
            && (item.componentId != 'DevicesModule/CbsdStatusComponent')
            && (item.componentId != 'DevicesModule/TerminalComponent')
          )
      })
    }

    if (path.endsWith("group-info")) {
      return widgets.filter(item => {
        return (item.componentId == 'GroupsModule/GroupTotalAlarmCountComponent' && deviceEventsListDisplayInDashboardAndDeviceInfo)
          || (item.componentId != 'GroupsModule/GroupTotalAlarmCountComponent')
      })
    }

    if (path.endsWith("preference")) {
      return widgets.filter(item => {
        return (item.componentId == 'SystemModule/SnmptrapNotificationsComponent' && snmpDisplayInPreference)
          || (item.componentId == 'SystemModule/SmtpNotificationsComponent' && mailDisplayInPreference)
          || (item.componentId == 'SystemModule/FaultmgmtPreferencesComponent' && eventsDisplayInPreferenceAndDashBoard)
          || (item.componentId == 'SystemModule/ReportsPreferencesComponent' && reportsDisplayInPreference)
          || (item.componentId == 'SystemModule/LogconfigPreferencesComponent' && logsDisplayInPreference)
          || (item.componentId == 'SystemModule/StatisticsPreferencesComponent' && statisticsDisplayInPreferenceAndDashBoard)
          || ((item.componentId != 'SystemModule/SnmptrapNotificationsComponent')
            && (item.componentId != 'SystemModule/SmtpNotificationsComponent')
            && (item.componentId != 'SystemModule/FaultmgmtPreferencesComponent')
            && (item.componentId != 'SystemModule/ReportsPreferencesComponent')
            && (item.componentId != 'SystemModule/LogconfigPreferencesComponent')
            && (item.componentId != 'SystemModule/StatisticsPreferencesComponent')
          )
      })
    }

    if (path.endsWith("system/setting")) {
      return widgets.filter(item => {
        return (item.componentId == 'SystemModule/SystemMqttComponent' && uspDisplay)
          || (item.componentId == 'SystemModule/SystemNetconfComponent' && netconfDisplay)
          || (item.componentId == 'SystemModule/SystemSummaryReportComponent' && summaryReportLogDisplay)
          || (item.componentId == 'SystemModule/SystemFiveGCComponent' && fiveGCDisplay)
          || (item.componentId == 'SystemModule/SystemNetworkIntrusionDetectionComponent' && NIDSDisplay)
          || (item.componentId == 'SystemModule/SystemMapComponent' && geoLocationDisplay)
          || ((item.componentId != 'SystemModule/SystemMqttComponent')
            && (item.componentId != 'SystemModule/SystemNetconfComponent')
            && (item.componentId != 'SystemModule/SystemSummaryReportComponent')
            && (item.componentId != 'SystemModule/SystemFiveGCComponent')
            && (item.componentId != 'SystemModule/SystemNetworkIntrusionDetectionComponent')
            && (item.componentId != 'SystemModule/SystemMapComponent')
          )
      })
    }

    if (path.endsWith("analysis/system")) {
      return widgets.filter(item => {
        return (item.componentId == 'Analysis/DataXmppStatusComponent' && XMPPDisplayInDashboard)
          || (item.componentId != 'Analysis/DataXmppStatusComponent')
      })
    }

    return widgets
  }

  hiddenNoPermssionMenu(menu) {
    // console.log(menu)
    let role = this._authenticationService.currentUserValue.role;
    // console.log(this.role)
    let isAdmin = this._authenticationService.isAdmin;
    // console.log(isAdmin)
    return menu
      .map(item => {
        if (item.id == 'dashboard' || item.id == 'analysis') {
          let dashboardRouteAccess = this.canActivateRoute("StatisticsComponent")
          item.hidden = !dashboardRouteAccess
        }
        if (item.id == 'groups') {
          let groupRouteAccess = this.canActivateRoute("GroupListComponent")
          item.hidden = !groupRouteAccess
        }
        if (item.id == 'products') {
          let productRouteAccess = this.canActivateRoute("ProductProfilesComponent")
          item.hidden = !productRouteAccess
        }
        if (item.id == 'events') {
          let alarmsRouteAccess = this.pageController("events", 'device') || this.canActivateRoute("EventsNotificationComponent")
          item.hidden = !alarmsRouteAccess
        }
        if (item.id == 'events' || item.id == 'provisioning' || item.id == 'users' || item.id == 'system') {
          item.children.map(child => {
            if (child.id == 'events-device') {
              let deviceEventsRouteAccess = this.pageController("events", 'device')
              child.hidden = !deviceEventsRouteAccess
            }
            if (child.id == 'events-notification') {
              let monitoringRouteAccess = this.canActivateRoute("EventsNotificationComponent")
              child.hidden = !monitoringRouteAccess
            }
            if (child.id == 'works-flow' || child.id == 'configuration') {
              let workflowRouteAccess = this.canActivateRoute("WorkFlowsComponent")
              child.hidden = !workflowRouteAccess
            }
            if (child.id == 'scripts') {
              let scriptRouteAccess = this.canActivateRoute("ScriptsComponent")
              child.hidden = !scriptRouteAccess
            }
            if (child.id == 'users-account') {
              let userAccountRouteAccess = this.canActivateRoute("UserAccountComponent")
              child.hidden = !userAccountRouteAccess
            }
            if (child.id == 'users-roles') {
              let rolesRouteAccess = this.canActivateRoute("AclRoleComponent")
              child.hidden = !rolesRouteAccess
            }
            if (child.id == 'system-events') {
              let systemEventsRouteAccess = this.pageController("events", 'system')
              child.hidden = !systemEventsRouteAccess
            }
            if (child.id == 'system-reports') {
              let serverReportsRouteAccess = this.pageController("reports", 'server')
              child.hidden = !serverReportsRouteAccess
            }
            if (child.id == 'system-preference') {
              let serverReportsRouteAccess = this.canActivateRoute("log")
              child.hidden = !serverReportsRouteAccess
            }
            return child
          })
        }
        if (item.id == '5GC') {
          let fivGCRouteAccess = this.canActivateRoute("5GC")
          // let roleAccess = !isAdmin && role != 'SUPER'
          item.hidden = !fivGCRouteAccess
        }
        return item
      });
  }

  onlyDisplayLicenseMenu() {
    return [
      {
        id: 'system',
        title: 'System',
        translate: 'SYSTEM.COLLAPSIBLE',
        type: 'collapsible',
        // role: ['Admin'], //? To hide collapsible based on user role
        icon: 'settings',
        children: [
          {
            id: 'system-license',
            title: 'License',
            translate: 'SYSTEM.LICENSE',
            type: 'item',
            icon: 'circle',
            url: 'system/license'
          },
        ]
      },
    ]
  }

}
