import { Component, ChangeDetectorRef } from '@angular/core';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { dashboardWidgets } from './dashboardWidgetUtils';
import { GenWidgetService } from '../commonService/gen-widget.service';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from 'app/layout/components/base/BaseComponent';
import { UserService } from 'app/auth/service/user.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent extends BaseComponent {
  constructor(
    private _gridSystemService: GridSystemService,
    _genWidgetService: GenWidgetService,
    private cdr: ChangeDetectorRef,
    route: ActivatedRoute,
    _userService: UserService,
  ) {
    super(_gridSystemService, _genWidgetService, route, _userService.removeUnauthorizedComponents(dashboardWidgets), _userService);
  };

  ngAfterViewInit() {
    setTimeout(() => { this.gridsterHeight = this._gridSystemService.gridsterHeight }, 0);
  };

  ngAfterViewChecked() {
    if (this._gridSystemService.pageResize) {
      this.gridsterHeight = this._gridSystemService.gridsterHeight
      this._gridSystemService.pageResize = false
      this.cdr.detectChanges()
    }
  }
}
