import { Component, OnInit, Input, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { AnalysisPmService } from 'app/main/analysis-pm/analysis-pm.service';
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-pm-delete',
  templateUrl: './pm-delete.component.html',
  styleUrls: ['./pm-delete.component.scss']
})
export class PmDeleteComponent implements OnInit {
  public blockUIStatus = false;
  @Output() refreshEvt = new EventEmitter<string>();

  @Input() mode: string;
  @Input() row: any;
  @Input() selectedMetric?: any;
  @Input() performanceReportList?: any;
  @Input() selectedMetrics
  @Input() noPermission: any;
  constructor(
    private _analysisPmService: AnalysisPmService,
    private route: ActivatedRoute,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService
  ) { }

  public metricNme: any;
  public ALL = this.translateService.instant('PM.ALL');
  public METRICRULE = this.translateService.instant('PM.METRICRULE');
  public PERFORMANCEREPORT = this.translateService.instant('PM.PERFORMANCEREPORT');
  public CONFIRMDelete = this.translateService.instant('PM.CONFIRM_DELETE');
  public DODELETE = this.translateService.instant('PM.DODELETE');
  public DELETESUCCESS = this.translateService.instant('PM.DELETESUCCESS');
  public DELETEFAIL = this.translateService.instant('PM.DELETEFAIL');
  public DO_DELETE = this.translateService.instant('PM.DO_DELETE');
  public PleaseSelect = this.translateService.instant('PM.PLESESELECT');
  public PMSTATEITEMS = this.translateService.instant('PM.PMSTATEITEMS');

  change(message: string) {
    this.refreshEvt.emit(message);
  }

  delete() {
    switch (this.mode) {
      case 'singleMetric':
        let name = this.row.name
        Swal.fire({
          title: this.CONFIRMDelete,
          text: this.DODELETE + name + "?",
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            this._analysisPmService.deleteMetric(this.selectedMetric.id).then(() => {
              this._toastrUtilsService.showSuccessMessage(this.DELETESUCCESS, `Name: ${name}`)
              this.change(this.DELETESUCCESS)
            }).catch((res) => {
              this._toastrUtilsService.showErrorMessage(this.DELETEFAIL, `Name: ${name} `);
            });
          }
        });
        break;
      case 'multiMetrics':
        // console.log("this.selectedMetrics", this.selectedMetrics)
        if (this.selectedMetrics.length) {
          Swal.fire({
            title: this.CONFIRMDelete,
            text: this.DO_DELETE + this.PERFORMANCEREPORT,
            showCancelButton: true,
            confirmButtonText: this.translateService.instant('COMMON.OK'),
            cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
            customClass: {
              confirmButton: 'btn btn-primary',
              cancelButton: 'btn btn-danger ml-1'
            }
          }).then((result) => {
            if (result.value) {
              let metricArr = []
              this.selectedMetrics.forEach(d => {
                if (!d.isAllowed) {
                  metricArr.push(d.id)
                }
              })
              if (this.selectedMetrics.some(item => item.isAllowed)) {
                this._toastrUtilsService.showWarningMessage(this.PERFORMANCEREPORT, this.PMSTATEITEMS);
                return;
              }
              // console.log(metricArr)
              this._analysisPmService.deleteMultiMetric(metricArr).then(() => {
                this._toastrUtilsService.showSuccessMessage(this.DELETESUCCESS, null)
                this.change(this.DELETESUCCESS)
              }).catch((res) => {
                this._toastrUtilsService.showErrorMessage(this.DELETEFAIL, null);
              })
            }
          });
        } else {
          Swal.fire({
            title: this.PleaseSelect,
            icon: "warning",
            showCancelButton: false,
            confirmButtonText: this.translateService.instant('COMMON.OK'),
            customClass: {
              confirmButton: 'btn btn-primary',
            }
          })
        }
        break;
      case 'allMetrics':
        Swal.fire({
          title: this.CONFIRMDelete,
          text: this.DODELETE + this.ALL + ' ' + this.PERFORMANCEREPORT + ' ' + "?",
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            if (this.performanceReportList.some(item => item.isAllowed)) {
              this._toastrUtilsService.showWarningMessage(this.PERFORMANCEREPORT, this.PMSTATEITEMS);
              return;
            }
            this._analysisPmService.deleteAllMetric().then(() => {
              this._toastrUtilsService.showSuccessMessage(this.DELETESUCCESS, null)
              this.change(this.DELETESUCCESS)
            }).catch((res) => {
              console.log(res)
              this._toastrUtilsService.showErrorMessage(this.DELETEFAIL, null);
            });
          }
        });
        break;
    }
  }


  ngOnInit(): void {

  }

}
