<core-card 
  [actions]="['reload']" 
  [componentId]="accessor.componentId" 
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <!-- warning message -->
  <div *ngIf="ismapErr" class="map-overlay-warning">
    <div class="badge badge-warning" style="white-space: normal;font-size: 14px;">
      <i [data-feather]="'alert-triangle'"></i>
      {{mapType == '0' ? GOOGLEMAPERROR : OSMERROR}}
    </div>
  </div>
    <!-- Google Map -->
  <div class="card-body h-100 w-100" style="cursor:default;">
    <div *ngIf="mapType=='0' && (apiLoaded | async)" class="h-100 w-100">
      <google-map #googlemap [options]="mapOptions" [center]="mapCenter" [zoom]="mapZoom" (zoomChanged)="onZoomChange()" height="100%" width="100%">
        <map-info-window>
          <div [innerHTML]="infoContent"></div>
        </map-info-window>
      </google-map>
    </div>
    <!-- OSM -->
    <div *ngIf="mapType=='1'" class="h-100 w-100">
      <div id='group_osmmap' #group_osmmap style="height: 100%;width: 100%;margin: 0;padding: 0;">
      </div>
    </div>
  </div>
</core-card>