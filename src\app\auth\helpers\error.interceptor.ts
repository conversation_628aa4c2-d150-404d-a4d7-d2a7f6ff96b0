import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandlingService } from 'app/main/commonService/error-handling.service';
import { AuthenticationService } from 'app/auth/service';


@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  /**
   * @param {Router} _router
   * @param {AuthenticationService} _authenticationService
   */
  constructor(
    private _router: Router, 
    private _authenticationService: AuthenticationService, 
    private errorHandler: ErrorHandlingService
  ) { }


  logOutAndNavigateToLoginPage(error) {
    // ? Can also logout and reload if needed
    if(!this._router.url.includes("/pages") && this._router.url != "/"){
      this._authenticationService.logout().finally(() => {
        // this._router.navigate(['/pages/authentication/login-v2']);
        this.errorHandler.handle401Error(error)
      });
    } else {
      this._authenticationService._logout()
      this.errorHandler.closeAllModals();
    }
  }
  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const pmFolder = 'pm/';
    return next.handle(request).pipe(
      catchError(err => {
        if ([401].indexOf(err.status) !== -1 && !request.url.includes(pmFolder)) {
          // auto logout if 401 Unauthorized or 403 Forbidden response returned from api
          // this._router.navigate(['/pages/miscellaneous/not-authorized']);
          this.logOutAndNavigateToLoginPage(err.error || 'Your session is invalid or has expired, please login again.');
          // location.reload(true);
        }
        // if (err.status === 0 && err.error instanceof ProgressEvent) {
        //   this.logOutAndNavigateToLoginPage();
        // }
        // throwError
        // if ([400, 403, 500, 501].indexOf(err.status) !== -1) {
        //   return throwError(err);
        // } else {
        //   // error = (err.error && err.error.message) || err.statusText;
        //   return throwError(err.error);
        // }
        return throwError(() => err);
      })
    );
  }
}
