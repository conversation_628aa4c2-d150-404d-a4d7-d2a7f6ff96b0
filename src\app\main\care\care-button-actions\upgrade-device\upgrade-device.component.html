<button
  *ngIf="!loading"
  [disabled]="editMode"
  (click)="upgradeDevice();"
  type="button"
  class="btn btn-sm mr-50"
  [class]="upgradeStatus ? 'btn-danger' : 'btn-primary'"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.UPGRADEFIRMWARE' | translate }}"
  [disabled]="!deviceId"
  rippleEffect>
  <span [data-feather]="'download-cloud'"></span>
</button>
<button
  *ngIf="loading"
  [disabled]="editMode"
  type="button"
  class="btn btn-sm mr-50"
  [class]="upgradeStatus ? 'btn-danger' : 'btn-primary'"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.UPGRADEFIRMWARE' | translate }}"
  rippleEffect
  [disabled]="true">
  <div
    class="spinner-border spinner-border-sm"
    role="status">
  </div>
</button>