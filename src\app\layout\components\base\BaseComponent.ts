import { Directive } from "@angular/core";
import { Unsubscribe } from "./Unsubscribe";
import { GridsterConfig } from "angular-gridster2";
import { GridSystemService } from "app/main/commonService/grid-system.service";
import { GenWidgetService } from "app/main/commonService/gen-widget.service";
import { ActivatedRoute } from "@angular/router";
import { Widget } from "app/layout/widget";
import { forkJoin, Observable, map, of } from 'rxjs';
import { UserService } from 'app/auth/service/user.service';

@Directive()
export class BaseComponent extends Unsubscribe {

    public gridOption: GridsterConfig;
    public gridsterHeight;
    public widgets;
    protected _userService: UserService; // 新增這行來定義屬性
    constructor(
        _gridSystemService: GridSystemService,
        _genWidgetService: GenWidgetService,
        route: ActivatedRoute,
        widgetUtils: Widget[],
        _userService: UserService
    ) {
        super();
        // console.log(this._userService)
        this._userService = _userService; // 儲存服務實例
        _genWidgetService.removeObservers();
        this.gridsterHeight = _gridSystemService.gridsterHeight;
        this.subscribeRouteData(route, _gridSystemService)
        this.customSubscribe(_gridSystemService.getGridOption(), res => this.gridOption = res);
        this.customSubscribe(_genWidgetService.onRenderingWidgetChanged, res => this.widgets = res);
        this.subscribeGetGenWidgetList(_genWidgetService, widgetUtils)
    }

    subscribeRouteData(route: ActivatedRoute, _gridSystemService: GridSystemService) {
        this.customSubscribe(route.data, res => _gridSystemService.init(res.pts));
    }

    subscribeGetGenWidgetList(_genWidgetService: GenWidgetService, widgetUtils: Widget[]) {
        this.customSubscribe(_genWidgetService.getRenderingWidgetList(widgetUtils), res => this.widgets = res);
    }

    checkWidgetLicenses(widgets): Observable<any> {
        const licenseChecks = widgets.map(widget => {
            if (widget.class == '5gc') {
                return this._userService.checkLicenceKey(widget.class).pipe(
                    map(isValid => {
                        widget.showWidget = isValid;
                        return widget; // 返回 widget 以便在最終結果中包含
                    })
                );
            } else {
                widget.showWidget = true; // 如果 widget 沒有指定 License Key，預設為顯示
                return of(widget); // 返回一個 Observable 以便於合併
            }
        });
        return forkJoin(licenseChecks); // 返回所有 license 驗證的結果
    }
}