<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <div
    class="form-group"
    style="position: absolute;right: 35px;top: 5.5px;width: 140px;">
    <ng-select
      class="ng-select-size-sm "
      [items]="groupList"
      bindLabel="name"
      bindValue="id"
      [clearable]="false"
      [(ngModel)]="groupselected"
      (change)="selectedGroup($event)"
      placeholder="Select Group">
      <ng-template
        ng-optgroup-tmp
        let-item="item">
        {{ item.name }}
      </ng-template>
    </ng-select>
  </div>
  <div class="card-body">
    <main class="w-100 h-100 ">
      <app-rf-map
        *ngIf="rfData"
        [res]="rfData"
        [groupId]="groupId">
      </app-rf-map>
    </main>
  </div>
</core-card>
