<core-card
  [actions]="[ 'columnDragula','reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  [columnDragula]="tableOption"
  (changeColumn)="changeColumn($event)"
  (events)="emittedEvents($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <ngx-datatable
    #tableRowDetails
    appDatatableRecalculate
    [datatable]="tableRowDetails"
    [rows]="rows"
    [rowHeight]="37"
    class="bootstrap core-bootstrap"
    [limit]="selectedOption"
    [columnMode]="ColumnMode.force"
    [headerHeight]="45"
    [footerHeight]="50"
    [scrollbarH]="true"
    [scrollbarV]="true"
    (select)="onSelect($event)">
    <!-- view linkrate chart -->
    <ngx-datatable-column
      name=" "
      headerClass="tableActionHead"
      cellClass="tableActionCell"
      [width]="30"
      [sortable]="false"
      [canAutoResize]="false"
      [draggable]="false"
      [resizeable]="false">
      <ng-template
        let-row="row"
        ngx-datatable-cell-template>
        <div>
          <button
            (click)="openChartModal(modalChart,row.MAC)"
            type="button"
            class="btn icon-btn btn-sm hide-arrow tableActionButton"
            ngbTooltip="{{ 'DEVICES.VIEWCHART' | translate }}"
            placement="top"
            container="body"
            rippleEffect>
            <i class="fa-solid fa-chart-line text-primary"></i>
          </button>
        </div>
      </ng-template>
    </ngx-datatable-column>
    <!-- MAC -->
    <!-- <ngx-datatable-column
      [width]="160"
      name="MAC"
      prop="MAC">
      <ng-template
        let-column="column"
        ngx-datatable-header-template>
        <input
          type="text"
          trim
          [(ngModel)]="filterMap[column.prop]"
          class="form-control form-control-sm"
          placeholder="{{column.name}}"
          (keyup)="filterInput($event,column.prop)">
      </ng-template>
      <ng-template
        let-MAC="value"
        let-row="row"
        ngx-datatable-cell-template>
        <app-beautify-content [content]="MAC"></app-beautify-content>
      </ng-template>
    </ngx-datatable-column> -->
    <ng-container *ngFor="let col of tableOption">
       <!-- MAC -->
      <ngx-datatable-column
        *ngIf="col.prop === 'MAC' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="true"
        name="{{ col.translate | translate }}"
        prop="MAC">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            [(ngModel)]="filterMap[column.prop]"
            class="form-control form-control-sm"
            placeholder="{{column.name}}"
            (keyup)="filterInput($event,column.prop)">
        </ng-template>
        <ng-template
          let-MAC="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="MAC"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- RSSI -->
      <ngx-datatable-column
        *ngIf="col.prop === 'Signal' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="Signal">
        <ng-template
          let-row="row"
          let-Signal="value"
          ngx-datatable-cell-template>
          <div [ngStyle]="{'color': Signal >-65 ? '#3BB938' : Signal <= -65 && Signal >= -80 ? '#FFD700':'#FF3838' }">{{Signal?Signal:'-'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- DownlinkRate -->
      <ngx-datatable-column
        *ngIf="col.prop === 'DownlinkRate' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="DownlinkRate">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          {{ 'DEVICES.DOWNLINK_RATE' | translate }}
        </ng-template>
        <ng-template
          let-row="row"
          let-DownlinkRate="value"
          ngx-datatable-cell-template>
          <div>{{(DownlinkRate || 0) | kbpsToSizeValue }}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- UplinkRate -->
      <ngx-datatable-column
        *ngIf="col.prop === 'UplinkRate' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="UplinkRate">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          {{ 'DEVICES.UPLINK_RATE' | translate }}
        </ng-template>
        <ng-template
          let-UplinkRate="value"
          ngx-datatable-cell-template>
          <div>{{(UplinkRate || 0) | kbpsToSizeValue }}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- SSID -->
      <ngx-datatable-column
        *ngIf="col.prop === 'SSID' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="SSID">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            [(ngModel)]="filterMap[column.prop]"
            class="form-control form-control-sm"
            placeholder="{{column.name}}"
            (keyup)="filterInput($event,column.prop)">
        </ng-template>
        <ng-template
          let-SSID="value"
          ngx-datatable-cell-template>
          <app-beautify-content
            [content]="SSID"
            [placement]="'left'"></app-beautify-content>
        </ng-template>
      </ngx-datatable-column>
      <!-- BSSID -->
      <ngx-datatable-column
        *ngIf="col.prop === 'BSSID' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="BSSID">
        <ng-template
          let-column="column"
          ngx-datatable-header-template>
          <input
            type="text"
            trim
            [(ngModel)]="filterMap[column.prop]"
            class="form-control form-control-sm"
            placeholder="{{column.name}}"
            (keyup)="filterInput($event,column.prop)">
        </ng-template>
        <ng-template
          let-BSSID="value"
          ngx-datatable-cell-template>
          <div class="text-uppercase">{{BSSID?BSSID:'-'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Band -->
      <ngx-datatable-column
        *ngIf="col.prop === 'Band' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="Band">
        <ng-template
          let-Band="value"
          ngx-datatable-cell-template>
          <div>{{Band?Band:'-'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Bandwidth -->
      <ngx-datatable-column
        *ngIf="col.prop === 'Bandwidth' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="Bandwidth">
        <ng-template
          let-Bandwidth="value"
          ngx-datatable-cell-template>
          <div>{{Bandwidth?Bandwidth:'-'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Wi-Fi Mode -->
      <ngx-datatable-column
        *ngIf="col.prop === 'Mode' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="Mode">
        <ng-template
          let-Mode="value"
          ngx-datatable-cell-template>
          <div>{{Mode?Mode:'-'}}</div>
        </ng-template>
      </ngx-datatable-column>
      <!-- Connect Time -->
      <ngx-datatable-column
        *ngIf="col.prop === 'AssociationTime' && col.columnStatus"
        [width]="col.width"
        [draggable]="false"
        [sortable]="false"
        name="{{ col.translate | translate }}"
        prop="AssociationTime">
        <ng-template
          let-AssociationTime="value"
          ngx-datatable-cell-template>
          <div>
            {{AssociationTime | date:'MM/dd/yy, HH:mm:ss'}}
          </div>
        </ng-template>
      </ngx-datatable-column>
    </ng-container>
  </ngx-datatable>
  <!-- Model -->
  <ng-template
    #modalChart
    let-modal>
    <div class="modal-header">
      <h4
        class="modal-title"
        id="myModalLabel1">
        <span class="modal-title-name">{{ 'COMMON.DEVICE' | translate }} MAC</span>
        <span class="ml-75 badge badge-light-info ">{{mac}}</span>
      </h4>
      <button
        type="button"
        class="close"
        (click)="modal.dismiss('Cross click')"
        aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div
      class="modal-body"
      tabindex="0"
      ngbAutofocus
      #linkRateHistoryRef>
      <app-wifi-clients-status-chart
        [chartRef]="linkRateHistoryRef"
        [chartData]="chartData"
        [isShowNoData]="isShowNoData"
        [personalTheme]="personalTheme"
        [title]="mac+'-LinkRateHistory'">
      </app-wifi-clients-status-chart>
    </div>
    <div class="modal-footer d-flex">
      <button
        type="button"
        class="btn btn-primary"
        (click)="modal.dismiss('Cross click')"
        rippleEffect>
        {{ 'GROUPS.CONFIRM' | translate }}
      </button>
    </div>
  </ng-template>
</core-card>
