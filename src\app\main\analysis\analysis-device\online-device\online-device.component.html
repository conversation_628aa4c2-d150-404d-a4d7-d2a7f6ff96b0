<core-card
  [actions]="['reload','duration','maximize','download']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="loading"
  [selectedIcon]="selectedIcon"
  [dayItems]="dayItems"
  (events)="emittedEvents($event)">
  <h4 class="card-title flex-fill d-flex justify-content-between align-items-center"
  style="width: calc(100% - 100px);">
    <span
      class="text-truncate"
      ngbTooltip="{{ accessor.name | translate }}"
      container="body">
      {{ accessor.name | translate }}
    </span>
    <div class="d-flex mr-75 header-icon">
      <!-- <div
        ngbDropdown
        [placement]="'bottom'"
        container="body"
        class="btn-group d-flex align-items-center dropup dropdown-icon-wrapper shadow-none pointer">
        <div
          ngbDropdownToggle
          class="d-flex align-items-center shadow-none"
          ngbTooltip="{{ 'ANALYSIS.SELECTDURATION' | translate }}"
          container="body"
          placement="bottom">
          <svg
            width="14px"
            height="14px"
            class="dropdown-icon">
            <use [attr.href]="'./../assets/fonts/added-icon.svg#' + selectedIcon"></use>
          </svg>
        </div>
        <div
          ngbDropdownMenu
          class="dropdown-menu">
          <div
            *ngFor="let item of dayItems"
            ngbDropdownItem
            class="draggable"
            (click)="changeFn(item)">
            {{ item.key }}
          </div>
        </div>
      </div> -->
    </div>
  </h4>
  <div class="card-body">
    <main
      *ngIf="chartData"
      class="w-100 h-100"
      #onlineDeviceRef>
      <!-- <app-bar-echarts
        [chartRef]="onlineDeviceRef"
        [chartData]="chartData"
        [tooltipStyle]="'scroll'"
        [componentTop]="componentTop"
        [title]="'DeviceStatistics-OnlineDevice'">
      </app-bar-echarts> -->
      <app-bar-echarts
        [chartRef]="onlineDeviceRef"
        [chartData]="chartData"
        [title]="'DeviceStatistics-OnlineDevice'"
        [downloadData]="downloadData"
        [openModal]="openModalFlag">
      </app-bar-echarts>
    </main>
  </div>
</core-card>
