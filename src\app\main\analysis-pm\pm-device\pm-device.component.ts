import { Component, OnInit, Input, SimpleChanges, OnChanges, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AnalysisPmService } from 'app/main/analysis-pm/analysis-pm.service';
import { BlockUI, NgBlockUI } from 'ng-block-ui';
import { selectPMClassify } from '../pm-classify-data';
import { DatePipe } from '@angular/common';
import { FileDownloadService } from 'app/main/commonService/file-download.service';

@Component({
  selector: 'app-pm-device',
  templateUrl: './pm-device.component.html',
  styleUrls: ['./pm-device.component.scss']
})
export class PmDeviceComponent implements OnInit, OnChanges {
  @Input() personalTheme: any;
  @Input() sn: any;
  @Input() duration: any;
  @Input() chartData: any;
  @Input() measureType: any;
  @Input() num: any;
  @Output() blockUIEvt = new EventEmitter<string>();
  @BlockUI('chartBlock') chartBlock: NgBlockUI;
  private isInitialized = false;
  public radioModel = 'day';
  public blockUIStatus = false;
  public maxDate;
  public minDate;
  public today = new Date();
  public selectPMClassify = selectPMClassify
  public selectCondition = [
    { name: '=', value: 'EQ' },
    { name: '!=', value: 'NE' },
    { name: '>', value: 'GT' },
    { name: '<', value: 'LT' },
    { name: '<=', value: 'LTE' },
    { name: '>=', value: 'GTE' },
  ];

  public beginTime;
  public endTime;
  public resNum: any

  constructor(
    private _analysisPmService: AnalysisPmService,
    private _route: ActivatedRoute,
    private datePipe: DatePipe,
    private _fileDownloadService: FileDownloadService,
  ) {
  }

  mappingName(value, mappingArr) {
    const item = mappingArr.find(item => item.value === value);
    return item ? item.name : null;
  }


  // key/value format
  extractAllKeysAndValues(obj: any, currentKey: string = ''): { key: string, value: any }[] {
    const keyValues: { key: string, value: any }[] = [];
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = currentKey ? `${currentKey}.${key}` : key;
        const value = obj[key];
        if (typeof value === 'object' && !Array.isArray(value)) {
          keyValues.push(...this.extractAllKeysAndValues(value, newKey));
        } else if (Array.isArray(value)) {
          for (let i = 0; i < value.length; i++) {
            if (typeof value[i] === 'object') {
              keyValues.push(...this.extractAllKeysAndValues(value[i], `${newKey}[${i}]`));
            }
          }
        } else {
          keyValues.push({ key: newKey, value });
        }
      }
    }
    return keyValues;
  }

  public classifyRateMapping = [
    { rate: 'RRC.Rate.EstabRate', succ: 'RRC.ConnEstabSucc.sum', att: 'gnb.RRC.ConnEstabSetup.sum' },
    { rate: 'UECNTX.Rate.EstabRate', succ: 'UECNTX.ConnEstabSucc.sum', att: 'UECNTX.ConnEstabAtt.sum' },
    { rate: 'SM.Rate.PduRate', succ: 'SM.PDUSessionSetupSucc', att: 'SM.PDUSessionSetupReq' },
    { rate: 'QF.Rate.VonrRate', succ: 'QF.EstabSuccNbr.5QI1', att: 'QF.EstabAttNbr.5QI1' },
    { rate: 'MM.Rate.HandoverRate', succ: 'MM.HoExeInterSucc', att: 'MM.HoExeInterReq' },
    { rate: 'MM.Rate.EpsRate', succ: 'MM.HoOutExe5gsToEpsSucc', att: 'MM.HoOutExe5gsToEpsReq' },
    { rate: 'X2.Rate.EndcRate', succ: 'X2.Cntrl.EndcSetupResponseSent', att: 'X2.Cntrl.EndcSetupReqReceived' },
    { rate: 'X2.Rate.SgnbRate', succ: 'X2.Cntrl.SgNBAddReqAckSent', att: 'X2.Cntrl.SgNBAddReqReceived' },
    { rate: 'X2.Rate.SgnbSNRate', succ: 'X2.Cntrl.SgNBRelConfReceived', att: 'X2.Cntrl.SgNBRelRequiredSent' },
    { rate: 'X2.Rate.SgnbMNRate', succ: 'X2.Cntrl.SgNBRelReqAckSent', att: 'X2.Cntrl.SgNBRelReqReceived' },
  ]
  mappingRateDetail(name, mappingArr) {
    const item = mappingArr.find(item => item.rate === name);
    return item ? item : null;
  }


  public res: any
  public throughputSeries = []
  public rateSeries = []
  public countSeries = []
  public fiveQISeries = []
  formatChartData(res: string) {
    const jsonRes = JSON.parse(res);
    if (!jsonRes.data?.length) return;

    const throughputResult = new Map();
    const rateDetailResult = new Map();
    const countResult = new Map();
    const fiveQIResult = new Map();

    jsonRes.data.sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime());

    jsonRes.data.forEach(item => {
      const keyArr = this.extractAllKeysAndValues(item);
      keyArr.forEach(k => k.value = [item.endTime, k.value]);

      keyArr.forEach(({ key, value }) => {
        if (['DRB.UEThpUl.sum', 'DRB.UEThpDl.sum', 'DRB.IPThpUl.sum', 'DRB.IPThpDl.sum'].includes(key)) {
          this.addToResultMap(throughputResult, key, value);
        } else if (key.includes('DRB.PacketLossRateUl.5QI')) {
          this.addToResultMap(fiveQIResult, key, value);
        } else if (['HO.IntraSys.TooEarly', 'HO.IntraSys.TooLate'].includes(key)) {
          this.addToResultMap(countResult, key, value);
        } else if (this.isRateKey(key)) {
          this.addToResultMap(rateDetailResult, key, value);
        }
      });

      // 將 rate key 補上 (succ/att) 額外資訊
      keyArr.forEach(({ key }) => {
        const mapping = this.mappingRateDetail(key, this.classifyRateMapping);
        if (mapping && key === mapping.rate) {
          const succ = keyArr.find(k => k.key === mapping.succ)?.value[1] ?? '-';
          const att = keyArr.find(k => k.key === mapping.att)?.value[1] ?? '-';
          const item = keyArr.find(k => k.key === mapping.rate);
          if (item) item.value.push(`(${succ}/${att})`);
        }
      });
    });


    // Convert the sorted data into ECharts format
    // console.log('new throughputResult', throughputResult)
    // console.log('new rateDetailResult', rateDetailResult)
    // console.log('new countResult', countResult)
    // console.log('new fiveQIResult', fiveQIResult)
    this.throughputSeries = this.mapToSeries(throughputResult, 'Throughput', 'kpbs', true);
    this.rateSeries = this.mapToSeries(rateDetailResult, 'Rate', '%', true);
    this.countSeries = this.mapToSeries(countResult, 'Count', '', true);
    this.fiveQISeries = this.mapToSeries(fiveQIResult, '5QI', '', true, true);
  }

  private addToResultMap(map: Map<string, any[][]>, key: string, value: any[]) {
    if (!map.has(key)) {
      map.set(key, []);
    }
    map.get(key)!.push(value);
  }

  private isRateKey(key: string): boolean {
    return !['DRB.UEThpUl.sum', 'DRB.UEThpDl.sum', 'DRB.IPThpUl.sum', 'DRB.IPThpDl.sum',
      '_id', 'beginTime', 'endTime', 'RRU.PrbTotDl', 'RRU.PrbTotUl'].includes(key) &&
      key.includes('Rate') && !key.includes('DRB.PacketLossRateUl.5QI');
  }

  private mapToSeries(
    map: Map<string, any[][]>,
    title: string,
    unit: string,
    smooth: boolean = true,
    extractNameFromKeyPart3: boolean = false
  ): any[] {
    const result = [];
    for (const [key, value] of map) {
      result.push({
        title,
        unit,
        name: extractNameFromKeyPart3 ? key.split('.')[2] : this.mappingName(key, this.selectPMClassify),
        data: value,
        type: 'line',
        smooth,
      });
    }
    return result;
  }


  formatDownloadData(res: string): {
    throughputSeries: any[],
    rateSeries: any[],
    countSeries: any[],
    fiveQISeries: any[]
  } {
    const jsonRes = JSON.parse(res);
    if (!jsonRes.data?.length) return {
      throughputSeries: [],
      rateSeries: [],
      countSeries: [],
      fiveQISeries: []
    };

    const throughputResult = new Map();
    const rateDetailResult = new Map();
    const countResult = new Map();
    const fiveQIResult = new Map();

    jsonRes.data.sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime());

    jsonRes.data.forEach(item => {
      const keyArr = this.extractAllKeysAndValues(item);
      keyArr.forEach(k => k.value = [item.endTime, k.value]);

      keyArr.forEach(({ key, value }) => {
        if (['DRB.UEThpUl.sum', 'DRB.UEThpDl.sum', 'DRB.IPThpUl.sum', 'DRB.IPThpDl.sum'].includes(key)) {
          this.addToResultMap(throughputResult, key, value);
        } else if (key.includes('DRB.PacketLossRateUl.5QI')) {
          this.addToResultMap(fiveQIResult, key, value);
        } else if (['HO.IntraSys.TooEarly', 'HO.IntraSys.TooLate'].includes(key)) {
          this.addToResultMap(countResult, key, value);
        } else if (this.isRateKey(key)) {
          this.addToResultMap(rateDetailResult, key, value);
        }
      });

      // 補 rate (succ/att)
      keyArr.forEach(({ key }) => {
        const mapping = this.mappingRateDetail(key, this.classifyRateMapping);
        if (mapping && key === mapping.rate) {
          const succ = keyArr.find(k => k.key === mapping.succ)?.value[1] ?? '-';
          const att = keyArr.find(k => k.key === mapping.att)?.value[1] ?? '-';
          const item = keyArr.find(k => k.key === mapping.rate);
          if (item) item.value.push(`(${succ}/${att})`);
        }
      });
    });

    return {
      throughputSeries: this.mapToSeries(throughputResult, 'Throughput', 'kpbs', true),
      rateSeries: this.mapToSeries(rateDetailResult, 'Rate', '%', true),
      countSeries: this.mapToSeries(countResult, 'Count', '', true),
      fiveQISeries: this.mapToSeries(fiveQIResult, '5QI', '', true, true),
    };
  }


  changDuration(duration) {
    this.chartBlock.start();
    // console.log(duration.currentValue)
    let today = new Date();
    this.endTime = new Date(today.setHours(23, 59, 59)).toISOString();
    if (duration.currentValue == 'month') {
      var oneMonthAgo = new Date(this.today);
      oneMonthAgo.setMonth(this.today.getMonth() - 1);
      if (this.today.getDate() < oneMonthAgo.getDate()) {
        oneMonthAgo.setDate(this.today.getDate());
      }
      // console.log(oneMonthAgo);
      oneMonthAgo.setHours(0, 0, 0, 0)
      this.beginTime = oneMonthAgo.toISOString()
    }
    else if (duration.currentValue == 'week') {
      let sevenDaysAgo = new Date(this.today);
      sevenDaysAgo.setDate(this.today.getDate() - 7);
      sevenDaysAgo.setHours(0, 0, 0, 0)
      // console.log(sevenDaysAgo);
      this.beginTime = sevenDaysAgo.toISOString()
    }
    else {
      this.beginTime = new Date(this.today.setHours(0, 0, 0, 0)).toISOString()
    }
    let param = {
      "beginTime": this.beginTime,
      "endTime": this.endTime,
    }
    // console.log(param)
    this._analysisPmService.postDeviceDataByMeasureType(this.sn, param, this.measureType).then(res => {
      // console.log(res)
      this.res = res
      this.resNum = JSON.parse(this.res).num
      this.formatChartData(this.res)
    }).finally(() => {
      this.chartBlock.stop();
    });
  }

  changMeasure(measureType) {
    this.chartBlock.start();
    // console.log(measureType.currentValue)
    let param = {
      "beginTime": this.beginTime,
      "endTime": this.endTime,
    }
    // console.log(param)
    this._analysisPmService.postDeviceDataByMeasureType(this.sn, param, measureType.currentValue).then(res => {
      // console.log(res)
      this.res = res
      this.resNum = JSON.parse(this.res).num
      this.formatChartData(this.res)
    }).finally(() => {
      this.chartBlock.stop();
    });
  }


  areAllArraysEmpty(arrays: any[][]): boolean {
    return arrays.every(array => array.length === 0);
  }

  getVisibleSeriesCount(): number {
    let count = 0;
    if (this.rateSeries.length > 0) count++;
    if (this.throughputSeries.length > 0) count++;
    if (this.fiveQISeries.length > 0) count++;
    if (this.countSeries.length > 0) count++;
    return count;
  }

  getColumnClass(index: number): string {
    const visibleCount = this.getVisibleSeriesCount();
    // 偶數：col-6
    if (visibleCount % 2 === 0) {
      return 'col-6';
    } else {
      // 單數：第1個 col-12, 其他 col-6
      return index === 0 ? 'col-12' : 'col-6';
    }
  }

  emittedEvents(event: any, chartType): void {
    if (event === 'download') {
      // console.log('download beginTime', this.beginTime)
      // console.log('download endTime', this.endTime)
      this.getOriginalData().then(seriesForDownload => {
        // console.log('seriesForDownload', seriesForDownload);
        switch (chartType) {
          case 'rate':
            this.downloadOriginalData(seriesForDownload.rateSeries, `Rate_${this.sn}`)
            break;
          case 'throughput':
            this.downloadOriginalData(seriesForDownload.throughputSeries, `Throughput_${this.sn}`)
            break;
          case 'fiveQI':
            this.downloadOriginalData(seriesForDownload.fiveQISeries, `5QI_${this.sn}`)
            break;
          case 'count':
            this.downloadOriginalData(seriesForDownload.countSeries, `Count_${this.sn}`)
            break;
        }
      });
    }
  }

  public originalRes
  getOriginalData(): Promise<any> {
    const param = {
      beginTime: this.beginTime,
      endTime: this.endTime,
    };

    return this._analysisPmService.postDeviceData(this.sn, param)
      .then(res => {
        this.originalRes = res;
        return this.formatDownloadData(this.originalRes);
      })
      .catch(error => {
        console.error(error);
        return [];
      })
      .finally(() => {

      });
  }

  downloadOriginalData(chartData, title) {
    // console.log(chartData)
    let rearrangedData
    const currentDate = new Date();
    const formattedDate = this.datePipe.transform(currentDate, 'yyyyMMdd_HHmmss');

    if (this.sn) {
      rearrangedData = chartData.reduce((result, item) => {
        item.data.forEach(entry => {
          entry[0] = new Date(entry[0]).getFullYear() + '/' + (new Date(entry[0]).getMonth() + 1) + '/' + new Date(entry[0]).getDate() + ' ' + (new Date(entry[0]).getHours() < 10 ? '0' : '') + new Date(entry[0]).getHours() + ':' + (new Date(entry[0]).getMinutes() < 10 ? '0' : '') + new Date(entry[0]).getMinutes()
          const existingEntry = result.find(obj => obj.Time === entry[0]);
          if (existingEntry) {
            existingEntry[item.name] = entry[1];
          } else {
            const newEntry = { Time: entry[0], [item.name]: entry[1] };
            result.push(newEntry);
          }
        });
        return result;
      }, []);

      this._fileDownloadService.exportPMdeviceToExcel(rearrangedData, `PM_${title}_${formattedDate}`, this.sn);
    } else {
      rearrangedData = chartData.map(item => ({
        name: item.name,
        data: item.data.map(entry => ({
          Time: new Date(entry[0]).getFullYear() + '/' + (new Date(entry[0]).getMonth() + 1) + '/' + new Date(entry[0]).getDate() + ' ' + (new Date(entry[0]).getHours() < 10 ? '0' : '') + new Date(entry[0]).getHours() + ':' + (new Date(entry[0]).getMinutes() < 10 ? '0' : '') + new Date(entry[0]).getMinutes(),
          [this.mappingName(item.xname, this.selectPMClassify) ? this.mappingName(item.xname, this.selectPMClassify) : this.sn ? this.sn : item.xname]: entry[1],
        }))
      }));

      this._fileDownloadService.exportPMToExcel(rearrangedData, `PM_${title}_${formattedDate}`);
    }
  }


  ngOnInit(): void {
    this.isInitialized = true;
    // this.res = this.chartData
    // this.resNum = this.num
    // this.formatData(this.res)

    this.beginTime = this.today.setHours(0, 0, 0, 0);
    this.endTime = this.today.setHours(23, 59, 59);
    let params = {
      "beginTime": (new Date(this.beginTime)).toISOString(),
      "endTime": (new Date(this.endTime)).toISOString(),
    }
    this._analysisPmService.postDeviceDataByMeasureType(this.sn, params, this.measureType).then(res => {
      // console.log(res)
      this.res = res
      this.resNum = JSON.parse(this.res).num
      this.formatChartData(this.res)
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.isInitialized) {
      if (changes.sn || changes.duration) {
        this.changDuration(changes.duration)
      } else if (changes.measureType) {
        this.changMeasure(changes.measureType)
      }
    }
  }


}
