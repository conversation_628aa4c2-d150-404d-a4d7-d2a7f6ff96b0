import { Inject, Injectable } from "@angular/core";
import { DOCUMENT } from "@angular/common";
import Swal from 'sweetalert2';
import { ToastrService } from "ngx-toastr";
import { ToastrUtilsService } from "app/main/commonService/toastr-utils.service";
import { TranslateService } from "@ngx-translate/core";
import { ProductDataService } from "app/main/products/products.service";

@Injectable({ providedIn: 'root' })
export class ParameterDetailHelper {

    summaryButtonAccess: boolean;
    summaryReportList: any;
    productId
    productName

    constructor(
        @Inject(DOCUMENT) private document: any,
        private toastr: ToastrService,
        private _toastrUtilsService: ToastrUtilsService,
        private translateService: TranslateService,
        private _productDataService: ProductDataService,
    ) {
    }

    init(productName) {
        if (this.productName !== productName) {
            this.productName = productName
            this._productDataService.getProductList().subscribe(res2 => {
                let product = res2.filter(item => item.name === this.productName)[0]
                if (product) {
                    this.productId = product["id"]
                    this.getProductClassSummaryReportList(this.productId)
                } else {
                    this.productId = null
                }
            })
        }
    }

    copyPath(value) {
        const selectBox = this.document.createElement('textarea');
        selectBox.style.position = 'fixed';
        selectBox.value = value;
        this.document.body.appendChild(selectBox);
        selectBox.focus();
        selectBox.select();
        this.document.execCommand('copy');
        this.document.body.removeChild(selectBox);
        this.toastr.success('', 'Copied successfully', { positionClass: 'toast-bottom-right', toastClass: 'toast ngx-toastr', closeButton: true });
    }

    getProductClassSummaryReportList(productId) {
        return this._productDataService.getSummaryReportList(productId).then((res: any) => {
            this.summaryReportList = res?.entities || []
        })
    }

    addSummary(item) {
        let labelName = item.name
        let referNode = item.path
        let params = {
            include: true,
            labelName,
            referNode,
            dataCollect: false
        }

        if (!this.productId) {
            Swal.fire({
                title: this.translateService.instant('PROVISIONING.ADDSUMMARYREPORT'),
                text: this.translateService.instant('PRODUCTS.PRODUCTNOTEXIST'),
                icon: 'warning',
                confirmButtonText: this.translateService.instant('COMMON.OK'),
                customClass: {
                    confirmButton: 'btn btn-primary',
                }
            })
        } else {
            Swal.fire({
                title: this.translateService.instant('PROVISIONING.ADDSUMMARYREPORT'),
                // html:`<div style="text-align:left">Product:${this.productName}</div><div style="text-align:left">Label Name:${labelName}</div><div style="text-align:left">Parameter Path:${referNode}</div>`,
                html: `<ul class="list-group text-truncate" style="font-size:14px;">
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>Product</span>
                <span class="text-info">${this.productName}</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>Label Name</span>
                <span class="text-info">${labelName}</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>Parameter Path</span>
                <span class="text-info ml-50" style="overflow-x:scroll;">${referNode}</span>
              </li>
            </ul>`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: this.translateService.instant('COMMON.OK'),
                cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
                customClass: {
                    confirmButton: 'btn btn-primary',
                    cancelButton: 'btn btn-danger ml-1'
                }
            }).then((result) => {
                if (result.value) {
                    let paramsList = [...this.summaryReportList, params]
                    this._productDataService.saveSummaryReportList(this.productId, { entities: paramsList }).then((res: any) => {
                        this._toastrUtilsService.showSuccessMessage(this.translateService.instant('DEVICES.ACTION.SUCCESS'), this.translateService.instant('CONFIRM.UPDATEPRODSUCC'))
                    }).catch(err => {
                        this._toastrUtilsService.showErrorMessage(this.translateService.instant('DEVICES.ACTION.FAIL'), err.error);
                    }).finally(() => {
                        this.getProductClassSummaryReportList(this.productId)
                    });
                }
            });
        }
    }
}