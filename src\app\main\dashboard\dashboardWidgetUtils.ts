import { Widget } from "app/layout/widget";
import { DeviceNumbersComponent } from "./top-mini-cards/device-numbers/device-numbers.component";
import { UserNumbersComponent } from "./top-mini-cards/user-numbers/user-numbers.component";
import { ProductsComponent } from './products/products.component';
import { UeCountComponent } from './top-mini-cards/ue-count/ue-count.component';
import { OnlineDeviceComponent } from 'app/main/analysis/analysis-device/online-device/online-device.component';
import { SessionsDurationComponent } from 'app/main/analysis/analysis-system/sessions-duration/sessions-duration.component';
import { RequestsLatencyComponent } from 'app/main/analysis/analysis-system/requests-latency/requests-latency.component';
import { SessionsRateComponent } from 'app/main/analysis/analysis-system/sessions-rate/sessions-rate.component';
import { GroupNumbersComponent } from './top-mini-cards/group-numbers/group-numbers.component';
import { DashboardCoverageMapComponent } from './dashboard-coverage-map/dashboard-coverage-map.component';
import { SystemEventsListComponent } from "../system/system-events/system-events-list/system-events-list.component";
import { DashboardAlarmCountComponent } from './top-mini-cards/dashboard-alarm-count/dashboard-alarm-count.component';
import { DashboardTotalAlarmCountComponent } from './top-mini-cards/dashboard-total-alarm-count/dashboard-total-alarm-count.component';
import { SoftwareVersionDistributionComponent } from 'app/main/analysis/analysis-provisioning/software-version-distribution/software-version-distribution.component';
import { ProvisioningCodeDistributionComponent } from 'app/main/analysis/analysis-provisioning/provisioning-code-distribution/provisioning-code-distribution.component';
import { XmppStatusComponent } from 'app/main/analysis/analysis-provisioning/xmpp-status/xmpp-status.component';
import { ImsRegistrationStatusComponent } from 'app/main/analysis/analysis-provisioning/ims-registration-status/ims-registration-status.component';
import { SimStatusComponent } from 'app/main/analysis/analysis-provisioning/sim-status/sim-status.component';
import { IpSecTunnelStatusComponent } from 'app/main/analysis/analysis-provisioning/ip-sec-tunnel-status/ip-sec-tunnel-status.component';
import { GorupListComponent } from './gorup-list/gorup-list.component';
import { AlarmListComponent } from './alarm-list/alarm-list.component';
import { OnlineProductsComponent } from './online-products/online-products.component';
import { SystemInformationComponent } from './system-information/system-information.component';
import { WifiTotalClientsComponent } from "./wifi-total-clients/wifi-total-clients.component";
import { WifiStatisticsOfTotalClientsComponent } from "./wifi-statistics-of-total-clients/wifi-statistics-of-total-clients.component";
import { WifiTotalClientsCountComponent } from './top-mini-cards/wifi-total-clients-count/wifi-total-clients-count.component';
import { GroupsLocationComponent } from "./groups-location/groups-location.component";
import { DashboardDeviceMapComponent } from "./dashboard-device-map/dashboard-device-map.component";

export const dashboardWidgets: Widget[] = [
    // Online Devices
    {
        componentId: 'DashboardModule/DeviceNumbersComponent',
        component: DeviceNumbersComponent,
        name: 'DASHBOARD.ONLINE_DEVICE',
        description: 'DASHBOARD.ONLINE_DEVICE_DESCRIPTION',
        class: 'device',
        subClass: 'generalData',
        render: false,
        hidden: false,
        cols: 6,
        rows: 3,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'count',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary',
    },
    // Groups
    {
        componentId: 'DashboardModule/GroupNumbersComponent',
        component: GroupNumbersComponent,
        name: 'DASHBOARD.GROUPS_COUNT',
        description: 'DASHBOARD.GROUPS_COUNT_DESCRIPTION',
        class: 'device',
        subClass: 'generalData',
        render: false,
        hidden: true,
        cols: 6,
        rows: 3,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'count',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Online Users
    {
        componentId: 'DashboardModule/UserNumbersComponent',
        component: UserNumbersComponent,
        name: 'DASHBOARD.ONLINE_USERS',
        description: 'DASHBOARD.ONLINE_USERS_DESCRIPTION',
        class: 'user',
        subClass: 'accountAdmin',
        render: false,
        hidden: false,
        cols: 6,
        rows: 3,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'count',
        protocalType: '',
        extension: 'user',
        data: [],
        color: 'sceondary'
    },
    // UE
    {
        componentId: 'DashboardModule/UeCountComponent',
        component: UeCountComponent,
        name: 'DASHBOARD.UE_COUNT',
        description: 'DASHBOARD.UE_COUNT_DESCRIPTION',
        class: 'device',
        subClass: 'cellularSpecific',
        render: false,
        hidden: false,
        cols: 6,
        rows: 3,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'count',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // wifi total clients count
    {
        componentId: 'DashboardModule/WifiTotalClientsCountComponent',
        component: WifiTotalClientsCountComponent,
        name: 'DASHBOARD.TOTAL_CLIENTS_COUNT',
        description: 'DASHBOARD.TOTAL_CLIENTS_COUNT_DESCRIPTION',
        class: 'device',
        subClass: 'wifiSpecific',
        render: false,
        hidden: false,
        cols: 6,
        rows: 3,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'count',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Total Alarms
    {
        componentId: 'DashboardModule/DashboardTotalAlarmCountComponent',
        component: DashboardTotalAlarmCountComponent,
        name: 'DASHBOARD.ALARMS_TOTAL',
        description: 'DASHBOARD.ALARMS_TOTAL_DESCRIPTION',
        class: 'alarm',
        subClass: 'deviceAlarm',
        render: false,
        hidden: true,
        cols: 6,
        rows: 3,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'count',
        protocalType: '',
        extension: 'alarm',
        data: [],
        color: 'sceondary'
    },

    // Alarms with Severity
    {
        componentId: 'DashboardModule/DashboardAlarmCountComponent',
        component: DashboardAlarmCountComponent,
        name: 'DASHBOARD.ALARMS_SERVERITY',
        description: 'DASHBOARD.ALARMS_SERVERITY_DESCRIPTION',
        class: 'alarm',
        subClass: 'deviceAlarm',
        render: false,
        hidden: false,
        cols: 12,
        rows: 3,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'count',
        protocalType: '',
        extension: 'alarm',
        data: [],
        color: 'sceondary'
    },
    // Group List
    {
        componentId: 'GroupsModule/GorupListComponent',
        component: GorupListComponent,
        name: 'DASHBOARD.GROUP_LIST',
        description: 'DASHBOARD.GROUP_LIST_DESCRIPTION',
        class: 'device',
        subClass: 'generalData',
        render: false,
        hidden: true,
        cols: 18,
        rows: 12,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'table',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Coverage Map
    {
        componentId: 'GroupsModule/DashboardCoverageMapComponent',
        component: DashboardCoverageMapComponent,
        name: 'DASHBOARD.COVERMAP',
        description: 'DASHBOARD.COVERMAP_DESCRIPTION',
        class: 'device',
        subClass: 'networkLocation',
        render: false,
        hidden: true,
        cols: 18,
        rows: 12,
        x: 0,
        y: 0,
        maxItemCols: 18,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'map',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Groups Location 
    {
        componentId: 'DashboardModule/GroupsLocationComponent',
        component: GroupsLocationComponent,
        name: 'DASHBOARD.GROUPSLOCATION',
        description: 'DASHBOARD.GROUPSLOCATION_DESCRIPTION',
        class: 'device',
        subClass: 'networkLocation',
        render: false,
        hidden: true,
        cols: 18,
        rows: 12,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'map',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Device Location 
    {
        componentId: 'DashboardModule/DashboardDeviceMapComponent',
        component: DashboardDeviceMapComponent,
        name: 'DASHBOARD.DEVICESLOCATION',
        description: 'DASHBOARD.DEVICESLOCATION_DESCRIPTION',
        class: 'device',
        subClass: 'networkLocation',
        render: false,
        hidden: true,
        cols: 18,
        rows: 12,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'map',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Alarm List
    {
        componentId: 'EventsModule/AlarmListComponent',
        component: AlarmListComponent,
        name: 'DASHBOARD.ALARM_LIST',
        description: 'DASHBOARD.ALARM_LIST_DESCRIPTION',
        class: 'alarm',
        subClass: 'deviceAlarm',
        render: false,
        hidden: true,
        cols: 36,
        rows: 15,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'table',
        protocalType: '',
        extension: 'alarm',
        data: [],
        color: 'sceondary'
    },
    // Online Count Distribution
    {
        componentId: 'DashboardModule/OnlineProductsComponent',
        component: OnlineProductsComponent,
        name: 'DASHBOARD.ONLINE_COUNT_DISTRIBUTION',
        description: 'DASHBOARD.ONLINE_COUNT_DISTRIBUTION_DESCRIPTION',
        class: 'device',
        subClass: 'generalData',
        render: false,
        hidden: false,
        cols: 12,
        rows: 7,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Provisioning Code Distribution
    {
        componentId: 'Analysis/ProvisioningCodeDistributionComponent',
        component: ProvisioningCodeDistributionComponent,
        name: 'DASHBOARD.PROVISIONING_CODE_DISTRIBUTION',
        description: 'DASHBOARD.PROVISIONING_CODE_DISTRIBUTION_DESCRIPTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 12,
        rows: 7,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary'
    },
    // Software Version Distribution
    {
        componentId: 'Analysis/SoftwareVersionDistributionComponent',
        component: SoftwareVersionDistributionComponent,
        name: 'DASHBOARD.SOFTWARE_VERSION_DISTRIBUTION',
        description: 'DASHBOARD.SOFTWARE_VERSION_DISTRIBUTION_DESCRIPTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: false,
        cols: 12,
        rows: 7,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary'
    },
    // Session Rate
    {
        componentId: 'AnalysisModule/SessionsRateComponent',
        component: SessionsRateComponent,
        name: 'DASHBOARD.SESSIONRATE',
        description: 'DASHBOARD.SESSIONRATEDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary'
    },
    // Session Duration
    {
        componentId: 'AnalysisModule/SessionsDurationComponent',
        component: SessionsDurationComponent,
        name: 'DASHBOARD.SESSIONDURATION',
        description: 'DASHBOARD.SESSIONDURATIONDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary'
    },
    // Request Latency
    {
        componentId: 'AnalysisModule/RequestsLatencyComponent',
        component: RequestsLatencyComponent,
        name: 'DASHBOARD.LATENCY',
        description: 'DASHBOARD.LATENCYDESCRIPTION',
        class: 'analysis',
        subClass: 'systemStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'lineChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary'
    },
    // total clients chart
    {
        componentId: 'DashboardModule/WifiTotalClientsComponent',
        component: WifiTotalClientsComponent,
        name: 'DASHBOARD.TOTAL_CLIENTS',
        description: 'DASHBOARD.TOTAL_CLIENTS_DESCRIPTION',
        class: 'device',
        subClass: 'wifiSpecific',
        render: false,
        hidden: true,
        cols: 12,
        rows: 6,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary',
    },
    // Online Devices History
    {
        componentId: 'AnalysisModule/OnlineDeviceComponent',
        component: OnlineDeviceComponent,
        name: 'DASHBOARD.HISTORYONLINEDEVICE',
        description: 'DASHBOARD.ONLINEDEVICEDESCRIPTION',
        class: 'analysis',
        subClass: 'deviceStatistics',
        render: false,
        hidden: false,
        cols: 9,
        rows: 6,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary'
    },
    // statistics of total clients
    {
        componentId: 'AnalysisModule/WifiStatisticsOfTotalClientsComponent',
        component: WifiStatisticsOfTotalClientsComponent,
        name: 'DASHBOARD.STATISTICSOFTOTALCLIENTS',
        description: 'DASHBOARD.STATISTICSOFTOTALCLIENTS_DESCRIPTION',
        class: 'device',
        subClass: 'wifiSpecific',
        render: false,
        hidden: true,
        cols: 18,
        rows: 7,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // Registered Count Distribution
    {
        componentId: 'DashboardModule/ProductsComponent',
        component: ProductsComponent,
        name: 'DASHBOARD.REGISTERED_COUNT_DISTRIBUTION',
        description: 'DASHBOARD.REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION',
        class: 'device',
        subClass: 'productAdmin',
        render: false,
        hidden: true,
        cols: 18,
        rows: 10,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'device',
        data: [],
        color: 'sceondary'
    },
    // XMPP Status Distribution
    {
        componentId: 'Analysis/XmppStatusComponent',
        component: XmppStatusComponent,
        name: 'DASHBOARD.XMPP_STATUS_DISTRIBUTION',
        description: 'DASHBOARD.XMPP_STATUS_DISTRIBUTION_DESCRIPTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: true,
        cols: 12,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary'
    },
    // IMS Status Distribution
    {
        componentId: 'Analysis/ImsRegistrationStatusComponent',
        component: ImsRegistrationStatusComponent,
        name: 'DASHBOARD.IMS_STATUS_DISTRIBUTION',
        description: 'DASHBOARD.IMS_STATUS_DISTRIBUTION_DESCRIPTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: true,
        cols: 12,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary',
    },
    // SIM Status Distribution
    {
        componentId: 'Analysis/SimStatusComponent',
        component: SimStatusComponent,
        name: 'DASHBOARD.SIM_STATUS_DISTRIBUTION',
        description: 'DASHBOARD.SIM_STATUS_DISTRIBUTION_DESCRIPTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: true,
        cols: 12,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: 'analysis',
        data: [],
        color: 'sceondary',
    },
    // IPSec Status Distribution
    /* {
        componentId: 'Analysis/IpSecTunnelStatusComponent',
        component: IpSecTunnelStatusComponent,
        name: 'DASHBOARD.IPSEC_STATUS_DISTRIBUTION',
        description: 'DASHBOARD.IPSEC_STATUS_DISTRIBUTION_DESCRIPTION',
        class: 'analysis',
        subClass: 'provisioningStatistics',
        render: false,
        hidden: true,
        cols: 12,
        rows: 5,
        y: 0,
        x: 0,
        minItemCols: 6,
        minItemRows: 2,
        grade: '',
        dmsType: 'pieChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    }, */
    // SystemInformationComponent
    {
        componentId: 'SystemModule/SystemInformationComponent',
        component: SystemInformationComponent,
        name: 'DASHBOARD.STSTEM_INFORMATIONS',
        description: 'DASHBOARD.STSTEM_INFORMATIONS_DESCRIPTION',
        class: 'system',
        subClass: 'systemInformation',
        render: false,
        hidden: true,
        cols: 12,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'keyValue',
        protocalType: '',
        extension: 'system',
        data: [],
        color: 'sceondary',
    },
    // System event List
    {
        componentId: 'SystemModule/SystemEventsListComponent',
        component: SystemEventsListComponent,
        name: 'DASHBOARD.SYSTEM_EVENT_LIST',
        description: 'DASHBOARD.SYSTEM_EVENT_LIST_DESCRIPTION',
        class: 'system',
        subClass: 'systemEvents',
        render: false,
        hidden: true,
        cols: 36,
        rows: 15,
        x: 0,
        y: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'table',
        protocalType: '',
        extension: 'system',
        data: [],
        color: 'sceondary',
    },
]