import { Component, OnInit, ViewChild, ViewEncapsulation, ElementRef, Input, AfterViewChecked, ChangeDetectorRef } from '@angular/core';
import { ColumnMode, DatatableComponent, SelectionType } from '@almaobservatory/ngx-datatable';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { ProductDataService } from 'app/main/products/products.service';
import { DataStoreService } from 'app/main/commonService/data-store.service';
import { TranslateService } from '@ngx-translate/core';
import { GroupDataService } from 'app/main/groups/group-data.service'
import { DatatableCustomizeService } from 'app/main/commonService/datatable-customize.service';
import { timer, Subscription, BehaviorSubject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { ResizeObservableService } from 'app/main/commonService/resize-observable.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import cloneDeep from 'lodash/cloneDeep';
import { GroupEventsService } from 'app/main/groups/group-events/group-events.service';

@UntilDestroy()
@Component({
  selector: 'app-gorup-list',
  templateUrl: './gorup-list.component.html',
  styleUrls: ['./gorup-list.component.scss']
})
export class GorupListComponent implements OnInit {
  @Input() accessor: any;
  public selectedOption = 10;
  public ColumnMode = ColumnMode;
  public SelectionType = SelectionType;
  public rowCount = 10;
  public pageSize = 10;
  public selectedCount = 10;
  public curPage = 1;
  public offset = 0;
  public rows: any;
  public tempData: any;
  public columnSelectOptions = {
    "groupList_Name": true,
    "groupList_Devices": true,
    "groupList_Product": true,
    "groupList_Protocol": true,
    'groupList_Create By': false,
    'groupList_Create Time': false,
    'groupList_Alarms': true,
    'groupList_Location':true,
    'groupList_Tags':true,
    'groupList_ViewLocation':true,
    'groupList_ViewCoverageMap':true
  }
  public deploymentModeMapping={
    '1':'Radio Access',
    '2':'WiFi AP',
    '3':'WiFi Mesh'
    // {value:2,name:'WiFi AP'},
    // {value:3,name:'WiFi Mesh'},
  }
  public tableOption = [
    { name: 'groupList_Name', prop: 'name', translate: 'COMMON.NAME', width: 120, flexGrow: 120, columnStatus: true },
    { name: 'groupList_Product', prop: 'productName', translate: 'PROVISIONING.PRODUCT', width: 100, flexGrow: 100, columnStatus: false },
    { name: 'groupList_DeploymentMode', prop: 'deploymentMode', translate: 'GROUPS.NETWORK', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'groupList_Devices', prop: 'number', translate: 'GROUPS.DEVICECOUNT', width: 80, flexGrow: 80, columnStatus: true },
    { name: 'groupList_OnlineRate', prop: 'onlineRate', translate: 'GROUPS.ONLINERATE', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'groupList_ServiceAvailability', prop: 'serviceAvailability', translate: 'GROUPS.SERVICEAVAILABILITY', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'groupList_Protocol', prop: 'protocol', translate: 'DEVICES.PROTOCOL', width: 120, flexGrow: 120, columnStatus: false },
    { name: 'groupList_Create By', prop: 'creator', translate: 'GROUPS.CREATEBY', width: 100, flexGrow: 100, columnStatus: false },
    { name: 'groupList_Create Time', prop: 'established', translate: 'GROUPS.CREATETIME', width: 100, flexGrow: 100, columnStatus: false },
    { name: 'groupList_Alarms', prop: 'alarmList', translate: 'GROUPS.ALARMCOUNT', width: 150, flexGrow: 150, columnStatus: true },
    { name: 'groupList_Tags', prop: 'tags', translate: 'DEVICES.TAGS', width: 120, flexGrow: 120, columnStatus: true },
    { name: 'groupList_Location', prop: 'location', translate: 'GROUPS.LOCATION', width: 150, flexGrow: 120, columnStatus: true },
    { name: 'groupList_ViewLocation', prop: 'viewLocation', translate: 'GROUPS.VIEWLOCATION', width: 150, flexGrow: 50, columnStatus: true },
    { name: 'groupList_ViewCoverageMap', prop: 'viewCoverageMap', translate: 'GROUPS.VIEWCOVERAGEMAP', width: 150, flexGrow: 50, columnStatus: true },
  ];

  public tableWidth = 0;
  public gridScrollHeight = 0;
  public scrollSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  public tableOptionStatus = false;
  public searchValue = ''
  public previousScopeFilter = ''
  public previousTypeFilter = ''
  public previousNameFilter = ''
  public previousProtocolFilter = ''
  public temp = []
  public filterKeys = {
    "name": "name",
    "CPE Number": "number",
    "Created By": "creator",
    "Created Time": "established"
  }
  public selectScope: any = [];
  public selectedScope = [];
  public selectType = [{ "name": "All Type", "value": "" }, { "name": "user", "value": "user" }, { "name": "device", "value": "device" }]
  public selectedProtocol: any = [];
  public selectProtocol = [
    { name: 'CWMP (TR-069)', value: 'cwmp' },
    { name: 'USP (TR-369)', value: 'usp' },
    { name: 'NETCONF', value: 'netconf' },
  ]
  public selectedType = [];
  public operationList = [];
  public groupRPCSshow = true;
  public selectedGroupId: any = "";
  public selectedGroupName: any = '';
  public isPending = false;
  public blockUIStatus = false;
  public protocolType: any = 0;
  public modalUpConfig: any;
  public sNoList = [];
  public currentTargetData: any;
  public queryParams = {
    filter: {
      'name': '',
      'productName': '',
      'protocol': '',
    },
    page: 1,
    size: 10,
  }
  public selected = [];
  public content: any;
  public pageCount: number

  constructor(
    private _groupListService: GroupListService,
    private _dataStoreService: DataStoreService,
    private _productDataService: ProductDataService,
    private translateService: TranslateService,
    private _groupDataService: GroupDataService,
    private _datatableCustomizeService: DatatableCustomizeService,
    private _resizeObservableService: ResizeObservableService,
    private cdr: ChangeDetectorRef,
    private _groupEventsService: GroupEventsService,
  ) {
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CRITICAL = this.translateService.instant('ALARMS.CRITICAL');
      this.MAJOR = this.translateService.instant('ALARMS.MAJOR');
      this.MINOR = this.translateService.instant('ALARMS.MINOR');
      this.WARNING = this.translateService.instant('ALARMS.WARNING');
      this.INDETERMINATE = this.translateService.instant('ALARMS.INDETERMINATE');
    })

    this.getProductListDataRes()
    this.getGroupsDeviceListRes(this.cloneParams(this.queryParams))
  }
  public CRITICAL = this.translateService.instant('ALARMS.CRITICAL');
  public MAJOR = this.translateService.instant('ALARMS.MAJOR');
  public MINOR = this.translateService.instant('ALARMS.MINOR');
  public WARNING = this.translateService.instant('ALARMS.WARNING');
  public INDETERMINATE = this.translateService.instant('ALARMS.INDETERMINATE');

  @ViewChild('groupFormDetails') groupFormDetails: any;
  @ViewChild('tableRowDetails') groupDataDetails: any;


  ngOnInit(): void {
    this.tableOptionStatus = !!this._datatableCustomizeService.getTableOption(this.accessor.componentId);
    this.tableOption = this._datatableCustomizeService.mergeOption(this.accessor.componentId, this.tableOption);
    const dueTime = this.tableOptionStatus ? 400 : 0;
    this.scrollSubject.pipe(untilDestroyed(this), debounceTime(dueTime)).subscribe(res => {
      if (res.gridScrollHeight && res.tableWidth && !this._resizeObservableService.windowResizeState) {
        this.tableOption = this._datatableCustomizeService.formatColumn(this.groupDataDetails, this.tableOption, this.tableWidth, this.accessor);
      }
    })
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getProductListDataRes()
      this.getGroupsDeviceListRes(this.cloneParams(this.queryParams))
    });
  }

  ngOnDestroy(): void {
  }

  ngAfterViewChecked(): void {
    const { scrollHeight, innerWidth: tableWidth } = this.groupDataDetails.bodyComponent;
    const { scrollHeight: gridScrollHeight } = this.accessor.gridRef?.el;
    if (((gridScrollHeight && gridScrollHeight > this.gridScrollHeight) || (tableWidth && tableWidth !== this.tableWidth)) && scrollHeight) {
      this.tableWidth = tableWidth;
      this.gridScrollHeight = gridScrollHeight;
      this.scrollSubject.next({ gridScrollHeight, tableWidth });
    }
    this.cdr.detectChanges();
  }

  reloadGroupList(event) {
    if (typeof event === 'string') {
      this.checkAlarms(event)
    } else {
      this.getGroupsDeviceListRes(this.cloneParams(this.queryParams))
    }
  }

  checkAlarms(groupId) {
    this._groupEventsService.getActAlarms(groupId).pipe(untilDestroyed(this)).subscribe(res => {
      this._groupEventsService.actAlarmObservable = null
      this.getGroupsDeviceListRes(this.cloneParams(this.queryParams))
    })
  }

  changeColumn(event): void {
    if (event.cloumns) {
      this.tableOption = [...event.cloumns];
    }
  }

  onResize({ column, newValue }) {
    this.tableOption = this._datatableCustomizeService.resize(column, newValue, this.tableOption, this.accessor, this.groupDataDetails);
  }

  reload(e) {
    if (e === "reload") {
      this.getGroupsDeviceListRes(this.cloneParams(this.queryParams))
    }
  }

  
  filterFn(condition) {
    this._dataStoreService.setQueryParams('group', this.queryParams);
    this.checkFilter(condition);
    this.queryParams.page = 1;
    this.getGroupsDeviceListRes(this.cloneParams(this.queryParams))
  }
  

  cloneParams(queryParams) {
    let cloneQueryParamsClone
    cloneQueryParamsClone = cloneDeep(queryParams)
    for (let key in cloneQueryParamsClone.filter) {
      if (cloneQueryParamsClone.filter[key] == '') {
        delete cloneQueryParamsClone.filter[key]
      }
    }
    cloneQueryParamsClone.filter = JSON.stringify(cloneQueryParamsClone.filter)
    cloneQueryParamsClone.page = cloneQueryParamsClone.page - 1
    // console.log('queryParamsClone', cloneQueryParamsClone)
    return cloneQueryParamsClone
  }

  checkFilter(condition: string) {
    for (let key in this.queryParams.filter) {
      if (key !== condition) {
        this.queryParams.filter[key] = ''
      }
    }
  }

  getGroupsDeviceListRes(param) {
    this.blockUIStatus = true;
    let params = {
      params: param
    };
    this._groupListService.getDeviceGroupsList(params).pipe(untilDestroyed(this)).subscribe({
      next: (res:any) => {
        this.rows = res.data && res.data.map(item => {
          if (!item.protocol) {
            item.protocol = ''
          }
          if (!item.hasOwnProperty("alarmCount")) {
            item.alarmCount = 'N/A';
          }

          if(item.hasOwnProperty('address') && item.address ){
            item.location=`${item.address.city?item.address.city:''} ${item.address.state?item.address.state:''} ${item.address.zipCode?item.address.zipCode:''}`
          }

          if(item.number!==0){
            
            if(item.onlineCount===0){
              item.onlineRate=`0% (0/${item.number})`
            }else{
              item.onlineRate = `${Math.floor((item.onlineCount / item.number) * 100)}% (${item.onlineCount}/${item.number})`
            }
            // console.log(item.onlineRate)
          }

          if(item.inService){
            if(item.inService!==0){
              item.serviceAvailability=`${Math.floor((item.inService / item.onlineCount) * 100)}%  (${item.inService}/${item.onlineCount})`
            }else{
              item.serviceAvailability=`0% (0/${item.onlineCount})`
            }
          }

          item.viewLocation=(item.address && item.address.lat!==0 && item.address.lng!==0)?true:false
          item.viewCoverageMap=item.groupMapId?true:false

          // console.log(item.serviceAvailability)
 
          return item
        }) || [];
        this.tempData = this.rows;
        this.pageCount = res.num || 0;
      },
      error: error => {
        console.log(error);
        this.blockUIStatus = false;
      },
      complete: () => {
        this.blockUIStatus = false;
        this.groupDataDetails.selected = []
        this.selected = []
      }
    })
  }

  onFooterPage(e) {
    this.queryParams.page = e.page
    this.getGroupsDeviceListRes(this.cloneParams(this.queryParams))
  }

  /**
 * update column select status
 * @param columnName
 */
  updateColumnSelect(columnName) {
    this.columnSelectOptions[columnName] = !this.columnSelectOptions[columnName]
  }

  getProductListDataRes() {
    this._productDataService.getProductList().pipe(untilDestroyed(this)).subscribe(res => {
      this.sortOption(res)
    })
  }

  sortOption(list) {
    this.selectScope = list.sort((a, b) => {
      if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
      return 1
    }).map((item) => {
      return {
        'name': item.name,
        'value': item.name
      }
    })
    this.selectScope.unshift({ name: 'All Products', value: '' })
  }

  addGroupsCallback(e) {
    this.getGroupsDeviceListRes(this.cloneParams(this.queryParams));
  }


  lookCoverageMap(groupId) {
    // console.log(groupId)
    this._groupDataService.setGroupId(groupId);
  }

  lookGroupLocation(groupId) {
    // console.log(groupId)
    this._groupDataService.setGroupId(groupId);
  }


  spanMouseenter(ev, obj) {
    if (ev.target.offsetWidth >= ev.target.scrollWidth && ev.target.offsetHeight >= ev.target.scrollHeight) {
      this.content = []
    } else {
      let alarmList = []
      for (let i in obj.alarmList) {
        if (i.toUpperCase() == 'CRITICAL') {
          alarmList.push({ name: this.CRITICAL, value: obj.alarmList[i] })
        }
        if (i.toUpperCase() == 'MAJOR') {
          alarmList.push({ name: this.MAJOR, value: obj.alarmList[i] })
        }
        if (i.toUpperCase() == 'MINOR') {
          alarmList.push({ name: this.MINOR, value: obj.alarmList[i] })
        }
        if (i.toUpperCase() == 'WARNING') {
          alarmList.push({ name: this.WARNING, value: obj.alarmList[i] })
        }
        if (i.toUpperCase() == 'INDETERMINATE') {
          alarmList.push({ name: this.INDETERMINATE, value: obj.alarmList[i] })
        }
      }
      this.content = alarmList.filter(item => item.value > 0)
    }
  }

}
