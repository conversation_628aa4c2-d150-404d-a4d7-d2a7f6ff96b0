<!-- Footer -->
<!-- 结构1 -->
<p class="d-flex align-items-center justify-content-between" *ngIf="false">
  <span class="float-md-left d-block d-md-inline-block mt-25">
    COPYRIGHT © {{ year }}
    <span class="ml-25 text-primary">ASKEY Computer Corp.</span>
    <span class="d-none d-sm-inline-block">, All Rights Reserved</span>
  </span>
  <span class="float-md-right d-none d-md-block">
    <div class="d-flex align-items-center">
      <div class="d-flex" style="flex-direction: column;padding: 0 0.5rem;">
        <span class="mr-25 text-success">{{clock | date:'MM/dd/yy, HH:mm:ss'}}</span>
      
        
        <!-- <b>{{ 'COMMON.VERSION' | translate }}:v4.0.7develop{{softWareVersion}}</b> -->
        <div class="media-body">
          <b
              class="transaction-title">
              <label class="m-0"> UI:</label>
              v4.0.12develop
          </b>
          <b
              class="transaction-title">
              <label class="m-0"> ACS:</label>
                {{softWareVersion}}
          </b>
        </div>
      </div>

      <span class="manual" *ngIf="this.currentUser">
        <a
          (click)="helpDocument()"
          *ngIf="this.currentUser.role != 'CSR'"
          class
          placement="left"
          ngbTooltip="Help document">
          <span
            [data-feather]="'info'"
            class="ficon"></span>
        </a>
      </span>
    </div>
  </span>
</p>


  <!-- 结构2 -->
  <p class="clearfix mb-0" *ngIf="true">
    <span class="float-md-left d-block d-md-inline-block mt-25">
      COPYRIGHT &copy; {{ year }}
      <span class="ml-25 text-primary">ASKEY Computer Corp.</span>
      <span class="d-none d-sm-inline-block">, All Rights Reserved</span>
    </span>
    <span class="float-md-right d-none d-md-block">
      <div class="d-flex align-items-center">
        <span class="mr-25 text-success">{{clock | date:'MM/dd/yy, HH:mm:ss'}}</span>
        <!-- {{ 'COMMON.VERSION' | translate }}:
      <b>v4.0.7develop</b>,<span>{{softWareVersion}}</span> -->
        <!-- UI:
      <b>v3.99</b> -->
        <div class="media-body d-flex" style="padding: 0 0.5rem;">
          <b class="mr-50">
              v4.0.12develop
          </b>
          /
          <b class="ml-50">
              {{softWareVersion}}
          </b>
        </div>
  
        <span class="manual" *ngIf="this.currentUser">
          <a
            (click)="helpDocument()"
            *ngIf="this.currentUser.role != 'CSR'"
            class
            placement="left"
            ngbTooltip="Help document">
            <span
              [data-feather]="'info'"
              class="ficon"></span>
          </a>
        </span>
      </div>
      
      
    </span>
      <!-- <span
        [data-feather]="'pocket'"
        [class]="'pink'"></span> -->
      
      
      
      <!-- helpDocument -->
      </p>
<!-- / Footer -->
<!-- Move to top Button -->
<app-scroll-top *ngIf="coreConfig.layout.scrollTop"></app-scroll-top>
<!-- editMode Save Button -->
<div class="edit-mode-save">
  <a
    (click)="cancel()"
    target="_blank"
    class="btn mr-1"
    *ngIf="coreConfig.layout.editMode">
    {{'COMMON.CANCEL'| translate}}
  </a>
  <a
    (click)="save()"
    target="_blank"
    class="btn"
    *ngIf="coreConfig.layout.editMode">
    {{'COMMON.SAVE'| translate}}
  </a>
</div>
