import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import isObject from 'lodash/isObject';
import { flattenJsonHelper } from './tools/flattenJsonHelper';
import { highlightUtils } from '../shared/log-parsing/cwmp/cwmpHighlightUtils';

interface HighlightPosition {
  outerIndex: number;
  innerHighlightIndex: number;  
  virtualScrollIndex: number;    
}

interface HighlightMatch extends HighlightPosition {
  text: string;
  globalIndex: number; 
  qid?: string;  
}

interface FieldConfig {
  name: string;
  text?: string;
  templates?: FieldConfig[];
  pre?: string;
  isVirtualScroll?: boolean;
  overall?: string;
  valueMapping?: { [key: string]: string };
}

@Injectable({
  providedIn: 'root'
})
export class HighlightManagerService {
  private currentIndexSubject = new BehaviorSubject<number>(-1);
  currentIndex$ = this.currentIndexSubject.asObservable();

  private totalMatchesSubject = new BehaviorSubject<number>(0);
  totalMatches$ = this.totalMatchesSubject.asObservable();

  private scrollToIndexSubject = new BehaviorSubject<{ outer: number, innerHighlight: number, virtualScroll: number } | null>(null);
  scrollToIndex$ = this.scrollToIndexSubject.asObservable();
  private filteredDataSubject = new BehaviorSubject<any[]>([]);
  filteredData$ = this.filteredDataSubject.asObservable();

  private allMatches: HighlightMatch[] = [];
  private positionMap = new Map<string, number>();
  private groupHighlightCountMap = new Map<number, number>();  
  private matchPositionMap = new Map<string, number>();

  constructor(
    private flattenJsonHelper: flattenJsonHelper,
  ) { }

  preProcessData(data: any[], searchText: string): void {
    this.resetAll();
    if (!searchText) {
      this.filteredDataSubject.next(data);
      return;
    }
    const escapedSearchText = this.escapeRegExp(searchText);
    const regex = new RegExp(escapedSearchText, 'gi');
    const matches: HighlightMatch[] = [];
    const filteredData: any[] = [];

    data.forEach((group, outerIndex) => {
      let hasMatch = false; 
      this.groupHighlightCountMap.set(outerIndex, 0);  
      const keys = Object.keys(group);
      const hasRpcItem = group.hasOwnProperty('rpcItem') && group.rpcItem[0];
      keys.forEach(key => {
        if (['messageType'].includes(key)) {
          if (this.collectMatches(group[key], outerIndex, -1, regex, matches, `${group.qid}messageType`)) {
            hasMatch = true;
          }
        }
        if (['methodSource'].includes(key) && isObject(group[key])) {
          Object.keys(group[key]).forEach(subKey => {
            if (this.collectMatches(group[key][subKey], outerIndex, -1, regex, matches, `${group[key]['qid']}${subKey}`)) {
              hasMatch = true;
            }
          });
        }
        if (key === 'cwmpMessage' && group[key].hasOwnProperty('events') && Array.isArray(group[key].events)) {
          group[key].events.forEach((event, idx) => {
            if (this.collectMatches(event, outerIndex, -1, regex, matches, `${group[key]['qid']}events${idx}`)) {
              hasMatch = true;
            }
          });
        } 
        if (!hasRpcItem && key === 'cwmpMessage') {
          const nodes = this.flattenJsonHelper.formatNodes(group[key]);
          nodes.forEach((node, virtualScrollIndex) => {
            if (this.collectMatches(node.key, outerIndex, virtualScrollIndex, regex, matches, '') ||
              this.collectMatches(node.value, outerIndex, virtualScrollIndex, regex, matches, '')) {
              hasMatch = true;
            }
          });
        } else if (hasRpcItem && key === 'cwmpMessage') {
          if (['GetParameterValues', 'GetParameterAttributes'].includes(group[key].messageType)) {
            const message = group[key]?.message || group[key];
            const params = message?.parameterNames || message?.paramNames || [];
            if (typeof params[0] === 'string') {
              message.parameterNames = params.map((name, id) => ({ name, id }));
            }
          }
          if (this.cwmpCollectMatches(group[key], outerIndex, regex, matches)) {
            hasMatch = true;
          }
        } 
      });
      if (hasMatch) {
        filteredData.push(group);
      }
    });
    matches.forEach((match, index) => {
      match.globalIndex = index;
      const key = this.getPositionKey(match.outerIndex, match.innerHighlightIndex, match.virtualScrollIndex);
      this.positionMap.set(key, index);
    });
    this.allMatches = matches;
    this.totalMatchesSubject.next(this.allMatches.length);
    this.filteredDataSubject.next(filteredData);
    if (this.allMatches.length > 0) {
      this.navigateToIndex(0);
    } else {
      this.currentIndexSubject.next(-1);
      this.scrollToIndexSubject.next({
        outer: -1,
        innerHighlight: -1,
        virtualScroll: -1
      });
    }
  }
  private collectMatches(
    text: string,
    outerIndex: number,
    virtualScrollIndex: number,
    regex: RegExp,
    matches: HighlightMatch[],
    qid: string
  ): boolean {
    let match;
    let hasMatch = false;
    regex.lastIndex = 0;

    while ((match = regex.exec(text)) !== null) {
      hasMatch = true;
      qid = virtualScrollIndex === -1 ? qid : '';
      const key = this.getOrCreateMatchPositionKey(
        outerIndex,
        virtualScrollIndex,
        match.index,
        match[0],
        qid
      );
      
      matches.push({
        outerIndex,
        innerHighlightIndex: this.matchPositionMap.get(key) || 0,
        virtualScrollIndex,
        text: match[0],
        globalIndex: -1,
        qid
      });
    }
    return hasMatch;
  }
  private processTemplate(field: FieldConfig, item: any, outerIndex: number, virtualScrollIndex: number, regex: RegExp, matches: HighlightMatch[]): boolean {
    let hasMatch = false;
    const value = item[field.name];
    if (field.pre) {
      hasMatch = this.collectMatches(field.pre, outerIndex, virtualScrollIndex, regex, matches, `${item.qid}${field.name}Pre`) || hasMatch;
    }
    if (value === undefined || value === null) return false;
    if (Array.isArray(value) && !field.overall) {
      hasMatch = this.processItems(field, value, outerIndex, field.isVirtualScroll || false, regex, matches, item.qid) || hasMatch;
    } else if (field.templates) {
      field.templates.forEach(tpl => {
        hasMatch = this.processTemplate(tpl, value, outerIndex, virtualScrollIndex, regex, matches) || hasMatch;
      });
    } else {
      let realValue = field.valueMapping && Object.keys(field.valueMapping).includes(value.toString()) ? field.valueMapping[value.toString()] : value;
      realValue = field.overall ? realValue.join(field.overall) : realValue;
      const formatted = field.text.replace('{value}', realValue);
      hasMatch = this.collectMatches(formatted, outerIndex, virtualScrollIndex, regex, matches, `${item.qid}${field.name}`) || hasMatch;
    }
    return hasMatch;
  }
  private processItems(field: FieldConfig, items: any[], outerIndex: number, virtualScroll: boolean, regex: RegExp, matches: HighlightMatch[], qid: string): boolean {
    let hasMatch = false;
    items.forEach((item, idx) => {
      const virtualScrollIndex = virtualScroll ? idx : -1;
      if (typeof item === 'string') {
        const formatted = field.text.replace('{value}', item);
        hasMatch = this.collectMatches(formatted, outerIndex, virtualScrollIndex, regex, matches, `${qid}${field.name}${idx}`) || hasMatch;
      } else if (field.templates) {
        field.templates.forEach(tpl => {
          hasMatch = this.processTemplate(tpl, item, outerIndex, virtualScrollIndex, regex, matches) || hasMatch;
        });
      }
    });
    return hasMatch;
  }
  private cwmpCollectMatches(cwmpMessage: any, outerIndex: number, regex: RegExp, matches: HighlightMatch[]): boolean {
    let hasMatch = false;
    const util = highlightUtils.find(item => item.name === cwmpMessage.messageType);
    if (!util?.fields) return false;
    util.fields.forEach(field => {
      hasMatch = this.processTemplate(field, cwmpMessage, outerIndex, -1, regex, matches) || hasMatch;
    });
    return hasMatch;
  }
  private getOrCreateMatchPositionKey(outerIndex: number, virtualScrollIndex: number, matchIndex: number, matchText: string, qid: string): string {
    const key = `${outerIndex}:${virtualScrollIndex}:${matchIndex}:${matchText}:${qid}`;
    if (this.matchPositionMap.has(key)) {
      return key;
    }
    const currentCount = this.groupHighlightCountMap.get(outerIndex) || 0;
    this.matchPositionMap.set(key, currentCount);
    this.groupHighlightCountMap.set(outerIndex, currentCount + 1);
    return key;
  }
  getInnerHighlightIndex(outerIndex: number, virtualScrollIndex: number, matchIndex: number, matchText: string, qid: string): number {
    const key = this.getOrCreateMatchPositionKey(outerIndex, virtualScrollIndex, matchIndex, matchText, qid);
    return this.matchPositionMap.get(key) ?? -1;
  }
  private getPositionKey(outer: number, innerHighlight: number, virtualScroll: number): string {
    const normalizedVirtualScroll = virtualScroll === undefined ? -1 : virtualScroll;
    return `${outer}:${innerHighlight}:${normalizedVirtualScroll}`;
  }

  next(): void {
    const currentIndex = this.currentIndexSubject.value;
    if (currentIndex < this.allMatches.length - 1) {
      this.navigateToIndex(currentIndex + 1);
    } else {
      this.navigateToIndex(0);
    }
  }

  previous(): void {
    const currentIndex = this.currentIndexSubject.value;
    if (currentIndex > 0) {
      this.navigateToIndex(currentIndex - 1);
    } else {
      this.navigateToIndex(this.allMatches.length - 1);
    }
  }

  private navigateToIndex(index: number): void {
    if (index >= 0 && index < this.allMatches.length) {
      this.currentIndexSubject.next(index);
      const match = this.allMatches[index];
      if (match) {
        this.scrollToMatch(match);
      }
    }
  }
  getCurrentMatch(): HighlightMatch | null {
    const currentIndex = this.currentIndexSubject.value;
    return this.allMatches[currentIndex] || null;
  }
  private scrollToMatch(match: HighlightMatch): void {
    if (match) {
      this.scrollToIndexSubject.next({
        outer: match.outerIndex,
        innerHighlight: match.innerHighlightIndex,
        virtualScroll: match.virtualScrollIndex
      });
    }
  }

  private escapeRegExp(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
  private removeAllHighlights(): void {
    document.querySelectorAll('.current-keyword').forEach(el => {
      el.classList.remove('current-keyword');
    });
  }
  resetAll(): void {
    this.allMatches = [];
    this.positionMap.clear();
    this.currentIndexSubject.next(-1);
    this.totalMatchesSubject.next(0);
    this.scrollToIndexSubject.next(null);
    this.filteredDataSubject.next([]);
    this.removeAllHighlights();
  }
  resetCount(): void {
    this.groupHighlightCountMap.clear();
    this.matchPositionMap.clear();
  }
}