import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WifiClientsStatusModalComponent } from './wifi-clients-status-modal.component';

describe('WifiClientsStatusModalComponent', () => {
  let component: WifiClientsStatusModalComponent;
  let fixture: ComponentFixture<WifiClientsStatusModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ WifiClientsStatusModalComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(WifiClientsStatusModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
