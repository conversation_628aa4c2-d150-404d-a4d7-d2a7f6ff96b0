import { Directive, OnDestroy, OnInit } from "@angular/core";
import { Observable, Subject } from "rxjs";
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Directive()
export class Unsubscribe implements OnInit, OnDestroy{

    constructor() {
    }

    customSubscribe(observer: Observable<any>, callback: (value: any) => void) {
        observer.pipe(untilDestroyed(this)).subscribe(callback);
    }

    ngOnInit(): void {
    }

    ngOnDestroy(): void {
    }

}