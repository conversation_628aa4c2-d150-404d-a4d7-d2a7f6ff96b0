import { Injectable } from '@angular/core';
import { Observable, fromEvent, timer } from 'rxjs';
import { debounceTime, map, switchMap, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ResizeObservableService {
  public windowResizeState: boolean = false;
  public observers = new Map<HTMLElement, Observable<ResizeObserverEntry[]>>();

  constructor() { }

  createResizeObserver(element: HTMLElement): Observable<ResizeObserverEntry[]> {
    return new Observable((subscriber) => {
      const resizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
        subscriber.next(entries);
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    });
  }

  elementResized(element: HTMLElement): Observable<ResizeObserverEntry[]> {
    if (!this.observers.has(element)) {
      const observer = this.createResizeObserver(element).pipe(
        debounceTime(200),
        map((entry) => entry),
      );
      this.observers.set(element, observer);
    }
    return this.observers.get(element);
  }

  windowResized(): Observable<number | Event> {
    return fromEvent(window, 'resize').pipe(
      tap(() => {
        this.windowResizeState = true;
      }),
      debounceTime(200),
      switchMap(() =>
        timer(3000).pipe(
          tap(() => {
            this.windowResizeState = false;
          })
        )
      )
    );
  }

}
