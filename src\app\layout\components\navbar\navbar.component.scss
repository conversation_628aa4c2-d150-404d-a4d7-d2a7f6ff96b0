@import '@core/scss/base/bootstrap-extended/include'; // Bootstrap includes
@import '@core/scss/base/components/include'; // Components includes

// To open dd on right
.dropdown-menu-right {
  right: 0 !important;
  left: auto !important;
}

// Cart Touchspin
.touchspin-cart {
  .touchspin-wrapper {
    width: 6.4rem;
  }

  &:focus-within {
    box-shadow: none !important;
  }
}

app-navbar-bookmark {
  display: flex;
}

// Apply style on window scroll for navbar static type
.navbar-static-style-on-scroll {
  background-color: #fff !important;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 4px 20px 0px !important;
}

// Dark Layout
.dark-layout {
  .navbar-container {
    .search-input {
      .search-list {
        li {
          &.auto-suggestion {
            &:hover {
              background-color: $theme-dark-body-bg;
            }
          }
        }
      }
    }
  }
}

.widget-description {
  height: 3rem;
  display: flex;
  align-items: flex-start;
  vertical-align: middle;
}

#widgetSettingModal {
  .tab-content {
    width: 100%;
  }

  // .nav-tabs {
  //     margin-bottom: 0px;
  //     .btn{
  //       border-radius: 4px;
  //     }
  //     .nav-link:after{
  //       display: none;
  //     }
  // }

  .cursor-not-allowed {

    .custom-control-label,
    .custom-control-input {
      &::before {
        pointer-events: none;
      }

      &::after {
        pointer-events: none;
      }
    }
  }
}

.customizer-toggle1 {
  background: $primary;
  color: $white !important;
  display: block;
  box-shadow: -3px 0px 8px rgba($black, 0.1);
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  position: absolute;
  top: 30%;
  width: 38px;
  height: 38px;
  left: -39px;
  text-align: center;
  line-height: 40px;
  cursor: pointer;
}

.manual .ficon{
  height: 1.5rem;
  width: 1.5rem;
  font-size: 1.5rem;
  color: #00cfe8;
}

li button.custom-style {
  display: flex;
  align-items: center;
  .user-nav{
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    float: left;
    margin-right: 0.8rem;
  }
  .user-name {
    display: inline-block;
    margin-bottom: 0.435rem;
    margin-left: 0.2rem;
  }
  .user-status {
    font-size: smaller;
  }
}

.widget-search{
  width: 16rem;
  // position: absolute;
  // right: 1%;
  // top: 25%;
  // z-index: 2000 ;
}

.btn-disabled {
  pointer-events: none;
  /* 禁用点击事件 */
  background-color: rgb(122, 122, 122);
  /* 设置背景颜色为红色 */
  color: white;
  /* 设置文字颜色为白色 */
  cursor: not-allowed;
  /* 更改鼠标指针样式 */
}