import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, of } from 'rxjs';
import { map, catchError, tap } from "rxjs/operators";
import { CoreMenuService } from '@core/components/core-menu/core-menu.service';
import { AuthenticationService } from 'app/auth/service';
import { ToastrUtilsService } from './toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class ChangeMenuService {
  public menu: any;
  public deaultPmUrl = 'http://telemetry.askeydms.com/kpi?device={Device.DeviceInfo.SerialNumber}';

  constructor(
    private http: HttpClient,
    private _coreMenuService: CoreMenuService,
    private _authenticationService: AuthenticationService,
    private toastr: ToastrUtilsService,
    private translateService: TranslateService
  ) { }

  /**
  * Get PM Server URL
  */
  getPMServerURL(): Observable<any> {
    return this.http.get('nbi/system/serverSetting/getAttributes')
      .pipe(map((resp: any) => {
        return resp['telemetry.server.url'] || this.deaultPmUrl;
      }),
        catchError(error => throwError(() => error))
      )
  }
  // format URL
  formatURL(url: string): string {
    let urlObject;
    try {
      urlObject = new URL(url);
    } catch (error) {
      // this.toastr.showErrorMessage(this.translateService.instant('DEVICES.ACTION.ERROR'), 'Telemetry Server URL data format in System Setting is incorrect.');
      console.log(error);
      urlObject = new URL(this.deaultPmUrl);
    }
    const acsOrigin = urlObject.origin;
    const kpiMetricDomain = `${acsOrigin}/kpi/metric/view`;
    return kpiMetricDomain;
  }
  /**
  * change Pm Menu URL
  */
  changePmMenuURL(url, flag = false): void {
    this.menu = this._coreMenuService.getCurrentMenu();
    this.menu.forEach(item => {
      if (item.id === 'analysis') {
        const child = item.children.find(child => child.id === 'analysis-pm');
        if (child) {
          child.url = this.formatURL(url);
        }
      }
    });
    if (flag) {
      this._coreMenuService.unregister('main');
      this._coreMenuService.register('main', this.menu);
      this._coreMenuService.setCurrentMenu('main');
    }
  }
  /**
  * set PM Server URL
  */
  setPMServerURL(url?): void {
    if (this._authenticationService.isAdmin) {
      if (url) {
        this.changePmMenuURL(url, true);
        return;
      } else {
        this.getPMServerURL().subscribe({
          next: (url: any) => {
            this.changePmMenuURL(url);
          },
          error: error => {
            if (error.error && error.status !== 401) {
              this.toastr.showErrorMessage(this.translateService.instant('DEVICES.ACTION.ERROR'), error.error);
            }
          }
        });
      }
    } else {
      this.changePmMenuURL(this.deaultPmUrl);
    }
  }
}
