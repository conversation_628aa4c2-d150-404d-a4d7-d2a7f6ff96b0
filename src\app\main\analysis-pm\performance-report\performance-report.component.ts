import { Component, Input, OnD<PERSON>roy, OnInit, ViewChild, ViewEncapsulation, ElementRef, AfterViewChecked, ChangeDetectorRef, } from '@angular/core';
import { ColumnMode, SelectionType, DatatableComponent } from '@almaobservatory/ngx-datatable';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { AnalysisPmService } from 'app/main/analysis-pm/analysis-pm.service';
import { DatatableCustomizeService } from 'app/main/commonService/datatable-customize.service';
import { NgbModal, NgbDateStruct, NgbTimeStruct, NgbDate, NgbCalendar } from '@ng-bootstrap/ng-bootstrap';
import cloneDeep from 'lodash/cloneDeep';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { Router } from '@angular/router';
import { FindDeviceService } from 'app/main/commonService/find-device.service'
import { BlockUI, Ng<PERSON>lockUI } from 'ng-block-ui';
import { EventsService } from 'app/main/events/events.service';
import { selectPMClassify } from '../pm-classify-data';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { ResizeObservableService } from 'app/main/commonService/resize-observable.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { AuthenticationService } from 'app/auth/service';
import { SanityTestService } from 'app/main/commonService/sanity-test.service';

@UntilDestroy()
@Component({
  selector: 'app-performance-report',
  templateUrl: './performance-report.component.html',
  styleUrls: ['./performance-report.component.scss']
})
export class PerformanceReportComponent extends Unsubscribe implements OnInit {
  @ViewChild(DatatableComponent) searchResultListTableRowDetails: DatatableComponent;
  @ViewChild(DatatableComponent) tableRowDetails: DatatableComponent;
  @Input() accessor: any;
  @BlockUI('searchResultBlock') searchResultBlock: NgBlockUI;
  @BlockUI('chartBlock') chartBlock: NgBlockUI;
  public personalTheme;
  public loading = false;
  public today = new Date();
  public selectedDeviceRow: string
  public contentHeader: object
  public selectedOption = 10;
  public ColumnMode = ColumnMode;
  public SelectionType = SelectionType;
  public selected = [];
  public selectedDevice = [];
  public selectGroup = [];
  public temp = [];
  public chartMode: any
  public resultMode: any
  public selectPMParamName = selectPMClassify
  public selectDuration = [
    { name: 'One Day', value: 'day' },
    { name: 'One Week', value: 'week' },
    { name: 'One Month', value: 'month' },
  ]
  public selectCondition = [
    { name: '=', value: 'EQ' },
    { name: '!=', value: 'NE' },
    { name: '>', value: 'GT' },
    { name: '<', value: 'LT' },
    { name: '<=', value: 'LTE' },
    { name: '>=', value: 'GTE' },
  ];
  public performanceReportList = []
  public selectedTarget = 'all';
  public noPermission = false
  public noAlarmPermission = false

  public tableWidth = 0;
  public gridScrollHeight = 0;
  public scrollSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  public tableOptionStatus = false;
  public tableOption = [
    { name: 'Name', prop: 'name', translate: 'PM.NAME', width: 250, flexGrow: 250, columnStatus: true },
    { name: 'SearchResult', prop: 'deviceCount', translate: 'PM.DEVICE_RESULT', width: 65, flexGrow: 65, columnStatus: true },
    { name: 'Target', prop: 'serialNumber', translate: 'PM.TARGET', width: 150, flexGrow: 150, columnStatus: true },
    { name: 'Conditions', prop: 'searchRule', translate: 'PM.CONDITIONS', width: 250, flexGrow: 250, columnStatus: true },
    { name: 'Description', prop: 'description', translate: 'PM.DESCRIPTION', width: 150, flexGrow: 150, columnStatus: true },
    { name: 'Duration', prop: 'duration', translate: 'PM.DURATION', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'From', prop: 'beginTime', translate: 'PM.FROM', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'To', prop: 'endTime', translate: 'PM.TO', width: 100, flexGrow: 100, columnStatus: true },
    { name: 'Created By', prop: 'creator', translate: 'PM.CREATEDBY', width: 100, flexGrow: 100, columnStatus: false },
    { name: 'Update Time', prop: 'updated', translate: 'PM.UPDATETIME', width: 150, flexGrow: 150, columnStatus: false },
    { name: 'Tracking', prop: 'isAllowed', translate: 'PM.TRACKING', width: 80, flexGrow: 80, columnStatus: true },
  ]

  public PERFORMANCEREPORT = this.translateService.instant('PM.PERFORMANCEREPORT');
  public SUCC = this.translateService.instant('CONFIRM.SUCC');
  public SUCCESS = this.translateService.instant('CONFIRM.ADDSUCC');
  public UPDATEDEV = this.translateService.instant('CONFIRM.UPDATEDEV');
  public COMMONNAME = this.translateService.instant('COMMON.NAME');
  public WASADD = this.translateService.instant('CONFIRM.WASADD');
  constructor(
    private _analysisPmService: AnalysisPmService,
    private translateService: TranslateService,
    private modalService: NgbModal,
    private _datatableCustomizeService: DatatableCustomizeService,
    private cdr: ChangeDetectorRef,
    private _toastrUtilsService: ToastrUtilsService,
    private calendar: NgbCalendar,
    private router: Router,
    private _findDeviceService: FindDeviceService,
    private _eventsService: EventsService,
    private route: ActivatedRoute,
    private _resizeObservableService: ResizeObservableService,
    private _groupListService: GroupListService,
    private _authService: AuthenticationService,
    private st: SanityTestService,
  ) {
    super()
    this.customSubscribe(route.data, res => {
      // console.log(res)
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
    this._groupListService.getGroupNameList().pipe(untilDestroyed(this)).subscribe(res => {
      // console.log(res)
      res.forEach(item => {
        this.selectGroup.push({
          'name': item.name,
          'id': item.id,
          'number': item.number
        })
      })
      this.selectGroup = this.selectGroup.sort((a, b) => {
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
        return 1
      })
      // console.log(this.selectGroup)
    })
  }

  allowTracking(row) {
    const status = row.isAllowed ? 'Disable' : 'Enable';
    this._analysisPmService.allowTracking(row.id, row.isAllowed).then(res => {
      row.isAllowed = !row.isAllowed
      this._toastrUtilsService.showSuccessMessage(this.PERFORMANCEREPORT, status + this.SUCC);
    }).catch(error => {
      row.isAllowed = false
      this._toastrUtilsService.showErrorMessage(this.PERFORMANCEREPORT, error.error);
    }).finally(() => {
    })
  }

  changeColumn(event): void {
    if (event.cloumns) {
      this.tableOption = [...event.cloumns];
    }
  }

  onResize({ column, newValue }) {
    this.tableOption = this._datatableCustomizeService.resize(column, newValue, this.tableOption, this.accessor, this.tableRowDetails);
  }

  emittedEvents(event: string): void {
    if (event === 'reload') {
      this.getMetricList(true)
    }
  }

  reloadMetricList(message: string) {
    // console.log(message)
    if (message) {
      this.getMetricList()
    }
  }
  //單選或多選的refresh
  refreshRows(rows) {
    // console.log(rows);
    //會有單選跟多選-->統一轉成陣列處理
    const rowArray = Array.isArray(rows) ? rows : [rows];

    rowArray.forEach(row => {
      // console.log(row);
      row.loading = true;
      this._analysisPmService.getMetricList().then(res => {
        const targetMetric = res.find(metric => metric.id === row.id);
        if (targetMetric) {
          //非全選的refresh只修改deviceCount跟updated
          row.deviceCount = targetMetric.lastQueryRes ? targetMetric.lastQueryRes.length : 0;
          row.updated = targetMetric.updated;
        } else {
          console.error('Refresh Fail:', row.name);
        }
      }).catch((err) => {
        this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
      }).finally(() => {
        row.loading = false;
        this.selected = []
      });
    });
  }



  public filterMap = {
    name: "",
  }
  private tempData = [];
  filterInput() {
    // Reset ng-select on search
    // this.selectedSeverity = [];
    // Filter Our Data
    const temp = this.tempData.filter(row => {
      const isNameMatch = row.name.toLowerCase().indexOf(this.filterMap.name.toLowerCase()) > -1 || !this.filterMap.name
      return isNameMatch
    });

    // Update The Rows
    this.performanceReportList = temp;
    // Whenever The Filter Changes, Always Go Back To The First Page
    // this.table.offset = 0;
  }

  initQueryParams(list) {
    for (let key in this.filterMap) {
      if (!list.includes(key)) {
        this.filterMap[key] = '';
      }
    }
  }

  /**
* For ref only, log selected values
*
* @param selected
*/
  onSelect({ selected }) {
    if (selected) {
      this.selected = selected;
    }
    this.selected = selected;
    // console.log('this.selected', this.selected)
  }
  onSelectDevice({ selected }) {
    if (selected) {
      this.selectedDevice = selected;
    }
    this.selectedDevice = selected;
    // console.log('this.selectedDevice', this.selectedDevice)
  }

  /**
 * Row Details Toggle
 * @param row
 */
  rowDetailsToggleExpand(row) {
    this.tableRowDetails.rowDetail.toggleExpandRow(row);
  }

  public conditionMapping = {
    'EQ': '=',
    'NE': '!=',
    'GT': '>',
    'LT': '<',
    'LTE': '<=',
    'GTE': '>=',
  }

  mappingName(value, mappingArr) {
    const item = mappingArr.find(item => item.value === value);
    return item ? item.name : null;
  }
  mappingValue(name, mappingArr) {
    const item = mappingArr.find(item => item.name === name);
    return item ? item.value : null;
  }
  mappingGroup(value, mappingArr) {
    const item = mappingArr.find(item => item.name === value);
    return item ? item.group : null;
  }

  public FAIL = this.translateService.instant('PM.FAIL');
  getMetricList(reload?) {
    this.loading = true
    this._analysisPmService.getMetricList().then(res => {
      // console.log(res)
      res.forEach(item => {
        // console.log(item)
        let searchRule = []
        item.metricParameters.forEach(item2 => {
          searchRule.push((this.mappingGroup(item2.classifyName, this.selectPMParamName) == 'Manual' ? item2.name : item2.classifyName) + this.conditionMapping[item2.condition] + item2.value)
        })
        item.searchRule = searchRule.join(` ${item.operator.toUpperCase()} `)

        item.deviceCount = item.lastQueryRes ? item.lastQueryRes.length : 0

        item.description = item.description.replace(/&lt;/g, "<").replace(/&gt;/g, ">")

        // 計算duration
        let timeDiff = new Date(item.endTime).getTime() - new Date(item.beginTime).getTime(); // 获取时间间隔
        let oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
        let oneWeek = 7 * oneDay; // 一周的毫秒数
        if (timeDiff <= oneDay) {
          // console.log("One Day");
          item.duration = 'Day'
        } else if (timeDiff <= oneWeek) {
          // console.log("One Week");
          item.duration = 'Week'
        } else {
          // console.log("One Month");
          item.duration = 'Month'
        }

        item.groupName = this.selectGroup.find(a => {
          return a.id === item.groupId
        }) ? this.selectGroup.find(a => {
          return a.id === item.groupId
        }).name : ''

        item.groupMember = this.selectGroup.find(a => {
          return a.id === item.groupId
        }) ? this.selectGroup.find(a => {
          return a.id === item.groupId
        }).number : 0

      })

      // console.log(res)
      this.performanceReportList = res
      this.tempData = this.performanceReportList;
      if (reload) {
        this.filterInput()
      } else {
        this.initQueryParams([])
      }
      // console.log('performanceReportList', this.performanceReportList)
    }).catch((err) => {
      this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
    }).finally(() => {
      this.tableRowDetails.selected = []
      this.selected = []
      this.loading = false
    });
  }

  public conditionList = {
    name: '',
    serialNumber: '',
    metricParameters: [],
    operator: "And",
    // devGroup: [],
    groupId: '',
    beginTime: '',
    endTime: '',
    description: '',
    duration: {}
  }

  public paramArr = []
  public metricName;
  public serialNumber;
  public group;
  public classify;
  public paramPath;
  public condition;
  public paramValue;
  public beginTime;
  public endTime;
  public duration = { name: 'One Day', value: 'day' };
  public operator = 'And';
  public measureType = 'avg'

  initValue() {
    this.conditionList = {
      name: '',
      serialNumber: '',
      metricParameters: [],
      operator: "And",
      // devGroup: [],
      groupId: '',
      beginTime: '',
      endTime: '',
      description: '',
      duration: {}
    }
    this.serialNumber = ''
    this.classify = null
    this.paramPath = ''
    this.condition = null
    this.paramValue = ''
    this.beginTime = this.beginTime
    this.endTime = this.endTime
  }

  checkLastCharacter(inputString) {
    // 使用字符串的endsWith方法检查最后一个字符是否是.
    if (inputString.endsWith('.')) {
      return false;
    } else {
      return true;
    }
  }

  isNumber(value) {
    if (value && /^\d+$/.test(value)) {
      return true;
    }
    return false;
  }

  @ViewChild('paramPathInput') paramPathInput: ElementRef;
  public verifyParamPath = false
  addNewCondition() {
    if (this.conditionList['metricParameters'].length >= 2) {
      this._toastrUtilsService.showWarningMessage('Limit to a maximum of 2 conditions.', '')
    }
    else if (this.checkLastCharacter(this.paramPath) == false) {
      this.verifyParamPath = true
      if (this.verifyParamPath) {
        this.paramPathInput.nativeElement.focus();
      }
      this._toastrUtilsService.showErrorMessage('Please enter the correct parameter path.', '')
    }
    else {
      // console.log(this.duration)
      this.conditionList['metricParameters'].push(
        {
          id: this.conditionList['metricParameters'].length,
          condition: this.condition.value,
          classifyName: this.classify.name,
          name: this.classify.group == 'Manual' ? this.paramPath : this.classify.value,
          type: 'int',
          value: this.paramValue,
          isEditValue: false
        }
      )
      if (this.serialNumber) {
        this.serialNumber = this.serialNumber.replace(/\s/g, '')
        if (this.serialNumber.split(',').length > 0) {
          let serialArr = this.serialNumber.split(',')
          this.conditionList.serialNumber = serialArr
        } else {
          this.conditionList.serialNumber = this.serialNumber
        }
      }

      this.conditionList.beginTime = this.returnDuration(this.duration.value)
      this.conditionList.endTime = new Date(this.today.setHours(23, 59, 59)).toISOString()
      this.conditionList.duration = this.duration

    }
    // console.log(this.conditionList)
  }


  returnDuration(value) {
    let beginTime
    if (value == 'month') {
      var oneMonthAgo = new Date(this.today);
      oneMonthAgo.setMonth(this.today.getMonth() - 1);
      // 如果当前日期的天数大于一个月前的日期的天数，
      // 则需要调整日期，确保在同一月份内
      if (this.today.getDate() < oneMonthAgo.getDate()) {
        oneMonthAgo.setDate(this.today.getDate());
      }
      // console.log(oneMonthAgo);
      oneMonthAgo.setHours(0, 0, 0, 0)
      beginTime = oneMonthAgo.toISOString()
    }
    else if (value == 'week') {
      // 计算七天前的日期
      let sevenDaysAgo = new Date(this.today);
      sevenDaysAgo.setDate(this.today.getDate() - 6);
      sevenDaysAgo.setHours(0, 0, 0, 0)
      // console.log(sevenDaysAgo);
      beginTime = sevenDaysAgo.toISOString()
    }
    else {
      beginTime = new Date(this.today.setHours(0, 0, 0, 0)).toISOString()
    }

    return beginTime
  }

  druationChange(event, conditionList) {
    // console.log(event.value)
    // console.log(conditionList)
    conditionList.beginTime = this.returnDuration(event.value)
    conditionList.duration = event
  }

  public searchRuleBeginTime = null
  public searchRuleEndTime = null
  public searchDuration = { name: 'One Day', value: 'day' };

  public showManualParamPath = false
  public showEditManualParamPath = false
  public showSearchRuleManualParamPath = false
  public showSearchRuleEditManualParamPath = false
  classifyChange(event, mode) {
    // console.log(event)
    switch (mode) {
      case 'manualParamPath':
        if (event.group == 'Manual') {
          this.showManualParamPath = true
          this.paramPath = event.value
        } else {
          this.showManualParamPath = false
          this.paramPath = event.value
        }
        break;
      case 'editManualParamPath':
        if (event.group == 'Manual') {
          this.showEditManualParamPath = true
          this.editParamPath = event.value
        } else {
          this.showEditManualParamPath = false
          this.editParamPath = event.value
        }
        break;
    }
  }


  delCondition(index, conditionList) {
    conditionList.metricParameters.splice(index, 1)
    if (conditionList.metricParameters.length == 0) {
      this.initValue()
    }
  }

  changeOperator(operator, conditionList) {
    conditionList.operator = operator
  }

  public searchResultConditionList = []
  public searchResultList: any
  public searchSpinner = false
  modalOpenSearchResult(modal, mode, row, conditionList) {
    this.searchResultBlock.start();
    // console.log(conditionList)
    this.resultMode = mode
    switch (mode) {
      case 'new':
        this.searchSpinner = true
        this.searchResultConditionList = []
        this.searchRuleBeginTime = null
        this.searchRuleEndTime = null
        this.searchResultConditionList = conditionList
        // console.log('new', this.searchResultConditionList)
        this.searchRuleBeginTime = new Date(conditionList['beginTime'])
        this.searchRuleEndTime = new Date(conditionList['endTime'])
        this.searchDuration = conditionList['duration']
        this.searchMetric(this.searchResultConditionList, modal)
        break;
      case 'report':
        this.loading = true
        // console.log(row)
        this.searchResultConditionList = []
        this.searchRuleBeginTime = null
        this.searchRuleEndTime = null

        this.searchResultConditionList = row
        // console.log('report', this.searchResultConditionList)
        this.searchRuleBeginTime = new Date(row['beginTime'])
        this.searchRuleEndTime = new Date(row['endTime'])
        // console.log(this.searchRuleBeginTime)
        // console.log(this.searchRuleEndTime)

        // 判断时间跨度是一天、七天还是一个月
        let timeDiff = this.searchRuleEndTime - this.searchRuleBeginTime; // 获取时间间隔
        let oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
        let oneWeek = 7 * oneDay; // 一周的毫秒数
        let oneMonth = 30 * oneDay; // 一个月的毫秒数
        if (timeDiff <= oneDay) {
          // console.log("One Day");
          this.searchDuration = { name: 'One Day', value: 'day' }
        } else if (timeDiff <= oneWeek) {
          // console.log("One Week");
          this.searchDuration = { name: 'One Week', value: 'week' }
        } else {
          // console.log("One Month");
          this.searchDuration = { name: 'One Month', value: 'month' }
        }
        // console.log(row)
        // console.log(row.id)
        this.viewMerticRule(row.id, modal)
        break;
    }
  }

  public res;
  public merticRuleResult = []
  columns: any[] = [];
  public merticRuleResultParamArr = []
  viewMerticRule(id, modal) {
    this._analysisPmService.viewMetricRule(id).then(res => {
      // console.log(res)
      this.merticRuleResult = []
      this.merticRuleResultParamArr = []
      this.res = res
      this.searchMetricRes = JSON.parse(this.res)
      let viewMerticRuleData = JSON.parse(this.res).data;
      // console.log(viewMerticRuleData)
      const groupedData = [];
      viewMerticRuleData.forEach(entry => {
        const existingGroup = groupedData.find(group => group._id === entry._id);
        if (existingGroup) {
          entry.records.forEach(item => {
            for (let key in item) {
              if (key != 'beginTime' && key != 'endTime' && key != 'serialNumber') {
                existingGroup.records[0][key] = item[key];
              }
            }
          })
        } else {
          groupedData.push({ _id: entry._id, records: entry.records });
        }
      });
      // console.log(groupedData)

      groupedData.forEach(item => {
        for (let key = 0; key < item.records.length; key++) {
          this.merticRuleResult.push(item.records[key])
        }
      })
      // console.log(this.merticRuleResult)

      // default selectedDevice
      if (this.merticRuleResult.length >= 20) {
        this.selectedDevice = this.merticRuleResult.slice(0, 20);
      } else {
        this.selectedDevice = this.merticRuleResult.slice(0, this.merticRuleResult.length);
      }

      if (this.searchResultConditionList['metricParameters'].length > 0) {
        this.searchResultConditionList['metricParameters'].forEach(item => {
          // console.log(item)
          this.merticRuleResultParamArr.push(this.mappingGroup(item.classifyName, this.selectPMParamName) == 'Manual' ? item.name : item.classifyName)
        })
        // console.log(this.merticRuleResultParamArr)
      }
      this.modalService.open(modal, {
        size: 'xl',
        scrollable: true,
      });

    }).catch((err) => {
      this._toastrUtilsService.showErrorMessage(this.FAIL, err.message || err);
    }).finally(() => {
      this.searchResultBlock.stop();
      this.loading = false
    });
  }

  public metricRuleName = ''
  public metricRuleDescription = ''
  public editMetricRuleNameMode
  openSaveMetricModal(modalNme, mode, conditionList) {
    // console.log(mode)
    // console.log(conditionList)
    this.editMetricRuleNameMode = mode
    if (mode == 'update') {
      this.metricRuleName = conditionList.name
      this.metricRuleDescription = conditionList.description
    } else {
      this.metricRuleName = ''
      this.metricRuleDescription = ''
    }
    this.performanceReportNameCheck(this.metricRuleName)
    this.modalService.open(modalNme)
  }

  saveMetric(conditionList, lastQueryRes, metricName, metricDescription, mode, modal) {
    // console.log('saveMetric conditionList', conditionList)
    // console.log('saveMetric lastQueryRes', lastQueryRes)
    // console.log('saveMetric metricName', metricName)
    // console.log('saveMetric metricDescription', metricDescription)
    let param = {
      name: metricName,
      metricParameters: conditionList.metricParameters,
      operator: conditionList.operator,
      serialNumber: conditionList.serialNumber,
      groupId: conditionList.groupId,
      beginTime: conditionList.beginTime,
      endTime: conditionList.endTime,
      description: metricDescription,
      size: '1',
      lastQueryRes: lastQueryRes
    }
    switch (mode) {
      case 'save':
        this._analysisPmService.addMetric(param).then(res => {
          // console.log(res)
          this.getMetricList()
          this._toastrUtilsService.showSuccessMessage(this.SUCCESS, 'New mertic rule ('+ metricName + this.WASADD)
          this.modalService.dismissAll()
          this.initValue()
        })
        break;
      case 'update':
        this._analysisPmService.updateMetric(conditionList.id, param).then(res => {
          // console.log(res)
          modal.close()
          this.searchResultConditionList['name'] = metricName
          this.searchResultConditionList['description'] = metricDescription
        })
        break;
    }
  }

  updateMetric(conditionList, lastQueryRes) {
    // console.log('updateMetric', conditionList)
    // console.log('updateMetric', lastQueryRes)
    let param = {
      name: conditionList.name,
      metricParameters: conditionList.metricParameters,
      operator: conditionList.operator,
      serialNumber: conditionList.serialNumber,
      groupId: conditionList.groupId,
      beginTime: conditionList.beginTime,
      endTime: conditionList.endTime,
      description: conditionList.description,
      size: '1',
      lastQueryRes: lastQueryRes
    }
    // console.log(param)

    this._analysisPmService.updateMetric(conditionList.id, param).then(res => {
      // console.log(res)
      this.getMetricList()
      this._toastrUtilsService.showSuccessMessage(this.UPDATEDEV, `${this.COMMONNAME}: ${res}`)
      this.modalService.dismissAll()
      this.initValue()
    }).catch((err) => {
      this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
    })
  }

  public editClassify
  public editParamPath
  public editCondition
  public editParamVlaue

  editParam(index, conditionList) {
    // console.log(conditionList)
    if (this.mappingGroup(conditionList.metricParameters[index].classifyName
      , this.selectPMParamName) == 'Manual') {
      this.showSearchRuleEditManualParamPath = true
      this.showEditManualParamPath = true
    } else {
      this.showSearchRuleEditManualParamPath = false
      this.showEditManualParamPath = false
    }
    this.editClassify = conditionList.metricParameters[index].classifyName
    this.editParamPath = conditionList.metricParameters[index].name
    this.editCondition = conditionList.metricParameters[index].condition
    this.editParamVlaue = conditionList.metricParameters[index].value
    conditionList.metricParameters[index].isEditValue = true
  }


  saveEditParam(index, conditionList, classifyName, condition, paramPath, paramValue) {
    // console.log('classifyName', classifyName)
    // console.log('condition', condition)
    // console.log('paramValue', paramValue)

    // classify name mapping
    if (classifyName.name) {
      conditionList.metricParameters[index].classifyName = classifyName.name
      conditionList.metricParameters[index].name = paramPath ? paramPath : classifyName.value
    } else {
      let param = this.selectPMParamName.find(item => {
        return item.name == classifyName
      })
      // console.log(param)
      conditionList.metricParameters[index].classifyName = param.name
      conditionList.metricParameters[index].name = paramPath ? paramPath : param.value
      // conditionList.metricParameters[index].paramName = paramPath
    }

    // condition mapping
    if (condition.name) {
      conditionList.metricParameters[index].condition = condition.value
    } else {
      let con = this.selectCondition.find(item => {
        return item.name == condition
      })
      // console.log(con)
      conditionList.metricParameters[index].condition = con.value
    }

    conditionList.metricParameters[index].value = paramValue
    conditionList.metricParameters[index].isEditValue = false
    // console.log(conditionList)

    this.editClassify = conditionList.metricParameters[index].classifyName
    this.editCondition = conditionList.metricParameters[index].condition
    this.editParamVlaue = conditionList.metricParameters[index].value
  }


  cancelEditParam(index, conditionList) {
    // console.log(conditionList)
    conditionList.metricParameters[index].isEditValue = false
  }

  filterSelectChange(event, conditionList) {
    conditionList.groupId = ''
    if (event.target.value != '') {
      conditionList.groupId = event.target.value
    }
    // console.log(conditionList.groupId)
  }
  targetSelectChange(event, conditionList) {
    conditionList.groupId = ''
    conditionList.serialNumber = ''
    this.serialNumber = ''
  }




  public searchMetricRes: any
  searchMetric(conditionList, modal) {
    this.searchResultBlock.start();
    // console.log(conditionList)
    // console.log(this.searchDuration)
    conditionList.beginTime = this.returnDuration(this.searchDuration.value)
    conditionList.endTime = new Date(this.today.setHours(23, 59, 59)).toISOString()
    if (this.serialNumber) {
      this.serialNumber = this.serialNumber.replace(/\s/g, '')
      if (this.serialNumber.split(',').length > 0) {
        let serialArr = this.serialNumber.split(',')
        this.conditionList.serialNumber = serialArr
      } else {
        this.conditionList.serialNumber = this.serialNumber
      }
    }
    let param = {
      serialNumber: conditionList.serialNumber,
      metricParameters: conditionList.metricParameters,
      operator: conditionList.operator,
      groupId: conditionList.groupId,
      beginTime: conditionList.beginTime,
      endTime: conditionList.endTime,
      size: "1"
    }
    this._analysisPmService.searchMetric(param).then(res => {
      // console.log(res)
      this.searchMetricRes = res
      this.searchMetricRes = JSON.parse(this.searchMetricRes)

      this.merticRuleResult = []
      this.merticRuleResultParamArr = []
      let searchMetricData

      searchMetricData = this.searchMetricRes.data;
      // console.log(searchMetricData)
      const groupedData = [];
      searchMetricData.forEach(entry => {
        const existingGroup = groupedData.find(group => group._id === entry._id);
        if (existingGroup) {
          entry.records.forEach(item => {
            for (let key in item) {
              if (key != 'beginTime' && key != 'endTime' && key != 'serialNumber') {
                existingGroup.records[0][key] = item[key];
              }
            }
          })
        } else {
          groupedData.push({ _id: entry._id, records: entry.records });
        }
      });
      // console.log(groupedData)
      groupedData.forEach(item => {
        for (let key = 0; key < item.records.length; key++) {
          this.merticRuleResult.push(item.records[key])
        }
      })
      // console.log(this.merticRuleResult)

      if (this.searchResultConditionList['metricParameters'].length > 0) {
        this.searchResultConditionList['metricParameters'].forEach(item => {
          this.merticRuleResultParamArr.push(this.mappingGroup(item.classifyName, this.selectPMParamName) == 'Manual' ? item.name : item.classifyName)
        })
        // console.log(this.merticRuleResultParamArr)
      }
      if (modal) {
        this.modalService.open(modal, {
          size: 'xl',
          scrollable: true,
        });
      }
    }).catch((err) => {
      this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
    }).finally(() => {
      this.clearSelected()
      this.searchResultBlock.stop();
      this.searchSpinner = false
    });
  }

  clearSelected() {
    this.searchResultListTableRowDetails.selected = []
    this.selectedDevice = []
  }


  modalOpenchart(modalChart, mode, row) {
    // console.log(row)
    this.chartMode = mode
    this.modalService.open(modalChart, {
      size: 'xl',
      scrollable: true,
    });
  }

  public viewChartSingleRes: any
  public viewChartMultiRes: any
  public viewChartAllRes: any
  public series = []
  public resultData = []
  //device view chart
  chartFormatData(res, markline, modal?) {
    // console.log(markline)
    this.series = []
    this.resultData = []
    const jsonRes = JSON.parse(res);
    // console.log('jsonRes', jsonRes)
    let dataArr = []
    jsonRes.data.forEach(item => {
      for (let key = 0; key < item.records.length; key++) {
        dataArr.push(item.records[key])
      }
    })
    // console.log(dataArr)
    const groupedData = {};
    // 遍历输入数据
    dataArr.forEach(item => {
      const serialNumber = item.serialNumber;
      // 如果该serialNumber尚未在分组数据中创建数组，就创建一个空数组
      if (!groupedData[serialNumber]) {
        groupedData[serialNumber] = [];
      }
      // 将当前对象添加到相应的数组中
      groupedData[serialNumber].push(item);
    });
    const groupedArray = Object.values(groupedData);
    // console.log('groupedArray', groupedArray);

    let filterKeyArr = []
    groupedArray.forEach(arr => {
      let itemArr = []
      let arrItem
      arrItem = arr
      // console.log(arrItem)
      for (let i = 0; i < arrItem.length; i++) {
        arrItem.sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime());
        const item = arrItem[i];
        let extractAllKeysAndValues = this.extractAllKeysAndValues(item)
        // console.log(extractAllKeysAndValues)
        extractAllKeysAndValues.forEach(item => {
          if (item.key != 'serialNumber') {
            item.value = [arrItem[i].endTime, item.value]
          }
        })
        itemArr.push(extractAllKeysAndValues)
      }
      filterKeyArr.push(itemArr)
      // console.log('filter keyArr', this.filterKeyArr)
    })

    filterKeyArr.forEach(keyArr => {
      let result: { name: string, value: [string, string][], serial: string }[] = []
      // console.log(keyArr)
      for (let i = 0; i < keyArr.length; i++) {
        // console.log(keyArr[i])
        let serial = keyArr[i].find(item => {
          return item.key == 'serialNumber'
        }).value
        for (const item of keyArr[i]) {
          const existingItem = result.find((r) => r.name === item.key);
          if (existingItem) {
            existingItem.value.push(item.value);
          } else {
            result.push({ name: item.key, value: [item.value], serial: serial });
          }
        }
      }
      let filterkey = result.filter(item => {
        return item.name !== 'beginTime' && item.name !== 'endTime' && item.name !== 'serialNumber'
      })
      this.resultData.push(filterkey)
    })
    // console.log('resultData', this.resultData)

    const result = [];
    for (const subArray of this.resultData) {
      // 在子数组中遍历对象
      for (const item of subArray) {
        const name = item.name;
        result[name] = result[name] || [];
        result[name].push(item);
      }
    }
    // console.log(result);

    for (let i in result) {
      let dataArr = []
      // console.log(i)
      // console.log(result[i])
      for (let k = 0; k < result[i].length; k++) {
        dataArr.push({
          xname: i,
          name: result[i][k].serial,
          data: result[i][k].value,
          type: 'line',
          smooth: true,
          sampling: 'lttb',
          markLine: {
            data: []
          }
        })

      }
      // console.log('dataArr', dataArr)
      this.series.push(dataArr)
    }
    if (this.series.length > 0) {
      for (let x = 0; x < this.series.length; x++) {
        for (let y = 0; y < markline.length; y++) {
          if (this.series[x][0].xname == markline[y].name && markline[y].value > 0) {
            this.series[x][0].markLine.data.push({
              symbol: "none",
              yAxis: parseInt(markline[y].value),
              lineStyle: {
                color: '#EA5455',
                type: 'dashed', // 标线类型，可选为 'solid', 'dashed', 'dotted'
              },
              label: {
                position: 'end',
                color: '#EA5455', // 文字颜色
                fontSize: 14, // 文字大小
                fontWeight: 'bold', // 文字粗细
                fontFamily: 'Arial, sans-serif', // 字体
              },
            })
          }
        }
      }
    }
    // console.log('series', this.series)
    if (modal) {
      this.modalService.open(modal, {
        size: 'xl',
        scrollable: true,
      });
      this.searchResultBlock.stop();
    } else {
      this.chartBlock.stop()
    }
  }

  // chart data key/value format
  extractAllKeysAndValues(obj: any, currentKey: string = ''): { key: string, value: any }[] {
    const keyValues: { key: string, value: any }[] = [];
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = currentKey ? `${currentKey}.${key}` : key;
        const value = obj[key];
        if (typeof value === 'object' && !Array.isArray(value)) {
          keyValues.push(...this.extractAllKeysAndValues(value, newKey));
        } else if (Array.isArray(value)) {
          for (let i = 0; i < value.length; i++) {
            if (typeof value[i] === 'object') {
              keyValues.push(...this.extractAllKeysAndValues(value[i], `${newKey}[${i}]`));
            }
          }
        } else {
          keyValues.push({ key: newKey, value });
        }
      }
    }
    return keyValues;
  }


  formatDownloadData(res: string): any[] {
    const jsonRes = JSON.parse(res);
    const dataArr: any[] = [];

    jsonRes.data.forEach(item => {
      for (const record of item.records) {
        dataArr.push(record);
      }
    });

    const groupedData: Record<string, any[]> = {};
    dataArr.forEach(item => {
      const serialNumber = item.serialNumber;
      if (!groupedData[serialNumber]) {
        groupedData[serialNumber] = [];
      }
      groupedData[serialNumber].push(item);
    });

    const groupedArray = Object.values(groupedData);

    const resultData: any[][] = [];

    groupedArray.forEach(arr => {
      arr.sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime());

      const itemArr: any[][] = [];
      for (const item of arr) {
        const extracted = this.extractAllKeysAndValues(item);
        extracted.forEach(kv => {
          if (kv.key !== 'serialNumber') {
            kv.value = [item.endTime, kv.value];
          }
        });
        itemArr.push(extracted);
      }

      const result: { name: string; value: [string, string][], serial: string }[] = [];

      for (const arrItem of itemArr) {
        const serial = arrItem.find(x => x.key === 'serialNumber')?.value;
        for (const kv of arrItem) {
          if (kv.key === 'beginTime' || kv.key === 'endTime' || kv.key === 'serialNumber') continue;

          const existing = result.find(r => r.name === kv.key);
          if (existing) {
            existing.value.push(kv.value);
          } else {
            result.push({ name: kv.key, value: [kv.value], serial });
          }
        }
      }

      resultData.push(result);
    });

    const mergedResult: Record<string, any[]> = {};
    for (const arr of resultData) {
      for (const item of arr) {
        if (!mergedResult[item.name]) mergedResult[item.name] = [];
        mergedResult[item.name].push(item);
      }
    }

    const finalSeries = [];
    for (const name in mergedResult) {
      const chartArr = mergedResult[name].map(entry => ({
        xname: name,
        name: entry.serial,
        data: entry.value,
      }));
      finalSeries.push(chartArr);
    }

    return finalSeries;
  }

  public searchResultRow
  searchResultViewChart(modalChart, mode, row, conditionList) {
    this.searchResultBlock.start();
    // console.log(row)
    // console.log(conditionList)
    // console.log(mode)
    this.measureType = 'avg'
    this.chartMode = mode
    this.searchResultRow = row
    let markline = []
    switch (mode) {
      case 'single':
        let newSingleSearchMetricParameters = cloneDeep(conditionList.metricParameters)
        newSingleSearchMetricParameters.forEach(item => {
          item.condition = 'GTE'
          item.value = "0"
        })
        let deviceArr = []
        deviceArr.push(row.serialNumber)
        let singleDeviceParam = {
          serialNumber: deviceArr,
          metricParameters: newSingleSearchMetricParameters,
          operator: conditionList.operator,
          beginTime: conditionList.beginTime,
          endTime: conditionList.endTime,
        }
        // markline value
        if (conditionList.metricParameters) {
          conditionList.metricParameters.forEach(item => {
            markline.push({ name: item.name, value: item.value })
          })
        }
        // console.log(markline)

        this._analysisPmService.searchMetricByMeasureType(singleDeviceParam, this.measureType).then(res => {
          // console.log(res)
          this.viewChartSingleRes = res
          this.chartFormatData(this.viewChartSingleRes, markline, modalChart)
        }).catch((err) => {
          this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
        }).finally(() => {
          this.getOriginalData(singleDeviceParam).then(seriesForDownload => {
            // console.log('seriesForDownload', seriesForDownload);
            this.originalData = seriesForDownload
          });
        })
        break;
      case 'multi':
        if (row.length > 20) {
          this._toastrUtilsService.showWarningMessage('The maximum allowed selection is 20 devices.', '')
        } else {
          let newMultiSearchMetricParameters = cloneDeep(conditionList.metricParameters)
          newMultiSearchMetricParameters.forEach(item => {
            item.condition = 'GTE'
            item.value = "0"
          })
          let deviceArr = []
          row.forEach(item => {
            deviceArr.push(item.serialNumber)
          })
          let multiDeviceParam = {
            serialNumber: deviceArr,
            metricParameters: newMultiSearchMetricParameters,
            operator: conditionList.operator,
            beginTime: conditionList.beginTime,
            endTime: conditionList.endTime,
          }
          // console.log(multiDeviceParam)
          // markline value
          if (conditionList.metricParameters) {
            conditionList.metricParameters.forEach(item => {
              markline.push({ name: item.name, value: item.value })
            })
          }
          this._analysisPmService.searchMetricByMeasureType(multiDeviceParam, this.measureType).then(res => {
            // console.log(res)
            this.viewChartMultiRes = res
            this.chartFormatData(this.viewChartMultiRes, markline, modalChart)
          }).catch((err) => {
            this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
          }).finally(() => {
            this.getOriginalData(multiDeviceParam).then(seriesForDownload => {
              // console.log('seriesForDownload', seriesForDownload);
              this.originalData = seriesForDownload
            });
          })
        }
        break;
      case 'all':
        let newAllSearchMetricParameters = cloneDeep(conditionList.metricParameters)
        newAllSearchMetricParameters.forEach(item => {
          item.condition = 'GTE'
          item.value = "0"
        })
        let allDeviceArr = []
        row.forEach(item => {
          allDeviceArr.push(item.serialNumber)
        })
        // console.log(allDeviceArr)
        let allDeviceParam = {
          serialNumber: allDeviceArr,
          metricParameters: newAllSearchMetricParameters,
          operator: conditionList.operator,
          beginTime: conditionList.beginTime,
          endTime: conditionList.endTime,
        }
        // console.log(allDeviceParam)

        // markline value
        if (conditionList.metricParameters) {
          conditionList.metricParameters.forEach(item => {
            markline.push({ name: item.name, value: item.value })
          })
        }
        // console.log(markline)
        this._analysisPmService.searchMetricByMeasureType(allDeviceParam, this.measureType).then(res => {
          // console.log(res)
          this.viewChartAllRes = res
          this.chartFormatData(this.viewChartAllRes, markline, modalChart)
        }).catch((err) => {
          this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
        }).finally(() => {
          this.getOriginalData(allDeviceParam).then(seriesForDownload => {
            // console.log('seriesForDownload', seriesForDownload);
            this.originalData = seriesForDownload
          });
        })
        break;
    }
  }

  changeMeasureType() {
    // console.log('measureType', this.measureType)
    // console.log('chartMode', this.chartMode)
    // console.log('searchResultConditionList', this.searchResultConditionList)
    // console.log('searchResultRow', this.searchResultRow)
    this.chartBlock.start()
    switch (this.chartMode) {
      case 'single':
        let newSingleSearchMetricParameters = cloneDeep(this.searchResultConditionList['metricParameters'])
        newSingleSearchMetricParameters.forEach(item => {
          item.condition = 'GTE'
          item.value = "0"
        })
        let deviceArr = []
        deviceArr.push(this.searchResultRow.serialNumber)
        let singleDeviceParam = {
          serialNumber: deviceArr,
          metricParameters: newSingleSearchMetricParameters,
          operator: this.searchResultConditionList['operator'],
          beginTime: this.searchResultConditionList['beginTime'],
          endTime: this.searchResultConditionList['endTime'],
        }
        // markline value
        let markline = []
        if (this.searchResultConditionList['metricParameters']) {
          this.searchResultConditionList['metricParameters'].forEach(item => {
            markline.push({ name: item.name, value: item.value })
          })
        }
        // console.log(markline)

        this._analysisPmService.searchMetricByMeasureType(singleDeviceParam, this.measureType).then(res => {
          // console.log(res)
          this.viewChartSingleRes = res
          this.chartFormatData(this.viewChartSingleRes, markline)
        }).catch((err) => {
          this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
        }).finally(() => {
        })
        break;
      case 'multi':
        if (this.searchResultRow.length > 20) {
          this._toastrUtilsService.showWarningMessage('The maximum allowed selection is 20 devices.', '')
        } else {
          let newMultiSearchMetricParameters = cloneDeep(this.searchResultConditionList['metricParameters'])
          newMultiSearchMetricParameters.forEach(item => {
            item.condition = 'GTE'
            item.value = "0"
          })
          let deviceArr = []
          this.searchResultRow.forEach(item => {
            deviceArr.push(item.serialNumber)
          })
          let multiDeviceParam = {
            serialNumber: deviceArr,
            metricParameters: newMultiSearchMetricParameters,
            operator: this.searchResultConditionList['operator'],
            beginTime: this.searchResultConditionList['beginTime'],
            endTime: this.searchResultConditionList['endTime'],
          }
          // console.log(multiDeviceParam)
          // markline value
          let markline = []
          if (this.searchResultConditionList['metricParameters']) {
            this.searchResultConditionList['metricParameters'].forEach(item => {
              markline.push({ name: item.name, value: item.value })
            })
          }
          this._analysisPmService.searchMetricByMeasureType(multiDeviceParam, this.measureType).then(res => {
            // console.log(res)
            this.viewChartMultiRes = res
            this.chartFormatData(this.viewChartMultiRes, markline)
          }).catch((err) => {
            this._toastrUtilsService.showErrorMessage(this.FAIL, err.message);
          }).finally(() => {
          })
        }
        break;
    }
  }

  public originalData
  public originalRes
  getOriginalData(param): Promise<any> {
    return this._analysisPmService.searchMetric(param)
      .then(res => {
        this.originalRes = res;
        // console.log(this.originalRes)
        return this.formatDownloadData(this.originalRes);
      })
      .catch(error => {
        console.error(error);
        return [];
      })
      .finally(() => {
      });
  }

  getOriginalByXname(xname: string): any[] | undefined {
    return this.originalData?.find(arr => arr[0]?.xname === xname);
  }

  public deviceList
  public GOTDEVICEINFO = this.translateService.instant('PM.GOTDEVICEINFO');
  gotoDeviceIInfo(sn) {
    this.loading = true
    // console.log(sn)
    Swal.fire({
      title: this.GOTDEVICEINFO + sn + " ?",
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._findDeviceService.findDeviceBySn(sn).pipe(untilDestroyed(this)).subscribe((res: any) => {
          if (res.data && res.data[0] && res.data[0].id) {
            this.router.navigate([`/devices/${res.data[0].id}/device-info`], { skipLocationChange: false });
          }
          this.modalService.dismissAll()
          this.loading = false
        })
      } else {
        this.loading = false
      }
    });
  }

  public pmNotificationData: any;
  public editData = { stages: {}, target: {} };
  convertDeviceAlarm(modal, row) {
    // console.log(row)
    this._eventsService.getAlarmConfig().then(res => {
      // console.log(res)
      this.pmNotificationData = Object.assign({}, row, res);
      // console.log(this.pmNotificationData)
      this.modalService.open(modal, {
        backdrop: false,
        size: 'lg',
        modalDialogClass: 'modal-custom modal-stepper',
        scrollable: true
      });
    }).catch(error => {
      this._toastrUtilsService.showErrorMessage('Device Alarm', error.error);
    })
  }

  refreshRule(row) {
    // console.log(row)
    let beginTime
    let endTime = new Date(this.today.setHours(23, 59, 59)).toISOString()
    let timeDiff = new Date(row.endTime).getTime() - new Date(row.beginTime).getTime(); // 获取时间间隔
    let oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
    let oneWeek = 7 * oneDay; // 一周的毫秒数
    let oneMonth = 30 * oneDay; // 一个月的毫秒数
    if (timeDiff <= oneDay) {
      // console.log("One Day");
      beginTime = this.returnDuration('day')
    } else if (timeDiff <= oneWeek) {
      // console.log("One Week");
      beginTime = this.returnDuration('week')
    } else {
      // console.log("One Month");
      beginTime = this.returnDuration('month')
    }
    let param = {
      name: row.name,
      metricParameters: row.metricParameters,
      operator: row.operator,
      serialNumber: row.serialNumber,
      groupId: row.groupId,
      beginTime: beginTime,
      endTime: endTime,
      description: row.description,
    }
    // console.log(param)

    this._analysisPmService.updateMetric(row.id, param).then(res => {
      // console.log(res)
      this.getMetricList()
      this._toastrUtilsService.showSuccessMessage(`${row.name} was updated successfully`, null)
    })
  }

  dismiss(event, modal) {
    modal.dismiss(event);
  }

  closeDrop(drop) {
    drop.close()
  }

  public showSearchBar: boolean = true
  openSearchBar(): void {
    this.showSearchBar = true;
    // this._analysisPmService.setShowSearchBar(true);
  }
  closeSearchBar(): void {
    this.showSearchBar = false;
    // this._analysisPmService.setShowSearchBar(false);
  }


  pollingInterval: any;
  progress: number = 0;
  totalExpiredCount: number = 0;
  currentExpiredCount: number = 0;
  @ViewChild('progressModal') progressModal: any;
  public EXPIREDUPDATESUCCESS = this.translateService.instant('PM.EXPIREDUPDATESUCCESS');
  public DATAEXPIRED = this.translateService.instant('PM.DATAEXPIRED');
  public DO_UPDATE_EXPIREDDATA = this.translateService.instant('PM.DO_UPDATE_EXPIREDDATA');
  getExpiredCount() {
    return this._analysisPmService.getExpiredCount().then((res: any) => {
      return res.expiredCount ?? 0;
    }).catch((err) => {
      console.error("Error fetching expired count:", err);
      return 0;
    });
  }
  putResearch() {
    this._analysisPmService.putResearch().then(res => {
      this.pollExpiredCount();
    }).catch((err) => {
    });
  }
  checkExpiredCount() {
    this.getExpiredCount().then(expiredCount => {
      this.totalExpiredCount = expiredCount;
      this.currentExpiredCount = expiredCount;

      if (expiredCount > 0) {
        Swal.fire({
          title: this.DATAEXPIRED,
          text: `There are ${expiredCount} expired records in the PM Report. Do you want to update them?`,
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            this.openProgressModal();
            this.putResearch();
          }
        });
      }
    });
  }
  pollExpiredCount() {
    this.pollingInterval = setInterval(() => {
      this.getExpiredCount().then(expiredCount => {
        this.currentExpiredCount = expiredCount;
        this.progress = this.totalExpiredCount > 0
          ? Math.round(((this.totalExpiredCount - expiredCount) / this.totalExpiredCount) * 100)
          : 100;

        if (expiredCount === 0) {
          clearInterval(this.pollingInterval);
          this._toastrUtilsService.showSuccessMessage(this.EXPIREDUPDATESUCCESS, null);
          this.modalService.dismissAll()
          this.getMetricList()
        }
      });
    }, 100)
  }

  openProgressModal() {
    this.progress = 0;
    this.modalService.open(this.progressModal, {
      centered: true,
      size: 'lg'
    });
  }

  performanceReportNameError;
  performanceReportNameCheck(metricRuleName): void {
    this.performanceReportNameError = this.st.check("pmMertricRuleName", metricRuleName) || null
  }


  ngOnInit(): void {
    this.checkExpiredCount()
    // this.showSearchBar = this._analysisPmService.getShowSearchBar();
    this.tableOptionStatus = !!this._datatableCustomizeService.getTableOption(this.accessor.componentId);
    this.tableOption = this._datatableCustomizeService.mergeOption(this.accessor.componentId, this.tableOption);
    const dueTime = this.tableOptionStatus ? 400 : 0;
    this.scrollSubject.pipe(untilDestroyed(this), debounceTime(dueTime)).subscribe(res => {
      if (res.gridScrollHeight && res.tableWidth && !this._resizeObservableService.windowResizeState) {
        this.tableOption = this._datatableCustomizeService.formatColumn(this.tableRowDetails, this.tableOption, this.tableWidth, this.accessor);
      }
    })
    this.getMetricList()
    this.beginTime = new Date(this.today.setHours(0, 0, 0, 0));
    this.endTime = new Date(this.today.setHours(23, 59, 59));

    this.selectPMParamName = this.selectPMParamName.filter(item => {
      return item.value != 'DRB.IPThpUl.sum' && item.value != 'DRB.IPThpDl.sum'
    })

    this.noPermission = !this._authService.check('analysis', 'pmStatistics', 'write')
    this.noAlarmPermission = !this._authService.check('alarm', 'notificationManagement', 'write')
  }

  ngAfterViewChecked(): void {
    const tableWidth = this.tableRowDetails.bodyComponent.innerWidth;
    if (tableWidth && tableWidth > this.tableWidth) {
      const { scrollHeight, innerWidth: tableWidth } = this.tableRowDetails.bodyComponent;
      const { scrollHeight: gridScrollHeight } = this.accessor.gridRef?.el;
      if (((gridScrollHeight && gridScrollHeight > this.gridScrollHeight) || (tableWidth && tableWidth !== this.tableWidth)) && scrollHeight) {
        this.tableWidth = tableWidth;
        this.gridScrollHeight = gridScrollHeight;
        this.scrollSubject.next({ gridScrollHeight, tableWidth });
      }
      this.cdr.detectChanges();
    }
  }
}
