<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="loading"
  (events)="emittedEvents($event);">
  <h4 class="card-title"></h4>
  <div class="card-body">
    <main class="w-100 h-100 d-flex justify-content-between align-items-center">
      <div>
        <h2 class="font-weight-bolder mb-0">{{totalDevices}}</h2>
        <p class="card-text">{{ 'COMMON.ONLINEDEVICE' | translate }}</p>
      </div>
      <div
        class="avatar bg-light-primary p-50 m-0"
        (click)="viewDeviceList()">
        <div
          class="avatar-content"
          *ngIf="!iconloading">
          <i
            data-feather="hard-drive"
            class="font-medium-5"></i>
        </div>
        <div
          *ngIf="iconloading"
          class="spinner-border text-primary"
          role="status">
        </div>
      </div>
    </main>
  </div>
</core-card>
