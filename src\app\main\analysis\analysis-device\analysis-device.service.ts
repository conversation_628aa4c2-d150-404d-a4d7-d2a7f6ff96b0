import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { GridSystemService } from 'app/main/commonService/grid-system.service';

@Injectable({
  providedIn: 'root'
})
export class AnalysisDeviceService {

  public dayItems = [
    { key: 'DASHBOARD.AVGSESSIONS.LAST7DAYS', value: 7, icons: 'seven-days' },
    { key: 'DASHBOARD.AVGSESSIONS.LAST15DAYS', value: 15, icons: 'fifteen-days' },
    { key: 'DASHBOARD.AVGSESSIONS.LAST30DAYS', value: 30, icons: 'thirty-days' }
  ]
  public onDaysChanged: Subject<number>;
  public curDays: number = this.dayItems[0].value
  private key = "analysis-device-data"

  constructor(
    private _httpClient: HttpClient,
    private _gridService: GridSystemService
  ) {
    this.onDaysChanged = new Subject();
    let data = this._gridService.getComponentData(this.key)
    if (data?.days) {
      this.curDays = data.days
    }
  }

  /**
   * Resolver
   *
   * @param {ActivatedRouteSnapshot} route
   * @param {RouterStateSnapshot} state
   * @returns {Observable<any> | Promise<any> | any}
   */
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
    return new Promise<void>((resolve, reject) => {
      resolve();
    });
  }

  getDaysItem() {
    return this.dayItems
  }

  changeDays(days) {
    this.curDays = days
    this._gridService.setComponentData(this.key, {days: days})
    this.onDaysChanged.next(days)
  }

  /**
   * Get analysis device
   */
  getAnalysisDevice(days = this.curDays): Promise<any> {
    let current_day = new Date().toLocaleDateString();
    let date_today = (new Date(current_day)).toJSON();
    let params = new HttpParams()
      .set('days', days);
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/deviceStatistics/device', { params: params }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getAnalysisNewDevice(days = this.curDays): Promise<any> {
    let current_day = new Date().toLocaleDateString();
    let date_today = (new Date(current_day)).toJSON();
    let params = new HttpParams()
      .set('days', days);
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/deviceStatistics/newDevice', { params: params }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getAnalysisSessions(days = this.curDays): Promise<any> {
    let current_day = new Date().toLocaleDateString();
    let date_today = (new Date(current_day)).toJSON();
    let params = new HttpParams()
      .set('days', days)
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/deviceStatistics/sessions', { params: params }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  getAnalysisEventCode(days = this.curDays): Promise<any> {
    let current_day = new Date().toLocaleDateString();
    let date_today = (new Date(current_day)).toJSON();
    let params = new HttpParams()
      .set('days', days)
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/deviceStatistics/eventCode', { params: params }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }
}
