import { ComponentFixture, TestBed } from '@angular/core/testing';

import { XmppStatusComponent } from './data-xmpp-status.component';

describe('XmppStatusComponent', () => {
  let component: XmppStatusComponent;
  let fixture: ComponentFixture<XmppStatusComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ XmppStatusComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(XmppStatusComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
