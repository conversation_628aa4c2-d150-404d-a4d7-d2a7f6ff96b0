import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { EncryptUtilsService } from 'app/main/commonService/encrypt-utils.service';
import { CookieService, CookieOptions } from 'ngx-cookie-service';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  public token = '';
  public tokenSubject: BehaviorSubject<string>;

  constructor(
    private encryptUtilsService: EncryptUtilsService,
    private cookieService: CookieService
  ) { 
    this.tokenSubject = new BehaviorSubject<string>(this.getToken());
    this.tokenSubject.subscribe(data => {
      if (data) {
        this.token = data;
        this.setToken(this.token);
      }
    })
  }
    
  md5(value) {
    return this.encryptUtilsService.md5(value);
  }

  encrypt(value) {
    return this.encryptUtilsService.encrypt(value, this.token);
  }

  decrypt(value) {
    return this.encryptUtilsService.decrypt(value, this.token);
  }

  // hasOwnProperty
  hasOwnProperty(key) {
    const newKey = this.md5(key);
    return localStorage.hasOwnProperty(newKey);
  }
  //set
  set(key, value) {
    const newKey = this.md5(key);
    localStorage.setItem(newKey, this.encrypt(value));
    // localStorage.setItem(key, JSON.stringify(value));
  }
  //get
  get(key) {
    const newKey = this.md5(key);
    return this.decrypt(localStorage.getItem(newKey)) || undefined;
    // return JSON.parse(localStorage.getItem(key)) || undefined;
  }
  // delete
  remove(key) {
    const newKey = this.md5(key);
    localStorage.removeItem(newKey);
  }
  // clear
  clear() {
    localStorage.clear()
  }

  setToken(value) {
    const key = this.md5('token');
    const askey = this.md5('askey');
    localStorage.setItem(key, this.encryptUtilsService.encrypt(value, askey));
  }

  getToken() {
    const key = this.md5('token');
    const askey = this.md5('askey');
    return this.encryptUtilsService.decrypt(localStorage.getItem(key), askey) || '';
  }

  setSession(key, value) {
    const newKey = this.md5(key);
    sessionStorage.setItem(newKey, this.encrypt(value));
  }
  getSession(key) {
    const newKey = this.md5(key);
    return this.decrypt(sessionStorage.getItem(newKey)) || undefined;
  }
  removeSession(key) {
    const newKey = this.md5(key);
    sessionStorage.removeItem(newKey);
  }
  clearSession() {
    sessionStorage.clear()
  }

  setCookie(key, value, options?: CookieOptions) {
    const newKey = this.md5(key);
    this.cookieService.set(newKey, this.encrypt(value), options);
  }
  getCookie(key) {
    const newKey = this.md5(key);
    return this.decrypt(this.cookieService.get(newKey)) || undefined;
  }
  removeCookie(key) {
    const newKey = this.md5(key);
    this.cookieService.delete(newKey);
  }
  clearCookie() {
    this.cookieService.deleteAll();
  }
  checkCookie(key) {
    const newKey = this.md5(key);
    return this.cookieService.check(newKey);
  }
}
