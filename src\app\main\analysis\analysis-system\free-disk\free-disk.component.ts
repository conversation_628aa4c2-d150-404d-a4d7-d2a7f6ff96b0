import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { AnalysisSystemService } from 'app/main/analysis/analysis-system/analysis-system.service';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { ActivatedRoute } from '@angular/router';
import { ChartTimePatchService } from 'app/main/commonService/chart-time-patch.service';

@UntilDestroy()
@Component({
  selector: 'app-free-disk',
  templateUrl: './free-disk.component.html',
  styleUrls: ['./free-disk.component.scss']
})
export class FreeDiskComponent extends Unsubscribe implements OnInit {
  @Input() accessor: any;
  public chartData = [];
  public blockUIStatus = false;
  public dayItems: any[];
  public days: number;
  public nodeItems: any;
  public node: string;
  public timeArr = [];
  selectedIcon: string = '';
  public personalTheme;
  public isShowNoData = true
  public measureItems: any[];
  public measure: any;
  public selectedMeasure: string = '';

  constructor(
    private _analysisSystemService: AnalysisSystemService,
    translateService: TranslateService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private _chartTimePatchService: ChartTimePatchService
  ) {
    super();
    this.dayItems = _analysisSystemService.getDaysItem().map(item => { return { key: translateService.instant(item.key), value: item.value, icons: item.icons } });
    this.days = this.dayItems[0].value;
    this.selectedIcon = this.dayItems[0]?.icons;
    this.customSubscribe(_analysisSystemService.onDaysChanged, days => {
      this.days = days;
      this.selectedIcon = this.dayItems.find(item => item.value === days)?.icons || '';
      this.getNodes(this.node);
    });
    this.customSubscribe(_analysisSystemService.onNodeChanged, nodeId => { this.node = nodeId; this.getNodes(this.node); })
    this.customSubscribe(route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
    
    this.measureItems = _analysisSystemService.getMeasureItem().map(item => { return { key:item.key, value: item.value, icons: item.icons } });
    this.measure = this.measureItems[0].value;
    this.selectedMeasure = this.measureItems[0]?.icons;
    this.customSubscribe(_analysisSystemService.onMeasureChanged, measure => {
      this.measure = measure;
      this.selectedMeasure = this.measureItems.find(item => item.value === measure)?.icons || '';
      this.getAnalysisSystemRes();
    })
  }

  changeFn(item: any) {
    this.selectedIcon = item.icons;
    this.days = item.value;
    this._analysisSystemService.changeDays(this.days);
  }
      changeMeasure(item: any) {
    this.selectedMeasure = item.icons;
    this.measure = item.value;
    this._analysisSystemService.changeMeasure(this.measure);
  }

  changeNode(item: any) {
    this.node = item;
    this._analysisSystemService.changeNode(this.node);
  }

  areAllDataArraysEmpty(chartData: any[]): boolean {
    return chartData.every(item => item.data.length === 0);
  }

  getNodeCluster() {
    this.blockUIStatus = true
    this._analysisSystemService.initClusterNodes().pipe(untilDestroyed(this)).subscribe(res => {
      // console.log(res)
      this.nodeItems = res.list
      this.node = res.node
      this.days = res.days
      this.selectedIcon = this.dayItems.find(item => item.value === this.days)?.icons
      this.getNodes(this.node)
    })
  }

  public currentTotalDisk
  getNodes(node) {
    this._analysisSystemService.getNodes().pipe(untilDestroyed(this)).subscribe((res: any) => {
      // console.log(res)
      let currentNode = res.find(item => {
        return node == item.acsNodeName
      })
      // console.log(currentNode)
      this.currentTotalDisk = currentNode ? currentNode.totalSpace : 0
      // console.log(this.currentTotalDisk)
      this.getAnalysisSystemRes()
    })
  }

  /**
* get Analysis System
*/
  getAnalysisSystemRes() {
    this.blockUIStatus = true
    this._analysisSystemService.getAnalysisSystemData('freeDisk', this.days, this.node, this.measure).then(res => {
      // console.log(res)
      
      res = res.sort((a, b) => {
        return new Date(a.established).getTime() - new Date(b.established).getTime();
      })
      let dataArr = []
      res.forEach((item) => {
        if (item.hasOwnProperty('established')) {
          let keyName = 'Free Disk (Gb)';
          let index = dataArr.findIndex((item) => item.name === keyName);
      
          const hasUsedDisk = typeof item.usedDisk === 'number';
          const freeDisk = hasUsedDisk
            ? (this.currentTotalDisk - item.usedDisk) / 1000000000
            : null;
          const percentage = hasUsedDisk
            ? '(' + Math.round((freeDisk * 1000000000 / this.currentTotalDisk) * 100) + '%)'
            : '(No Data)';
      
          const newData = [item.established, freeDisk, percentage];
      
          if (index === -1) {
            dataArr.push({ name: keyName, value: [newData] });
          } else {
            dataArr[index].value.push(newData);
          }
        }
      });
      //nbi回不足days就補[第一天時間,null]
      this._chartTimePatchService.patchStartTime(dataArr, this.days);
      // console.log(dataArr)

      let data = []
      dataArr.forEach(item => {
        data.push({
          name: this.node,
          data: item.value,
          stack: 'total',
          type: 'line',
          // smooth: true,
          // sampling: 'lttb',
          showSymbol: false,
        })
      })
      this.chartData = data;
      this.blockUIStatus = false;
    }).catch((err) => {
      console.error(err)
      this.isShowNoData = true
    }).finally(() => {
      this.isShowNoData = this.areAllDataArraysEmpty(this.chartData);
      this.cdr.detectChanges();
      this.blockUIStatus = false;
    });
  }

  emittedEvents(event: any): void {
    if (event === 'reload') {
      this.blockUIStatus = true;
      this.getAnalysisSystemRes()
    }else if (event.type === 'changeDay') {
      this.changeFn(event.daysItem);
    }else if (event.type === 'changeMeasure') {
    this.changeMeasure(event.measureItem);
    }else if (event === 'download') {
      // this.triggerDownloadData()

      this._analysisSystemService.getAnalysisSystemRawData('freeDisk', this.days,'usedDisk', this.node,'SystemStatistics-FreeDisk',this.currentTotalDisk).then(response => {
      }).catch(error => {
     console.error(error);
    });
    }else if (event === 'maximize') {
      this.triggerModal()
    }
  }
  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }

  ngOnInit(): void {
    this.getNodeCluster()
  }

  ngOnDestroy(): void {
    this._analysisSystemService.removeNodeListDataObservable();
  }

}

