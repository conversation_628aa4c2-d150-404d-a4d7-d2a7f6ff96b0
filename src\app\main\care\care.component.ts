import { Component, ChangeDetectorRef } from '@angular/core';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from 'app/layout/components/base/BaseComponent';
import { careWidgets } from './careWidgetUtils';
import { UserService } from 'app/auth/service/user.service';
import { CoreConfigService } from '@core/services/config.service';

@Component({
  selector: 'app-care',
  templateUrl: './care.component.html',
  styleUrls: ['./care.component.scss']
})
export class CareComponent extends BaseComponent {

  constructor(
    private _gridSystemService: GridSystemService,
    private cdr: ChangeDetectorRef,
    _genWidgetService: GenWidgetService,
    route: ActivatedRoute,
    private _coreConfigService: CoreConfigService,
    _userService: UserService,
  ) {
    super(_gridSystemService, _genWidgetService, route, careWidgets, _userService);

    // Configure the layout
    this._coreConfigService.config = {
      layout: {
        menu: {
          hidden: true
        },
        customizer: true,
        enableLocalStorage: false
      }
    };

  }

  ngOnInit(): void {
    // this._careCsrService.getServerStatus()
  }
  public contentHeader: object
  ngAfterViewInit() {
    setTimeout(() => { this.gridsterHeight = this._gridSystemService.gridsterHeight }, 0);
  };

  ngAfterViewChecked() {
    if (this._gridSystemService.pageResize) {
      this.gridsterHeight = this._gridSystemService.gridsterHeight
      this._gridSystemService.pageResize = false
      this.cdr.detectChanges()
    }
  }

}
