import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar-portable';
import { PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar-portable';
import { PerfectScrollbarConfigInterface } from 'ngx-perfect-scrollbar-portable';

import { CoreMenuModule } from '@core/components';
import { CoreCommonModule } from '@core/common.module';

import { VerticalMenuComponent } from 'app/layout/components/menu/vertical-menu/vertical-menu.component';
import { VerticalUserManualComponent } from 'app/layout/components/menu/vertical-menu/vertical-user-manual/vertical-user-manual.component';
const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
  suppressScrollX: true,
  wheelPropagation: true
};
@NgModule({
  declarations: [
    VerticalMenuComponent,
    VerticalUserManualComponent
  ],
  imports: [CoreMenuModule, CoreCommonModule, PerfectScrollbarModule, RouterModule],
  providers: [
    {
      provide: PERFECT_SCROLLBAR_CONFIG,
      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG
    }
  ],
  exports: [VerticalMenuComponent,VerticalUserManualComponent]
})
export class VerticalMenuModule { }
