import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, ViewChild, ChangeDetectorRef, Input } from '@angular/core';
import { BarChartComponent } from 'app/main/shared/chart/bar-chart/bar-chart.component';
import { CareService } from 'app/main/care/care.service';
import { Location } from '@angular/common';

@UntilDestroy()
@Component({
  selector: 'app-wifi-clients-status-modal',
  templateUrl: './wifi-clients-status-modal.component.html',
  styleUrls: ['./wifi-clients-status-modal.component.scss']
})
export class WifiClientsStatusModalComponent implements OnInit {
  @Input() mac: any;
  public chartData = [];
  public deviceId: string;
  public sourceData: any = {}
  public customChartOption = {};
  constructor(
    private _careService: CareService,
    private location: Location,
  ) { }

  getChartDataRes() {
    this._careService.getLinkRateHistory(this.deviceId, this.mac).then(res => {
      this.sourceData = res
      this.chartData = this.initChartData()
    })
  }

  initChartData() {
    let data: any = Object.values(this.sourceData)
    var oneDay = 1000 * 60 * 60 * 24;//一天的毫秒数
    var now = new Date().getTime()
    let _data = data[0].filter(item => {
      return (new Date(item.time).getTime()) > (now - oneDay)
    })
    return [
      {
        "name": "DownlinkRate",
        "type": "column",
        "data": _data.map(item => {
          return [new Date(item.time).getTime(), item.DownlinkRate]
        })
      },
      {
        "name": "UplinkRate",
        "type": "column",
        "data": _data.map(item => {
          return [new Date(item.time).getTime(), item.UplinkRate]
        })
      },
      {
        "name": "Signal",
        "type": "line",
        "data": _data.map(item => {
          return [new Date(item.time).getTime(), item.Signal]
        })
      }
    ]
  }


  getDeviceId(): void {
    this._careService.careDeviceChanged.pipe(untilDestroyed(this)).subscribe(res => {
      if (res && res.id) {
        this.deviceId = res.id
        this.getChartDataRes()
      }
    })
  }

  ngOnInit(): void {
    this.getDeviceId()
    this.customChartOption = {
      chart: {
        height: 350,
        type: "line",
        stacked: false
      },
      stroke: {
        width: [1, 1, 4],
        show: true,
        // colors: ["transparent"]
      },
      title: {
        text: "",
        align: "center",
        offsetX: 0,
        style: {
          color: "#008FFB"
        }
      },
      xaxis: {
        type: 'datetime', //设置X轴的类型 三种可选：category、datetime、numeric
        labels: {
          datetimeUTC: false,
          format: 'MM/dd',
        }
      },
      yaxis: [
        {
          seriesName: "DownlinkRate",
          axisTicks: {
            show: true
          },
          axisBorder: {
            show: true,
            color: "#e3c2a2"
          },
          labels: {
            style: {
              colors: "#e3c2a2"
            }
          },
          title: {
            text: "Mbps",
            offsetX: 0,
            offsetY: 0,
            style: {
              color: "#e3c2a2",
              fontSize: "14px"

            },
            rotate: 0
          },
          tooltip: {
            enabled: true
          }
        },
        {
          seriesName: "UplinkRate",
          show: false,
          opposite: true,
          axisTicks: {
            show: true
          },
          axisBorder: {
            show: true,
            color: "#db8e79"
          },
          labels: {
            style: {
              colors: "#db8e79"
            }
          },
          title: {
            text: "Mbps",
            style: {
              color: "#db8e79"
            }
          }
        },
        {
          seriesName: "Signal",
          min: -100,
          max: -10,
          opposite: true,
          axisTicks: {
            show: true
          },
          axisBorder: {
            show: true,
            color: "#70abbd"
          },
          labels: {
            style: {
              colors: "#70abbd",
            },
          },
          title: {
            text: "DBm",
            offsetX: 0,
            offsetY: 0,
            style: {
              color: "#70abbd",
              fontSize: "14px"
            },
            rotate: 0
          }
        }
      ],
    }
  }

}
