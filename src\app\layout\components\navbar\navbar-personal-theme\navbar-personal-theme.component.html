<li
  ngbDropdown
  class="nav-item dropdown-personal-theme mr-25">
  <a
    class="nav-link  dropdown-toggle dropdown-user-link"
    ngbDropdownToggle
    aria-haspopup="true"
    aria-expanded="false"
    id="navbarPersonalThemeDropdown">
    <i
      class="ficon"
      data-feather="grid"></i>
  </a>
  <div
    ngbDropdownMenu
    aria-labelledby="navbarPersonalThemeDropdown"
    class="dropdown-menu dropdown-menu-right">
    <!-- <h6 class="dropdown-header">Customize Widgets</h6> -->
    <div class="dropdown-header d-flex mr-3">
      <h4 class="notification-title mb-0 mr-auto">{{ 'COMMON.WIDGETS' | translate }}</h4>
    </div>
    <div class="dropdown-divider"></div>
    <a ngbDropdownItem>
      <div
        class="custom-control custom-control-primary custom-switch d-flex"
        ngbTooltip="Edit Widet' size and location at current page"
        placement="left">
        <span
          [data-feather]="'edit'"
          class="mr-50"></span>
        {{ 'COMMON.EDITMODE' | translate }}
        <span class="mr-auto"></span>
        <input
          name="editMode"
          type="checkbox"
          unchecked
          class="custom-control-input"
          id="editMode"
          [(ngModel)]="editMode"
          (change)="onChangeEditMode();">
        <label
          class="custom-control-label"
          for="editMode">
        </label>
      </div>
    </a>
    <a
      ngbDropdownItem
      ngbTooltip="Toggle widget to make it visible or invisible"
      placement="left"
      (click)="editComponent(editModal);">
      <span
        [data-feather]="'toggle-left'"
        [class]="'mr-50'"></span>
      {{ 'DEVICES.EDITCOMPONENT' | translate }}
    </a>
    <a
      ngbDropdownItem
      ngbTooltip="Reset all widgets at all pages to default setting"
      placement="left"
      [disabled]="!editMode"
      (click)="reset();"
      [ngClass]="{'notAllowed':!editMode}">
      <span
        [data-feather]="'rotate-ccw'"
        [class]="'mr-50'"></span>
      {{ 'DEVICES.RESET' | translate }}
    </a>
    <div class="dropdown-divider"></div>
    <form class="px-2 py-50">
      <button
        [disabled]="!editMode"
        type="submit"
        class="btn btn-primary btn-block"
        rippleEffect
        (click)="save();">
        {{ 'COMMON.SAVE' | translate }}
      </button>
    </form>
  </div>
</li>
<!-- Modal -->
<ng-template
  #editModal
  let-modal>
  <div
    class="modal-header"
    style="background-color: #283046;">
    <h4
      class="modal-title"
      id="myModalLabel4">
      {{ 'DEVICES.EDITCOMPONENT' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    style="margin: 0px !important;background-color: #161D31;"
    tabindex="0"
    ngbAutofocus>
    <div
      class="row"
      *ngFor="let item of formatWidgets;let i = index">
      <div
        *ngIf="formatWidgets.length > 1"
        class="divider divider-success col-12">
        <div class="divider-text">
          {{ subClassMapping[item.key]?subClassMapping[item.key] : item.key }}
        </div>
      </div>
      <div
        class="col-lg-4 col-sm-6 col-12"
        style="padding: 1rem;"
        *ngFor="let widget of item.widgets;let j = index">
        <div
          class="card"
          style="margin-bottom:0px !important;">
          <div
            class="card-header"
            style="cursor:default !important;">
            <div class="col-10 pl-0">
              <div
                popoverTitle="{{'COMMON.NAME' | translate}}"
                [ngbPopover]="widget.name | translate"
                triggers="mouseenter:mouseleave"
                container="body"
                class="text-truncate text-capitalize">
                <h4>{{ widget.name | translate }}</h4>
              </div>
            </div>
            <div class="col-2  p-50 m-0 custom-control custom-control-primary custom-switch">
              <input
                (click)="statusChange(widget)"
                type="checkbox"
                [checked]="!widget.hidden"
                class="custom-control-input"
                id="personalThemeSwitch{{ i }}-{{ j }}">
              <label
                class="custom-control-label"
                for="personalThemeSwitch{{ i }}-{{ j }}"></label>
            </div>
            <div
              popoverTitle="{{'PROVISIONING.DESCRIPTION' | translate}}"
              [ngbPopover]="widget.description | translate"
              triggers="mouseenter:mouseleave"
              container="body"
              class="col-12 pl-0 text-truncate text-capitalize">
              {{ widget.description | translate }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Accept click')"
      rippleEffect>
      {{ 'COMMON.SAVE' | translate }}
    </button>
  </div>
</ng-template>
<!-- / Modal -->
