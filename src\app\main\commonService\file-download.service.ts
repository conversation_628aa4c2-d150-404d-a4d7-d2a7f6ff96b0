import { HttpClient } from '@angular/common/http';
import { Injectable, Inject } from '@angular/core';
import { DatePipe } from '@angular/common';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from "rxjs/operators";
import { XlsxLoaderService } from './xlsx-loader.service';

@Injectable({
  providedIn: 'root'
})
export class FileDownloadService {

  constructor(
    private _httpClient: HttpClient,
    private datePipe: DatePipe,
    private xlsxLoaderService: XlsxLoaderService
  ) { }

  /**
  * @description: create HttpClient
  * @param {*} url
  * @param {*} params
  * @param {*} responseType
  * @param {*} filePrefix
  * @param {*} formatType
  * @return {*}
  */
  createRequest(url, params, responseType, filePrefix, formatType): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(url, params, { responseType }).subscribe({
        next: (response: any) => {
          let fileName = this.formatFileName(formatType, filePrefix)
          this.generateFile(response, fileName)
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
  * @description: create HttpClient
  * @param {*} url
  * @param {*} params
  * @param {*} responseType
  * @param {*} filePrefix
  * @param {*} formatType
  * @return {*}
  */
  createRequestMethodGet(url, filePrefix, formatType): Promise<any> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(url, { responseType: 'blob' as 'json' }).subscribe({
        next: (response: any) => {
          let fileName = this.formatFileName(formatType, filePrefix)
          this.generateFile(response, fileName)
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
   * @description: generate fileName eg:SummaryReport_20221130_1155.csv
   * @param {*} filePrefix
   * @param {*} formatType
   * @param {*} date?
   * @return {*}
   */
  formatFileName(formatType, filePrefix, date?) {
    let datetime = date || new Date();
    return `${filePrefix}_${this.datePipe.transform(datetime, 'yyyyMMddHHmm')}.${formatType}`
  }

  /**
   * @description: remave element
   * @param {*} event
   * @return {*}
   */
  destroyClickedElement(event) {
    document.body.removeChild(event.target);
  }

  /**
   * @description: create download
   * @param {*} data
   * @param {*} targetName
   * @return {*}
   */
  generateFile(data, targetName) {
    let textFileAsBlob = new Blob([data], { type: "blob" });
    let fileNameToSaveAs = targetName;
    let downloadLink = document.createElement("a");
    downloadLink.download = fileNameToSaveAs;
    downloadLink.innerHTML = "Download File";
    downloadLink.href = window.URL.createObjectURL(textFileAsBlob);
    downloadLink.onclick = this.destroyClickedElement;
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    setTimeout(() => {
      downloadLink.click();
    }, 0, false);
  }

  /**
   * @description: create Local Download
   * @param {*} filePrefix
   * @param {*} formatType
   * @param {*} data
   * @param {*} date?
   * @return {*}
   */
  createLocalDownload(filePrefix, formatType, data, date?) {
    let fileName = this.formatFileName(formatType, filePrefix, date)
    this.generateLocalFile(data, fileName);
  }
  /**
   * @description: create Local download
   * @param {*} data
   * @param {*} targetName
   * @return {*}
   */
  generateLocalFile(data, targetName) {
    //使用 encodeURIComponent 来对 JSON.stringify(data) 的结果进行严格编码 (解决字符串中包括特殊字符)
    let textFile = 'data:text/plain;charset=utf-8,' + encodeURIComponent(JSON.stringify(data));
    // let textFile = 'data:text/plain;charset=utf-8,' + JSON.stringify(data);
    let fileNameToSaveAs = targetName;
    let downloadLink = document.createElement("a");
    downloadLink.download = fileNameToSaveAs;
    downloadLink.innerHTML = "Download File";
    downloadLink.href = textFile;
    downloadLink.onclick = this.destroyClickedElement;
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    setTimeout(() => {
      downloadLink.click();
    }, 0, false);
  }
  /**
  * @description: create New HttpClient
  * @param {*} url
  * @param {*} params
  * @param {*} responseType
  * @param {*} fileName
  * @param {*} formatType
  * @return {*}
  */
  createNewRequest(url, params, responseType, fileName, formatType): Observable<any[]> {
    return this._httpClient.post(url, params, { responseType })
      .pipe(tap((resp: any) => {
        this.generateFile(resp, `${fileName}.${formatType}`);
      }),
        catchError(error => throwError(() => error))
      )
  }

  generateFileToXLSX(dataOption) {
    this.xlsxLoaderService.loadXLSX().then(XLSX => {
      const currentDate = new Date();
      const formattedDate = this.datePipe.transform(currentDate, 'yyyyMMdd_HHmmss');
      const worksheet = XLSX.utils.aoa_to_sheet(dataOption.data)
      worksheet['!cols'] = [
        { wch: 10 }
      ]
      // 新建book
      const workbook = XLSX.utils.book_new()
      // 生成xlsx文件(book,sheet数据,sheet命名)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet 1')
      // 写文件(book,xlsx文件名称)
      XLSX.writeFile(workbook, `${dataOption.name}_${formattedDate}.csv`)
    }).catch(error => {
      console.error('Failed to load XLSX for generateFileToXLSX:', error);
    });
  }


  exportToExcel(data, filename: string, opts?) {
    this.xlsxLoaderService.loadXLSX().then(XLSX => {
      const worksheet: any = XLSX.utils.json_to_sheet(data, opts);
      const workbook: any = XLSX.utils.book_new();

      // Set column width for all columns
      worksheet['!cols'] = Object.keys(data[0]).map((key) => ({ wch: 15 }));

      // Add the worksheet to the workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet 1');

      // Write the workbook to a file
      XLSX.writeFile(workbook, `${filename}.xlsx`);
    }).catch(error => {
      console.error('Failed to load XLSX for exportToExcel:', error);
    });
  }

  exportPMToExcel(data, filename: string) {
    this.xlsxLoaderService.loadXLSX().then(XLSX => {
      const workbook: any = XLSX.utils.book_new();

      data.forEach((item, index) => {
        // Convert each object in the 'data' array to a worksheet
        const worksheet: any = XLSX.utils.json_to_sheet(item.data);

        // Set column width for all columns
        worksheet['!cols'] = Object.keys(item.data[0]).map((key) => ({ wch: 15 }));

        // Add the worksheet to the workbook with a sheet name based on the index
        XLSX.utils.book_append_sheet(workbook, worksheet, item.name);
      });

      // Write the workbook to a file
      XLSX.writeFile(workbook, `${filename}.xlsx`);
    }).catch(error => {
      console.error('Failed to load XLSX for exportPMToExcel:', error);
    });
  }

  downloadPMallToExcel(data: any[], filename: string, sheetName?) {
    this.xlsxLoaderService.loadXLSX().then(XLSX => {
      const workbook: any = XLSX.utils.book_new();
      const combinedData: any[] = [];

      // Create an object to store data rows indexed by timestamp
      const timestampIndex: { [key: string]: any } = {};

      // Combine data into timestamp-indexed object
      data.forEach((item) => {
        item.data.forEach((rowData) => {
          const timestamp = rowData.Time;
          if (!timestampIndex[timestamp]) {
            timestampIndex[timestamp] = { Time: timestamp };
          }
          timestampIndex[timestamp][item.name] = rowData[item.name];
        });
      });

      // Convert the timestamp-indexed object to an array of rows
      for (const timestamp in timestampIndex) {
        if (Object.prototype.hasOwnProperty.call(timestampIndex, timestamp)) {
          combinedData.push(timestampIndex[timestamp]);
        }
      }

      const worksheet: any = XLSX.utils.json_to_sheet(combinedData);
      worksheet['!cols'] = Object.keys(combinedData[0]).map((key) => ({ wch: 15 }));

      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName ? sheetName : 'Sheet 1');
      XLSX.writeFile(workbook, `${filename}.xlsx`);
    }).catch(error => {
      console.error('Failed to load XLSX for downloadPMallToExcel:', error);
    });
  }

  exportPMdeviceToExcel(data, filename: string, sn) {
    this.xlsxLoaderService.loadXLSX().then(XLSX => {
      const worksheet: any = XLSX.utils.json_to_sheet(data);
      const workbook: any = XLSX.utils.book_new();

      // Set column width for all columns
      worksheet['!cols'] = Object.keys(data[0]).map((key) => ({ wch: 15 }));

      // Add the worksheet to the workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, sn);

      // Write the workbook to a file
      XLSX.writeFile(workbook, `${filename}.xlsx`);
    }).catch(error => {
      console.error('Failed to load XLSX for exportPMdeviceToExcel:', error);
    });
  }

  generateTableFromCSV(csvFiles): Promise<any[]> {
    return this.xlsxLoaderService.loadXLSX().then(XLSX => {
      const workbook = XLSX.read(csvFiles, { type: 'binary' });
      let data = [];
      // 遍历每张工作表进行读取（这里默认只读取第一张表）
      for (const sheet in workbook.Sheets) {
        // esline-disable-next-line
        if (workbook.Sheets.hasOwnProperty(sheet)) {
          // 利用 sheet_to_json 方法将 excel 转成 json 数据
          data = data.concat(XLSX.utils.sheet_to_json(workbook.Sheets[sheet]));
          // break; // 如果只取第一张表，就取消注释这行
        }
      }
      return data;
    }).catch(error => {
      console.error('Failed to load XLSX for generateTableFromCSV:', error);
      return [];
    });
  }

}
