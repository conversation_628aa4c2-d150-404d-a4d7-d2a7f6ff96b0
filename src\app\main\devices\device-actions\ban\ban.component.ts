import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewEncapsulation } from '@angular/core';
import { DeviceActionsService } from '../device-actions.service';
import { DeviceInfoService } from 'app/main/devices/device-info/device-info.service'
import { ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';

@UntilDestroy()
@Component({
  selector: 'app-ban',
  templateUrl: './ban.component.html',
  styleUrls: ['./ban.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class BanComponent implements OnInit {
  @Input() mode: string;
  @Input() row: string;
  @Input() selectedDevice?: string;
  @Input() selectedDevices?;

  public currentDevice: string;
  public currentDeviceSN: string;
  public selectSingleSN: string;
  public CONFIRMBAN = this.translateService.instant('DEVICES.ACTION.CONFIRMBAN');
  public PleaseSelect = this.translateService.instant('DEVICES.ACTION.PLESESELECT');
  public BANSUCCESS = this.translateService.instant('DEVICES.ACTION.BANSUCCESS');
  public BANFAIL = this.translateService.instant('DEVICES.ACTION.BANFAIL');
  public DOBAN = this.translateService.instant('DEVICES.ACTION.DOBAN');
  public BANSELECT = this.translateService.instant('DEVICES.ACTION.BANSELECT');

  constructor(
    private _deviceActionsService: DeviceActionsService,
    private _deviceInfoService: DeviceInfoService,
    private route: ActivatedRoute,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService
  ) {
  }

  ban() {
    switch (this.mode) {
      case 'currentDevice':
        Swal.fire({
          title: this.CONFIRMBAN,
          text: this.DOBAN + this.currentDeviceSN + "?",
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            this._deviceActionsService.deactivate(this.currentDevice).then(() => {
              this._toastrUtilsService.showSuccessMessage(this.BANSUCCESS, `S/N: ${this.currentDeviceSN}`)
            }).catch((err) => {
              if (err.status == 403) {
                this._toastrUtilsService.showErrorMessage('Fail!', 'Inadequate permissions');
              } else { 
                this._toastrUtilsService.showErrorMessage(this.BANFAIL, `S/N: ${this.currentDeviceSN}`);
              }
            });
          }
        });
        break;
      case 'singleDevice':
        this.selectSingleSN = this.row['deviceIdStruct'].serialNumber
        Swal.fire({
          title: this.CONFIRMBAN,
          text: this.DOBAN + this.selectSingleSN + "?",
          showCancelButton: true,
          confirmButtonText: this.translateService.instant('COMMON.OK'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {
          if (result.value) {
            this._deviceActionsService.deactivate(this.selectedDevice).then(() => {
              this._toastrUtilsService.showSuccessMessage(this.BANSUCCESS, `S/N: ${this.selectSingleSN}`)
            }).catch((err) => {
              if (err.status == 403) {
                this._toastrUtilsService.showErrorMessage('Fail!', 'Inadequate permissions');
              } else { 
                this._toastrUtilsService.showErrorMessage(this.BANFAIL, `S/N: ${this.selectSingleSN}`);
              }
            });
          }
        });
        break;
      case 'multiDevices':
        if (this.selectedDevices.length) {
          Swal.fire({
            title: this.CONFIRMBAN,
            text: this.BANSELECT,
            showCancelButton: true,
            confirmButtonText: this.translateService.instant('COMMON.OK'),
            cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
            customClass: {
              confirmButton: 'btn btn-primary',
              cancelButton: 'btn btn-danger ml-1'
            }
          }).then((result) => {
            if (result.value) {
              this.selectedDevices.forEach(d => {
                this._deviceActionsService.deactivate(d.id).then(() => {
                  this._toastrUtilsService.showSuccessMessage(this.BANSUCCESS, `S/N: ${d['deviceIdStruct'].serialNumber}`)
                }).catch((err) => {
                  if (err.status == 403) {
                    this._toastrUtilsService.showErrorMessage('Fail!', 'Inadequate permissions');
                  } else { 
                    this._toastrUtilsService.showErrorMessage(this.BANFAIL, `S/N: ${d['deviceIdStruct'].serialNumber}`); 
                  }
                });
              })
            }
          });
        } else {
          Swal.fire({
            title: this.PleaseSelect,
            icon: "warning",
            showCancelButton: false,
            confirmButtonText: this.translateService.instant('COMMON.OK'),
            customClass: {
              confirmButton: 'btn btn-primary',
            }
          })
        }
        break;
    }
  }


  getDevice() {
    this.route.data.pipe(untilDestroyed(this)).subscribe(res => {
      // console.log(res)
      this.currentDeviceSN = res.basicInfo.deviceIdStruct.serialNumber;
    });
  }


  ngOnInit(): void {
    this.currentDevice = this.route.snapshot.paramMap.get('id')
    let isGroup = this.route.snapshot['_routerState'].url.endsWith("group-info")
    if (this.currentDevice && !isGroup) {
      this.getDevice()
    }
  }

}
