import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgApexchartsModule } from 'ng-apexcharts';
import { DragulaModule } from 'ng2-dragula';
import { GoogleMapsModule } from '@angular/google-maps';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxDatatableModule } from '@almaobservatory/ngx-datatable';

import { CoreCommonModule } from '@core/common.module';
import { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';
import { AuthGuard, Navigation, ThemeStatus } from 'app/auth/helpers';
import { CoreCardModule } from '@core/components/core-card/core-card.module';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar-portable';
import { SharedModule } from '../shared/shared.module';
import { ChartModule } from '../shared/chart/chart.module';
import { EchartsModule } from '../shared/echarts/echarts.module'
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { RfMapModule } from '../shared/rf-map/rf-map.module';

import { DashboardComponent } from './dashboard.component';
import { DeviceNumbersComponent } from './top-mini-cards/device-numbers/device-numbers.component';
import { ClientNumbersComponent } from './top-mini-cards/client-numbers/client-numbers.component';
import { UserNumbersComponent } from './top-mini-cards/user-numbers/user-numbers.component';
import { ProductsComponent } from './products/products.component';
import { AvgSessionsComponent } from './avg-sessions/avg-sessions.component';
import { MapComponent } from './map/map.component';
import { GridsterModule } from 'angular-gridster2';
import { GroupNumbersComponent } from './top-mini-cards/group-numbers/group-numbers.component';
import { DashboardCoverageMapComponent } from './dashboard-coverage-map/dashboard-coverage-map.component';
import { PersonalThemeService } from '@core/services/personal-theme.service';
import { DashboardAlarmCountComponent } from './top-mini-cards/dashboard-alarm-count/dashboard-alarm-count.component';
import { DashboardTotalAlarmCountComponent } from './top-mini-cards/dashboard-total-alarm-count/dashboard-total-alarm-count.component';
import { GorupListComponent } from './gorup-list/gorup-list.component';
import { AlarmListComponent } from './alarm-list/alarm-list.component';
import { UeCountComponent } from './top-mini-cards/ue-count/ue-count.component';
import { OnlineProductsComponent } from './online-products/online-products.component';
import { SystemInformationComponent } from './system-information/system-information.component';
import { WifiTotalClientsComponent } from './wifi-total-clients/wifi-total-clients.component';
import { WifiStatisticsOfTotalClientsComponent } from './wifi-statistics-of-total-clients/wifi-statistics-of-total-clients.component';
import { WifiTotalClientsCountComponent } from './top-mini-cards/wifi-total-clients-count/wifi-total-clients-count.component';
import { GroupsLocationComponent } from './groups-location/groups-location.component';
import { DashboardDeviceMapComponent } from './dashboard-device-map/dashboard-device-map.component';

import { FormatIpPipe } from 'app/main/commonService/pipes/format-ip.pipe';
const routes = [
  {
    path: '',
    component: DashboardComponent,
    data: { animation: 'dashboard' },
    canActivate: [AuthGuard],
    canDeactivate: [ThemeStatus, Navigation],
    resolve: {
      pts: PersonalThemeService
    }
  }
];

@NgModule({
  declarations: [
    DashboardComponent,
    DeviceNumbersComponent,
    ClientNumbersComponent,
    UserNumbersComponent,
    ProductsComponent,
    AvgSessionsComponent,
    MapComponent,
    GroupNumbersComponent,
    DashboardCoverageMapComponent,
    DashboardAlarmCountComponent,
    DashboardTotalAlarmCountComponent,
    GorupListComponent,
    AlarmListComponent,
    UeCountComponent,
    OnlineProductsComponent,
    SystemInformationComponent,
    WifiStatisticsOfTotalClientsComponent,
    WifiTotalClientsComponent,
    WifiTotalClientsCountComponent,
    GroupsLocationComponent,
    DashboardDeviceMapComponent,
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ContentHeaderModule,
    TranslateModule,
    CoreCommonModule,
    NgbModule,
    NgSelectModule,
    NgApexchartsModule,
    DragulaModule.forRoot(),
    CoreCardModule,
    GoogleMapsModule,
    GridsterModule,
    PerfectScrollbarModule,
    SharedModule,
    ChartModule,
    NgxDatatableModule,
    EchartsModule,
    FontAwesomeModule,
    RfMapModule
  ],
  providers: [FormatIpPipe],
})
export class DashboardModule { }
