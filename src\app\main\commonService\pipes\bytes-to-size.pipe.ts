import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'bytesToSize'
})
export class BytesToSizePipe implements PipeTransform {
  /**
     * Transform
     *
     * @param {number} bytes
     * @param {number} decimals
     *
     * @returns {any}
     */
  transform(bytes: string | number, decimals?: number): any {
    if (undefined === bytes || null === bytes) return "-";
    if ("N/A" === bytes) return bytes;
    bytes = Number(bytes)
    if (Number.isNaN(bytes)) {
      return '-'
    }
    if (0 === bytes) return "0 Bytes";
    var c = 1024, d = decimals || 2, e = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"], f = Math.floor(Math.log(bytes) / Math.log(c));
    return parseFloat((bytes / Math.pow(c, f)).toFixed(d)) + " " + e[f]
  }

}
