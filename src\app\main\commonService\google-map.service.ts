import { Injectable } from '@angular/core';
import { StorageService } from 'app/main/commonService/storage.service';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class GoogleMapService {
  private isLoadedSubject = new BehaviorSubject<boolean>(false);
  isLoaded$ = this.isLoadedSubject.asObservable();

  private loadingPromise: Promise<void> | null = null;

  constructor(private _storageService: StorageService) { }

  loadGoogleMapsApi(): Promise<void> {
    // 若已經載入完成，不需再次執行
    if (this.isLoadedSubject.value) {
      return Promise.resolve();
    }

    // 若已有正在進行的 Promise，直接回傳，避免重複加載
    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = new Promise((resolve, reject) => {
      // 若全域已定義 google.maps，表示已被加載過
      if (typeof google !== 'undefined' && google.maps) {
        this.isLoadedSubject.next(true);
        resolve();
        return;
      }

      // 檢查是否已有 maps.googleapis.com 腳本標籤
      const existingScript = document.querySelector('script[src*="maps.googleapis.com"]') as HTMLScriptElement;
      if (existingScript) {
        // 如果已存在腳本，但狀態未確定，在腳本 onload 事件完成後更新狀態
        existingScript.onload = () => {
          this.isLoadedSubject.next(true);
          resolve();
        };
        existingScript.onerror = () => reject('Failed to load Google Maps API');
        return;
      }

      // 如果沒有已存在的腳本且 global 沒有 google.maps，需要手動載入
      const apiKey = this._storageService.get('apiKey');
      if (!apiKey) {
        reject('Google Maps API Key is missing');
        return;
      }

      const script = document.createElement('script') as HTMLScriptElement;
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}`;
      script.async = true;
      script.defer = true;
      script.onload = () => {
        this.isLoadedSubject.next(true);
        resolve();
      };
      script.onerror = () => reject('Failed to load Google Maps API');
      document.body.appendChild(script);
    });

    return this.loadingPromise;
  }
}
