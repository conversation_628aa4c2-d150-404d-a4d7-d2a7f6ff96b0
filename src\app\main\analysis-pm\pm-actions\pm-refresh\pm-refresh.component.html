<button
  [disabled]="selectedMetrics.length!=0||noPermission"
  *ngIf="mode=='singleMetric'"
  type="button"
  class="btn icon-btn btn-sm tableActionButton"
  ngbTooltip="{{ 'PM.RESEARCH' | translate }}"
  container="body"
  (click)="refresh(modalBasic);"
  rippleEffect>
  <svg
    width="14"
    height="14"
    class="text-primary">
    <use href="./../assets/fonts/added-icon.svg#tabler-icons-filter-research"></use>
  </svg>
</button>
<a
  [disabled]="selectedMetrics.length==0||noPermission"
  *ngIf="mode=='multiMetrics'"
  (click)="refresh(modalBasic);"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center">
  <svg
    width="14"
    height="14"
    class="mr-50">
    <use href="./../assets/fonts/added-icon.svg#tabler-icons-filter-research"></use>
  </svg>
  {{ 'PM.RESEARCH' | translate }}
</a>
<a
  [disabled]="noPermission"
  *ngIf="mode=='allMetrics'"
  (click)="refresh(modalBasic);"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center">
  <svg
    width="14"
    height="14"
    class="mr-50">
    <use href="./../assets/fonts/added-icon.svg#tabler-icons-filter-research"></use>
  </svg>
  {{ 'PM.RESEARCHALL' | translate }}
</a>
<!-- Modal -->
<ng-template
  #modalBasic
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      {{ 'PM.REFRESHLOADING' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <div class="progress-wrapper">
      <ngb-progressbar
        showValue="true"
        type="primary"
        [value]="20"
        [striped]="true"
        [height]="'2rem'"
        [value]="progress"></ngb-progressbar>
    </div>
  </div>
</ng-template>
<!-- / Modal -->
