import { Component, ChangeDetectorRef } from '@angular/core';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { widgets } from './analysisDeviceWidgetUtils';
import { ActivatedRoute } from '@angular/router';
import { BaseComponent } from 'app/layout/components/base/BaseComponent';
import { UserService } from 'app/auth/service/user.service';

@Component({
  selector: 'app-analysis-device',
  templateUrl: './analysis-device.component.html',
  styleUrls: ['./analysis-device.component.scss']
})
export class AnalysisDeviceComponent extends BaseComponent {

  constructor(
    private _gridSystemService: GridSystemService,
    private cdr: ChangeDetectorRef,
    _genWidgetService: GenWidgetService,
    route: ActivatedRoute,
    _userService: UserService,
  ) {
    super(_gridSystemService, _genWidgetService, route, widgets, _userService);
  }

  ngAfterViewInit() {
    setTimeout(() => { this.gridsterHeight = this._gridSystemService.gridsterHeight }, 0);
  };

  ngAfterViewChecked() {
    if (this._gridSystemService.pageResize) {
      this.gridsterHeight = this._gridSystemService.gridsterHeight
      this._gridSystemService.pageResize = false
      this.cdr.detectChanges()
    }
  }

}
