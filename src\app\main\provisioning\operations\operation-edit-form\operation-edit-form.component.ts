import { Component, OnInit, Input, EventEmitter, Output, ViewEncapsulation, ViewChild, ChangeDetectorRef, ElementRef, AfterViewChecked, OnD<PERSON>roy } from '@angular/core';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { OperationsService } from 'app/main/provisioning/operations/operations.service';
import { ProductDataService } from 'app/main/products/products.service';
import { UserService } from 'app/auth/service/user.service';
import Stepper from 'bs-stepper';
import cloneDeep from 'lodash/cloneDeep';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { SwalUtilsService } from 'app/main/commonService/swal-utils.service';
import { cwmpSelectRpc, uspSelectRpc, cwmpComponentMap, uspComponentMap, netconfComponentMap, netconfSelectRpc } from '../components/utils';
import { forkJoin, fromEvent } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SanityTestService } from 'app/main/commonService/sanity-test.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Observable, of } from 'rxjs';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { BlockUI, NgBlockUI } from 'ng-block-ui';

@UntilDestroy()
@Component({
  selector: 'app-operation-edit-form',
  templateUrl: './operation-edit-form.component.html',
  styleUrls: ['./operation-edit-form.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class OperationEditFormComponent extends Unsubscribe implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('stepperModal') private stepperModal: ElementRef;
  @ViewChild('stepperHeader') private stepperHeader: ElementRef;
  @ViewChild('map', { static: false }) mapElement!: ElementRef;
  @Output() dismissEvt = new EventEmitter<string>();
  @Output() selectedEvt = new EventEmitter<string>();
  @Input() editData: any;
  @BlockUI('searchResultBlock') searchResultBlock: NgBlockUI;
  // public
  public typeList: any = [
    // { name: 'CWMP (TR-069)', value: 'cwmp' },
    // { name: 'USP (TR-369)', value: 'usp' },
    // { name: 'NETCONF', value: 'netconf' },
  ];
  public apiLoaded: Observable<boolean>;
  public selectYangList = [];
  public selectRpcselected: any;
  public selectRpc = [];
  public componentMap: any;
  public options: any;
  public productList = [];
  public productData = [];
  public moduleData = {};
  public netconfOptions = {
    yangRpc: {},
    namespace: '',
    parentNodeList: [],
    childNodeList: {},
    nodeList: [],
    moduleName: '',
    yangNamespace: ''
  };

  public loading = false;
  public stepperIndex = 0;
  public get noPermission(): boolean {
    return !this._authenticationService.check('provisioning', 'operationSetup', 'write');
  }
  // private
  private modernWizardStepper: Stepper;
  private operationEditStepper: any;
  public mapType: any
  currentStep = 1

  @ViewChild('acc') acc: any;
  public CONFACTIONRE = this.translateService.instant('CONFIRM.CONFACTIONRE');
  public DOGROUP = this.translateService.instant('CONFIRM.DOGROUP');
  public ACTION = this.translateService.instant('CONFIRM.ACTION');
  public FORMFAIL = this.translateService.instant('CONFIRM.FORMFAIL');
  public OPERATION = this.translateService.instant('CONFIRM.OPERATION');
  public PROFILES = this.translateService.instant('PROVISIONING.PROFILES');
  public OPERUPDATESUCC = this.translateService.instant('CONFIRM.OPERUPDATESUCC');
  public OPERADDSUCC = this.translateService.instant('CONFIRM.OPERADDSUCC');
  public WASUPDATE = this.translateService.instant('CONFIRM.WASUPDATE');
  public WASADD = this.translateService.instant('CONFIRM.WASADD');
  public CROSSCLICK = this.translateService.instant('CONFIRM.CROSSCLICK');
  public CONFIRMRESETLOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMRESETLOCA');
  public DORESETLOCA = this.translateService.instant('DEVICES.ACTION.DORESETLOCA');
  public SAVESUCC = this.translateService.instant('DEVICES.ACTION.SAVESUCC');
  public SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
  public CONFIRMSAVELOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMSAVELOCA');
  public DOSAVELOCA = this.translateService.instant('DEVICES.ACTION.DOSAVELOCA');

  constructor(
    private _authenticationService: AuthenticationService,
    private _operationsService: OperationsService,
    private _productDataService: ProductDataService,
    private _toastrUtilsService: ToastrUtilsService,
    private _swalUtilsService: SwalUtilsService,
    private translateService: TranslateService,
    private _userService: UserService,
    private st: SanityTestService,
    private cdr: ChangeDetectorRef,
  ) {
    super()
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CONFACTIONRE = this.translateService.instant('CONFIRM.CONFACTIONRE');
      this.DOGROUP = this.translateService.instant('CONFIRM.DOGROUP');
      this.ACTION = this.translateService.instant('CONFIRM.ACTION');
      this.FORMFAIL = this.translateService.instant('CONFIRM.FORMFAIL');
      this.OPERATION = this.translateService.instant('CONFIRM.OPERATION');
      this.PROFILES = this.translateService.instant('PROVISIONING.PROFILES');
      this.OPERUPDATESUCC = this.translateService.instant('CONFIRM.OPERUPDATESUCC');
      this.OPERADDSUCC = this.translateService.instant('CONFIRM.OPERADDSUCC');
      this.WASUPDATE = this.translateService.instant('CONFIRM.WASUPDATE');
      this.WASADD = this.translateService.instant('CONFIRM.WASADD');
      this.CROSSCLICK = this.translateService.instant('CONFIRM.CROSSCLICK');

    })
  }

  /**
   * Modern Horizontal Wizard Stepper Next
   */
  modernHorizontalNext(data) {
    data.onSubmit()
    if (data.form.valid === true) {
      if (this.editData.type === 'netconf') this.getYangModuleListRes();
      this.modernWizardStepper.next();
    }
  }
  /**
   * Modern Horizontal Wizard Stepper Previous
   */
  modernHorizontalPrevious() {
    this.modernWizardStepper.previous();
  }

  stepNavigate(detailsForm?) {
    const index = detailsForm ? 2 : 1;
    if (this.editData.readOnly) {
      this.modernWizardStepper.to(1);
      if (index === 2 && this.editData.type === 'netconf') {
        this.getYangModuleListRes();
      }
    } else {
      if (detailsForm) {
        detailsForm.onSubmit()
        if (detailsForm.form.valid === true) {
          if (this.editData.type === 'netconf') this.getYangModuleListRes();
          this.modernWizardStepper.to(2);
        }
      } else {
        this.modernWizardStepper.to(1);
      }
    }
  }


  /**
   * @description: change type select
   * @param {*} event
   * @return {*}
   */
  onChangeType(event) {
    this.editData.productName = [];
    this.filterProductList(event?.value);
    this.onTypeChange(event?.value);
  }
  selectProduct(event) {
    if (this.editData.productName.slice(-1)[0] === 'ALL') {
      this.editData.productName = ['ALL'];
    } else if (this.editData.productName.indexOf('ALL') === 0) {
      this.editData.productName = this.editData.productName.slice(1);
    }
  }
  /**
   * @description: add Rpc
   * @param {*} rpc
   * @return {*}
   */
  onAddRpc(rpc) {
    const { name: rpcName, moduleName } = rpc;
    this.netconfOptions.moduleName = moduleName || '';
    this.netconfOptions.yangRpc = this.moduleData[this.netconfOptions.moduleName]?.rpc;
    this.netconfOptions.yangNamespace = this.moduleData[this.netconfOptions.moduleName]?.namespace;
    this.editData.children.push({
      messageType: rpcName,
      message: {},
      options: this.options,
      isAddStatus: true,
      netconfOptions: Object.assign({}, this.netconfOptions),
      productName: this.editData.productName
    })
    this.selectRpcselected = null;
    this.clickOperation(this.editData.children[this.editData.children.length - 1], this.editData.children.length - 1)
  }
  /**
   * @description: remove Rpc
   * @param {*} item
   * @param {*} i
   * @return {*}
   */
  onRemoveRpc(item, i) {
    this._swalUtilsService.commonSwal(this.CONFACTIONRE, this.DOGROUP + '<span class="h3 text-danger">' + item.messageType + '</span>' + this.OPERATION, 'warning', () => {
      this.editData.children.splice(i, 1);
    }, '40rem')
    this.showOperation = false
  }

  simplifyData(actions) {
    let messages = []
    Object.values(actions).forEach((item: any) => {
      item.forEach(it => {
        messages.push(...it.messages)
      })
    })
    return messages
  }

  checkParams(params) {
    let formError = {}
    let errorMsg: string;
    let urlKeyMap = [
      'failureURL',
      'successURL',
      'url'
    ]
    formError["name"] = this.st.check("name", params.name)
    formError["version"] = this.st.check("version", params.version)
    params.messages.forEach(item => {
      for (let key in item) {
        if (urlKeyMap.includes(key) && item[key]) {
          formError[key] = this.st.check("isURL", item[key])
        }
      }
      if (item.operations) {
        item.operations.forEach(operations => {
          for (let i in operations) {
            if (urlKeyMap.includes(i) && operations[i]) {
              formError[i] = this.st.check("isURL", operations[i])
            }
          }
        })
      }
    })
    for (let i in formError) {
      if (formError[i]) {
        errorMsg = `${i}:${formError[i]}`
        break
      }
    }
    if (!!errorMsg) {
      this._toastrUtilsService.showWarningMessage(this.OPERATION, errorMsg);
    }
    return !!errorMsg
  }
  /**
   * On Submit
   */
  onSubmit(TDValidationForm) {
    this.editData.tags = this.tagArr.map(item => item.name)
    this.editData.tags.forEach(item => {
      if (this.st.check("profileTag", item.name) != null) {
        this.formError["tag"] = this.st.check("profileTag", item.name)
      }
    })
    if (!TDValidationForm.form.valid) {
      this._toastrUtilsService.showWarningMessage('Warning!', this.FORMFAIL);
    } else {
      const checkActions = this.checkActions();
      if (checkActions.isError) {
        return false;
      } else {
        let params;

        params = Object.assign({}, this.editData);
        // console.log(params)
        // params.actions = checkActions.actions;
        params.messages = this.simplifyData(checkActions.actions)
        if (this.checkParams(params)) {
          return false
        }
        this.loading = true;
        params = this.filterInvalidAttr(params)
        if (this.editData.id) {
          delete params.id;
          delete params.children;
          delete params.updated;
          delete params.actions;
          // console.log(this.editData)
          this._operationsService.setOperation(this.editData.id, params).pipe(untilDestroyed(this)).subscribe({
            next: res => {
              this._toastrUtilsService.showSuccessMessage(this.OPERUPDATESUCC, this.PROFILES + ' (ID: ' + this.editData.id + this.WASUPDATE);
              this.selectedEvt.emit(this.OPERUPDATESUCC);
              this.dismissEvt.emit(this.CROSSCLICK);
            },
            error: error => {
              this.loading = false;
              this._toastrUtilsService.showErrorMessage(this.PROFILES, error.error);
            },
            complete: () => {
              this.loading = false;
            }
          })
        } else {
          delete params.children;
          delete params.actions;
          this._operationsService.addOperation(params).pipe(untilDestroyed(this)).subscribe({
            next: res => {
              this._toastrUtilsService.showSuccessMessage(this.OPERADDSUCC, this.PROFILES + ' (ID: ' + res + this.WASADD);
              this.selectedEvt.emit(this.OPERADDSUCC);
              this.dismissEvt.emit(this.CROSSCLICK);
            },
            error: error => {
              this.loading = false;
              this._toastrUtilsService.showErrorMessage(this.PROFILES, error.error);
            },
            complete: () => {
              this.loading = false;
            }
          })
        }
      }
    }
  }

  filterInvalidAttr(stages) {
    if (this.editData.type == 'usp') {
      stages.messages.forEach(messages => {
        if (messages.messageType == 'Add' && messages.hasOwnProperty("createObjs")) {
          messages.createObjs.forEach(objs => {
            objs.paramSettings = objs.paramSettings.filter(item => !(["unsignedInt", "Int"].includes(item.type) && item.value === ""));
            objs.paramSettings.forEach(item => {
              delete item.type;
            });
          })
        }
      })
    }
    return stages
  }

  isCommandKeyAvailable(rpc) {
    var rpcNames = ['ScheduleInform', 'Reboot', 'Download', 'Upload', 'ScheduleDownload', 'CancelTransfer', 'ChangeDUState'];
    return rpcNames.filter(function (item) {
      return item == rpc
    }).length > 0;
  };
  checkActions() {
    let isError = false;
    let actions = {};
    this.editData.children.forEach((item, index) => {
      let rpc: any = {};
      if (typeof item.getRpcEntry == 'function') {
        rpc = item.getRpcEntry();
        if (!rpc.isError && this.isCommandKeyAvailable(rpc.message.messageType)) {
          rpc.message.commandKey = item.commandKey;
        }
      } else {
        rpc = {};
        rpc.isError = false;
        rpc.message = item.message;
        rpc.message.messageType = item.messageType;
        if (this.isCommandKeyAvailable(rpc.message.messageType)) {
          rpc.message.commandKey = item.commandKey;
        }
      }
      if (rpc.isError) {
        isError = true;
        this._toastrUtilsService.showErrorMessage(item.messageType, rpc.errorMessage);
      } else {
        var configurationActionList = [];
        var messageList = [];
        let configurationAction: any = {};
        messageList.push(rpc.message);
        configurationAction.messages = messageList;
        configurationActionList.push(configurationAction);
        actions[index] = configurationActionList;
        // configurationActions = {};
        // configurationActions.actions = actions;
      }
    })
    return {
      isError: isError,
      actions: actions
    };
  }
  /**
   * @description: Change Type
   * @param {*} type
   * @return {*}
   */
  onTypeChange(type) {
    switch (type) {
      case 'cwmp':
        this.selectRpc = cwmpSelectRpc;
        this.componentMap = cwmpComponentMap;
        this.getUspDataModelRes('cwmp');
        break;
      case 'usp':
        this.selectRpc = uspSelectRpc;
        this.componentMap = uspComponentMap;
        this.getUspDataModelRes('usp');
        break;
      case 'netconf':
        this.selectRpc = netconfSelectRpc;
        this.componentMap = netconfComponentMap;
        break;
      default:
        this.selectRpc = [];
        this.componentMap = {};
        break;
    }
  }
  filterProductList(type) {
    this.productList = [].concat(this.productData.filter(item => item.protocol && item.protocol === type)).sort((a, b) => {
      if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
      return 1
    });
    if (this._authenticationService.isAdmin) {
      this.productList.unshift({
        name: 'ALL',
        value: 'ADMIN'
      });
    }
  }
  formatNetconf() {
    const yangComponentMap = {};
    const yangModuleRpcComponent = this.componentMap['YangModuleRpc'];
    this.editData.children.forEach(item => {
      item.netconfOptions = { ...this.netconfOptions };
      item.netconfOptions.namespace = item.message.namespace;
      item.netconfOptions.moduleName = item.message.moduleName;
      item.netconfOptions.yangRpc = this.moduleData[item.message.moduleName]?.rpc;
      if (!this.componentMap[item.messageType]) {
        yangComponentMap[item.messageType] = yangModuleRpcComponent;
      }
    })
    this.componentMap = Object.assign(this.componentMap, yangComponentMap);
  }
  /**
   * @description: prepare Data
   * @param {*} data
   * @return {*}
   */
  prepareData(data) {
    if (data.productName && data.productName.includes("ADMIN")) data.productName[data.productName.map((x, i) => [i, x]).filter(x => x[1] == 'ADMIN')[0][0]] = 'ALL';
    data.children = data.children || [];
    this.onTypeChange(data.type);
    this._productDataService.getProductList().pipe(untilDestroyed(this)).subscribe(res => {
      this.productData = res;
      this.filterProductList(data.type);
    });
  }
  /**
   * @description: get Usp Data Model response
   * @return {*}
   */
  getUspDataModelRes(type) {
    this._operationsService.getTempDataModel(type).then(res => {
      this.options = res;
      this.editData.children.forEach((item) => {
        if (item) item.options = this.options;
      })
    });
  }
  getYangModuleListRes() {
    this._operationsService.getNetconfData(JSON.stringify(this.editData.productName)).pipe(untilDestroyed(this)).subscribe((res: any) => {
      this.moduleData = res;
      this.formatNetconfData(res);
    })
  }
  formatNetconfData(moduleList) {
    for (let yang in moduleList) {
      const module = moduleList[yang];
      if (module.rpc && Object.keys(module.rpc).length) {
        this.formatYangRpcList(yang, module.rpc);
      }
      if (module.data && Object.keys(module.data).length) {
        const { isWritable, parentNodeList, childNodeList, nodeList } = this.formatNodeList(module.data, module.namespace, yang);
        this.netconfOptions.nodeList.push(...nodeList);
        this.netconfOptions.parentNodeList.push(...parentNodeList);
        this.netconfOptions.childNodeList = Object.assign(this.netconfOptions.childNodeList, childNodeList);
      }
    }
    this.netconfOptions.nodeList.sort((a, b) => a.name.localeCompare(b.name));
    this.netconfOptions.parentNodeList.sort((a, b) => a.name.localeCompare(b.name));
    this.formatNetconf();
  }
  formatYangRpcList(yang, rpc) {
    const baseRpcList = this.selectRpc.filter(item => item.group === 'Base').map(item => item.name);
    const yangRpc = [];
    const yangComponentMap = {};
    const yangModuleRpcComponent = this.componentMap['YangModuleRpc'];
    for (let key in rpc) {
      if (rpc[key].type === 'rpc' && !baseRpcList.includes(key)) {
        yangRpc.push({
          group: 'Yang Module',
          name: key,
          moduleName: yang,
        })
        yangComponentMap[key] = yangModuleRpcComponent;
      }
    }
    this.selectRpc = this.selectRpc.concat(yangRpc);
    this.componentMap = Object.assign(this.componentMap, yangComponentMap);
  }
  formatNodeList(data, namespace, moduleName) {
    const caseNames = [];
    const ignoreList = ['choice', 'case'];
    let nodeList = [];
    const writable = {};
    for (let k in data) {
      if (k.indexOf('.') === -1) writable[k] = !(data[k].writable === false);
      if (data[k].type === 'case') caseNames.push(`${k.split('.').slice(-2).join('.')}.`);
    }
    for (let k in data) {
      const key = k.split('.')[0];
      data[k].writable = writable[key];
      nodeList.push(Object.assign({ name: k, label: k, namespace, moduleName }, data[k]));
    }
    const isWritable = nodeList.some(item => item.writable === true);
    const parentNodeList = [];
    const childNodeList = {};
    nodeList.forEach(item => {
      caseNames.forEach(c => {
        item.name = item.name.replace(c, '');
      })
      if (item.type === 'list') {
        parentNodeList.push(item);
        if (!childNodeList[item.name]) childNodeList[item.name] = [];
        nodeList.forEach(i => {
          if (i.label.startsWith(`${item.label}.`) && !ignoreList.includes(i.type)) {
            childNodeList[item.name].push(Object.assign({}, i, { name: i.label.replace(`${item.label}.`, ''), parentNode: item.name }));
          }
        })
      }
    })
    nodeList = nodeList.filter(item => !ignoreList.includes(item.type));
    return { isWritable, parentNodeList, childNodeList, nodeList };
  }
  getProtocolLicense() {
    const protocolMap = {
      'cwmp': 'CWMP (TR-069)',
      'usp': 'USP (TR-369)',
      'netconf': 'NETCONF'
    };
    this._userService.getPageAuthority('protocol').then((data: any) => {
      this.typeList = data.map((item) => {
        return {
          name: protocolMap[item],
          value: item
        }
      }).filter(item => Object.keys(protocolMap).includes(item.value));
      if (this.editData.type) {
        this.typeList = this.typeList.map((item: any) => item = { ...item, disabled: !this.editData.type.includes(item.value) })
      }
    })
  }
  public formError: any = {}


  inputingChange(obj, attr) {
    this[attr] = obj.text
    this.editData[attr] = obj.text
    this.formError[attr] = obj.valid ? null : obj.errorMsg
    // this.getLatLng(obj,this[attr])
  }

  ngOnInit(): void {
    this.getProtocolLicense()
    this.editData.readOnly = this.noPermission;
    const tags = this.editData.tags || [];
    this.tagArr = tags.map((item, index) => ({ name: item, index }));
    this.prepareData(this.editData);
    this.operationEditStepper = document.querySelector('#operationEditStepper');
    this.modernWizardStepper = new Stepper(this.operationEditStepper, {});
    fromEvent(this.operationEditStepper, 'show.bs-stepper').pipe(untilDestroyed(this)).subscribe((event: any) => {
      this.stepperIndex = event.detail.indexStep;
      console.log(this.stepperIndex)
    })
  }

  public stepperContentHeight
  ngAfterViewChecked() {
    this.stepperContentHeight = this.stepperModal.nativeElement.offsetHeight - this.stepperHeader.nativeElement.offsetHeight;
    // 手動觸發變更檢測
    this.cdr.detectChanges();
  };

  public clickedOperation
  public showOperation: boolean = false;
  selectedItemId: number | null = null;
  clickOperation(item, index: number): void {
    this.clickedOperation = item;
    this.showOperation = false;
    this.cdr.detectChanges();
    this.showOperation = true;

    this.selectedItemId = index; // 保存当前选定项的索引
  }

  isItemSelected(index: number): boolean {
    // 判断某个项是否被选中
    return this.selectedItemId === index;
  }

  public tagArr: any = []
  public newTag: any = ''
  public FAILRegister

  addTag(newTag) {
    this.tagArr = this.tagArr.map((item, index) => {
      return {
        index: index,
        name: item.name
      }
    })
    if (this.tagArr.some(item => item.name === this.newTag)) {
      return this._toastrUtilsService.showErrorMessage(this.FAILRegister, this.translateService.instant('DEVICES.EXISTTAG'));
    }
    this.tagArr.push({ index: this.tagArr.length, name: newTag })
    this.newTag = ''
  }

  deleteTag(item) {
    this.tagArr.splice(item.index, 1)
    this.tagArr = this.tagArr.map((item, index) => {
      return {
        name: item.name,
        index: index
      }
    });
  }
  ngOnDestroy() {
    // BS Stepper清理
    if (this.modernWizardStepper) {
      this.modernWizardStepper.destroy();
      this.modernWizardStepper = null;
    }

    // DOM引用清理
    this.operationEditStepper = null;

    // ViewChild引用清理
    this.stepperModal = null;
    this.stepperHeader = null;
    this.mapElement = null;

    // 大对象引用清理
    this.editData = null;
    this.productData = null;
    this.moduleData = null;
    this.options = null;
    this.netconfOptions = null;

    // 数组引用清理
    this.tagArr = null;
    this.selectRpc = null;
    this.productList = null;
    this.typeList = null;
    this.selectYangList = null;

    // 其他对象引用清理
    this.componentMap = null;
    this.clickedOperation = null;
    this.formError = null;

    // ChangeDetectorRef清理
    this.cdr = null;
  }
}
