import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, Input, OnInit, OnD<PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { AnalysisSystemService } from 'app/main/analysis/analysis-system/analysis-system.service';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { ActivatedRoute } from '@angular/router';
import { timer, Subscription } from 'rxjs';
import { ChartTimePatchService } from 'app/main/commonService/chart-time-patch.service';

@UntilDestroy()
@Component({
  selector: 'app-sessions-rate',
  templateUrl: './sessions-rate.component.html',
  styleUrls: ['./sessions-rate.component.scss']
})
export class SessionsRateComponent extends Unsubscribe implements OnInit, OnDestroy {
  @Input() accessor: any;
  public chartData = [];
  public loading = false;
  public blockUIStatus = false;
  public dayItems: any[];
  public days: number;
  public nodeItems: any;
  public node: string;
  public timeArr = [];
  selectedIcon: string = '';
  public personalTheme;
  public isShowNoData = true
  public measureItems: any[];
  public measure: any;
  public selectedMeasure: string = '';

  constructor(
    private _analysisSystemService: AnalysisSystemService,
    private _route: ActivatedRoute,
    translateService: TranslateService,
    private cdr: ChangeDetectorRef,
    private _chartTimePatchService: ChartTimePatchService
  ) {
    super();
    this.dayItems = _analysisSystemService.getDaysItem().map(item => { return { key: translateService.instant(item.key), value: item.value, icons: item.icons } });
    this.days = this.dayItems[0].value;
    this.selectedIcon = this.dayItems[0]?.icons;
    this.customSubscribe(_analysisSystemService.onDaysChanged, days => {
      this.days = days;
      this.selectedIcon = this.dayItems.find(item => item.value === days)?.icons || '';
      this.getAnalysisSystemRes();
    });
    this.customSubscribe(_analysisSystemService.onNodeChanged, nodeId => { this.node = nodeId; this.getAnalysisSystemRes(); })
    this.customSubscribe(_route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
    
    this.measureItems = _analysisSystemService.getMeasureItem().map(item => { return { key:item.key, value: item.value, icons: item.icons } });
    this.measure = this.measureItems[0].value;
    this.selectedMeasure = this.measureItems[0]?.icons;
    this.customSubscribe(_analysisSystemService.onMeasureChanged, measure => {
      this.measure = measure;
      this.selectedMeasure = this.measureItems.find(item => item.value === measure)?.icons || '';
      this.getAnalysisSystemRes();
    })
  }

  changeFn(item: any) {
    this.selectedIcon = item.icons;
    this.days = item.value;
    this._analysisSystemService.changeDays(this.days);
  }

  changeMeasure(item: any) {
    this.selectedMeasure = item.icons;
    this.measure = item.value;
    this._analysisSystemService.changeMeasure(this.measure);
  }

  changeNode(item: any) {
    this.node = item;
    this._analysisSystemService.changeNode(this.node);
  }

  areAllDataArraysEmpty(chartData: any[]): boolean {
    return chartData.every(item => item.data.length === 0);
  }

  getNodeCluster() {
    this._analysisSystemService.initClusterNodes().pipe(untilDestroyed(this)).subscribe(res => {
      // console.log(res)
      this.nodeItems = res.list
      this.node = res.node
      this.days = res.days
      this.selectedIcon = this.dayItems.find(item => item.value === this.days)?.icons
      this.getAnalysisSystemRes()
    })
  }

  /**
* get Analysis System
*/
  getAnalysisSystemRes() {
    this.blockUIStatus = true
    this.isShowNoData = false
    this._analysisSystemService.getAnalysisSystemData('cpeSessRate', this.days, this.node, this.measure).then(res => {
      // console.log(res)
      
      res = res.sort( (a, b)=> {
        return new Date(a.established).getTime() - new Date(b.established).getTime();
      })
      let dataArr = []
      res.forEach( (item)=> {
        if (item.hasOwnProperty('sessionRate') && item.hasOwnProperty('established')) {
          var keyName = 'Session Rate (sessions/sec)'
          var index = dataArr.findIndex( (item)=> {
            return item.name == keyName;
          });
          if (index == -1) {
            dataArr.push({ name: 'Session Rate (sessions/sec)', value: [[item.established, item.sessionRate]] });
          } else if (index != -1) {
            dataArr[index].value.push([item.established, item.sessionRate]);
          }
        }
      });
      //nbi回不足days就補[第一天時間,null]
      this._chartTimePatchService.patchStartTime(dataArr, this.days);
      // console.log(dataArr)
      // console.log(this.days)

      let data = []
      dataArr.forEach(item => {
        data.push({
          name: this.node,
          data: item.value,
          stack: 'total',
          type: 'line',
          // smooth: true,
          // sampling: 'lttb',
          showSymbol: false,
        })
      })
      this.chartData = data;
      this.loading = false;
    }).catch((err) => {
      console.error(err)
      this.isShowNoData = true
    }).finally(() => {
      this.isShowNoData = this.areAllDataArraysEmpty(this.chartData);
      this.cdr.detectChanges();
      this.blockUIStatus = false;
    });
  }

  emittedEvents(event: any): void {
    if (event === 'reload') {
      this.loading = true;
      this.getAnalysisSystemRes()
    }else if (event.type === 'changeDay') {
      this.changeFn(event.daysItem);
    }else if (event.type === 'changeMeasure') {
      this.changeMeasure(event.measureItem);
    }else if (event === 'download') {
      // this.triggerDownloadData()

      this._analysisSystemService.getAnalysisSystemRawData('cpeSessRate', this.days, 'sessionRate', this.node,'SystemStatistics-SessionRate').then(response => {
      }).catch(error => {
        console.error(error);
      });
    }else if (event === 'maximize') {
      this.triggerModal()
    }
  }


  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }

  isWriteAllowed() {
    return this._route.snapshot['_routerState'].url == '/dashboard'
  }

  ngOnInit(): void {
    this.getNodeCluster()
    if (this.isWriteAllowed()) {
      // 启动新的定时器
      timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
        this.getAnalysisSystemRes();
      });
    }
  }

  ngOnDestroy(): void {
    this._analysisSystemService.removeNodeListDataObservable();
  }


}
