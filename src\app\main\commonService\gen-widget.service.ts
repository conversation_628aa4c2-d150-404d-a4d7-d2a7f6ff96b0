import { Injectable } from '@angular/core';
import { BehaviorSubject, forkJoin, Observable, of, throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthenticationService } from 'app/auth/service';
import { GridSystemService } from './grid-system.service';
import cloneDeep from 'lodash/cloneDeep';
import { Widget } from 'app/layout/widget';
import { HttpClient } from '@angular/common/http';
import { dataCollectWidget } from 'app/main/devices/device-info/dataCollectWidgetUtils';
import { catchError } from 'rxjs/operators';
@Injectable({
  providedIn: 'root'
})
export class GenWidgetService {
  public renderWidgetsBak: any;
  public renderWidgetsOriginal: any;
  public onRenderingWidgetChanged: BehaviorSubject<any>;
  public dataCollect: any;
  constructor(
    private _gridService: GridSystemService,
    private authService: AuthenticationService,
    private _router: Router,
    private _httpClient: HttpClient
  ) {
    this.onRenderingWidgetChanged = new BehaviorSubject(null);
  }
  /**
   * Get final authority and widgets to be rendered
   * @param widgetUtils Widget utils from current page
   * @returns Final authority list and Widgets to be rendered
   */
  getRenderingWidgetList(widgetUtils, productName?) {
    this._gridService.productName = productName;
    const allRequest = forkJoin([this.getAuthoritySetting(), this.getPersonalTheme(), this.getDataCollect()
      .pipe(catchError(error => {
        // 处理404错误
        if (error.status === 404) {
          console.warn('Data collect not found, returning empty array.');
          return of([]); // 返回一个空数组
        }
        // 处理其他错误
        return throwError(error);
      }))
    ]).pipe(
      map(([authoritySetting, personalTheme, dataCollect]) => {
        dataCollect = dataCollect || [];
        this.dataCollect = dataCollect;

        // console.log('authoritySetting', authoritySetting);
        // console.log('personalTheme', cloneDeep(personalTheme));

        dataCollect.forEach(item => {
          if (item.dataCollect) {
            let name = item.name
            let labelName = item.labelName;
            let newWidget = cloneDeep(dataCollectWidget);
            newWidget.componentId = newWidget.componentId + '-' + labelName;
            newWidget.data.push({
              name: 'labelName',
              value: labelName
            }, {
              name: 'name',
              value: name
            })
            widgetUtils.push(newWidget);
          }
        });

        let finalAuthority = authoritySetting;
        let renderWidgets = widgetUtils;
        let personalThemeWidgets = widgetUtils;

        renderWidgets = this.buildRenderWidgets(personalThemeWidgets, finalAuthority);
        personalThemeWidgets = this.buildPersonalThemeWidgets(personalThemeWidgets, personalTheme, productName);
        // console.log('authWidgets:', authWidgets);
        this.renderWidgetsBak = renderWidgets;
        this.renderWidgetsOriginal = null;
        let filter = this.filterWidget(renderWidgets);
        this.onRenderingWidgetChanged.next(filter);
        return filter;
      })
    );
    return allRequest
  }

  public get renderWidgets(): any {
    var result = [];
    this.renderWidgetsBak.forEach(widget => {
      result.push({
        componentId: widget.componentId,
        name: widget.name,
        description: widget.description,
        hidden: widget.hidden,
        render: widget.render,
        class: widget.class,
        subClass: widget.subClass,
        copy: widget.copy,
        copyed: widget.copyed,
        toggled: widget.toggled,
        data: widget.data,
        src: widget.src,
        dmsType: widget.dmsType,
        extension: widget.extension
      })
    });
    return result;
  }

  notSave() {
    return this._gridService.notSave()
  }

  /**
   * Change grid configuration to editable if Edit Mode is toggled to true
   */
  editMode() {
    this.renderWidgetsOriginal = cloneDeep(this.renderWidgetsBak);
    this._gridService.editMode();
  };

  /**
   * Change gird configuration to uneditable if Edit Mode is toggled to false
   */
  unEditMode() {
    this.renderWidgetsOriginal = null;
    this._gridService.unEditMode();
  };

  resetChange() {
    this._gridService.resetChange();
    for (let i = 0; i < this.renderWidgetsBak.length;) {
      let widget = this.renderWidgetsBak[i];
      let original = this.renderWidgetsOriginal.find(item => widget.componentId === item.componentId);
      if (original) {
        for (const key in widget) {
          widget[key] = original[key];
        }
        i++;
      } else {
        this.renderWidgetsBak.splice(i, 1);
      }
    }
    this.renderWidgetsBak = this.renderWidgetsBak.filter(item => !item.copyed || !item.hidden);
    this.onRenderingWidgetChanged.next(this.filterWidget(this.renderWidgetsBak))
    this.unEditMode();
  }

  save() {
    return this._gridService.savePersonalTheme()
  }

  updateStatus(res, widgets) {
    var changed = [];
    res.forEach(item => {
      var widget = widgets.find(widget => widget.componentId === item.componentId);;
      if (widget) {
        widget.hidden = item.hidden;
        if (item.rows) {
          widget.rows = item.rows
        }
        if (item.cols) {
          widget.cols = item.cols
        }
        if (item.x) {
          widget.x = item.x
        }
        if (item.y) {
          widget.y = item.y
        }
        if (item.data) {
          widget.data = item.data
        }
        changed.push({
          componentId: widget.componentId,
          name: widget.name,
          cols: widget.cols,
          rows: widget.rows,
          x: widget.x,
          y: widget.y,
          copy: widget.copy,
          copyed: widget.copyed,
          hidden: widget.hidden,
          data: widget.data,
          color: widget.color
        });
      }
    });
    return changed;
  }

  widgetChangedFromPage(res) {
    if (res.componentId && this.renderWidgetsBak) {
      let obj = this.renderWidgetsBak.filter(item => item.componentId === res.componentId)[0]
      obj = Object.assign({}, { ...obj, rows: res.rows, data: [{ pageSize: res.data[0].pageSize }] })
      let changed = this.updateStatus([obj], this.renderWidgetsBak);
      // this.onRenderingWidgetChanged.next([])
      // if (changed.length > 0) {
      this._gridService.saveComponentStatus(changed);
      this._gridService.savePersonalTheme()
      // .finally(()=>{
      //   this.onRenderingWidgetChanged.next(this.filterWidget(this.renderWidgetsBak))
      // })
      // }
    }
  }

  refreshUI() {
    this.onRenderingWidgetChanged.next([])
    setTimeout(() => {
      this.onRenderingWidgetChanged.next(this.filterWidget(this.renderWidgetsBak))
    }, 50);
  }

  widgetChanged(res, flag = false) {
    if (res && (flag || this.renderWidgetsOriginal) && this.renderWidgetsBak) {
      let changed = this.updateStatus(res, this.renderWidgetsBak);
      this.onRenderingWidgetChanged.next(this.filterWidget(this.renderWidgetsBak))
      if (changed.length > 0) {
        this._gridService.saveComponentStatus(changed);
      }
    }
  }

  addDataCollectWidget(item) {
    if (item.dataCollect && this.renderWidgetsOriginal && this.renderWidgetsBak) {
      let labelName = item.labelName;
      let componentId = dataCollectWidget.componentId + '-' + labelName;
      let dcWidget = this.renderWidgetsBak.find(widget => widget.componentId === componentId);
      if (dcWidget) {
        dcWidget.hidden = false;
        this.onRenderingWidgetChanged.next(this.filterWidget(this.renderWidgetsBak))
        let changed = [{
          componentId: dcWidget.componentId,
          name: dcWidget.name,
          cols: dcWidget.cols,
          rows: dcWidget.rows,
          x: dcWidget.x,
          y: dcWidget.y,
          hidden: dcWidget.hidden,
          data: dcWidget.data,
          color: dcWidget.color
        }]
        this._gridService.saveComponentStatus(changed);
      }
    }
  }

  addNewWidget(res) {
    let { widget, originalId } = res;
    let newItem;
    let newIndex = 0;
    this.renderWidgetsBak.forEach((item: Widget, index: number) => {
      if (item.componentId === originalId) {
        newItem = cloneDeep(item);
        newItem.hidden = false;
        newItem.render = true;
        newItem.copyed = true;
        newIndex = Math.max(index + 1, newIndex);
      } else if (item.componentId.startsWith(originalId)) {
        newIndex = Math.max(index + 1, newIndex);
      }
    })
    newItem.componentId = widget.componentId;
    this.renderWidgetsBak.splice(newIndex, 0, newItem);
    this.onRenderingWidgetChanged.next(this.filterWidget(this.renderWidgetsBak))
  }

  filterWidget(widgets) {
    return widgets.filter(item => item.render === true);
  }

  removeObservers() {
    // if (this.onRenderingWidgetChanged.observers.length > 0) {
    //   this.onRenderingWidgetChanged.observers.forEach(item => item.complete())
    // }
    // this.onRenderingWidgetChanged.next([]);
    this.onRenderingWidgetChanged.unsubscribe();
    this.onRenderingWidgetChanged = new BehaviorSubject([]);
  }

  getAuthoritySetting() {
    return of(this.authService.currAuthority);
  };

  getPersonalTheme(): Observable<any> {
    return of(this._gridService.tempPersonalTheme || []);
  };

  getDataCollect(): Observable<any> {
    const productName = this._gridService.mProductName;
    if (this.isDeviceInfo() && this.authService.check('device', 'productAdmin', 'read')) {
      return this._httpClient.get(`nbi/device/productAdmin/${productName}/collectItem`)
    } else {
      return of([])
    }
  }

  isDeviceInfo() {
    const productName = this._gridService.mProductName;
    return productName && `/devices/${productName}/device-info` === this.getPersonalThemeUrl(productName)
  }

  /**
   * Compare with widgetUtils and Compared Authority List:
   * If both of them have the same class and subClass and, set the widget's render value to Compared Authority's read value.
   * If not, keep the widgetUtils' render value. 
   */
  buildRenderWidgets(authWidgets, finalAuthority) {
    if (finalAuthority) {
      authWidgets.forEach((aw) => {
        if (aw.class === 'common') {
          aw.render = true
        } else if (finalAuthority[aw.class] && finalAuthority[aw.class][aw.subClass]) {
          aw.render = finalAuthority[aw.class][aw.subClass]['read'];
        }
      });
    }
    // console.log('authWidgets:', authWidgets)
    return authWidgets
  };

  getPersonalThemeUrl(productName) {
    let url = this._router.url;
    if (productName && /\/[a-z0-9]{24}\//.test(url)) {
      return url.replace(/\/[a-z0-9]{24}\//, `/${productName}/`);
    } else if (url === '/care' && productName) {
      return url + '/' + productName
    } else if (url.includes('/user-manual')) {
      return '/user-manual'
    }
    return url;
  }

  /**
   * Compare with authWidgets and personalTheme, 
   * @param renderWidgets 
   * @param authWidgets 
   * @param personalTheme 
   * @returns rederWidgets, which will be rendered on the page
   */
  buildPersonalThemeWidgets(renderWidgets, personalTheme, productName) {
    const currentUrl = this.getPersonalThemeUrl(productName);
    //todo:Find if current url in personal theme
    const samePageUrl = personalTheme.find(pt => pt.pageUrl == currentUrl);
    //todo:If found it, find same component id compared with authWidgets(renderWidgets). If not found it, render widgets is equal to auth widgets.
    if (samePageUrl && samePageUrl.widgets && samePageUrl.widgets.length) {
      const widgetPreference = samePageUrl.widgets;
      // console.log('widgetPreference:', widgetPreference);
      renderWidgets.forEach(rw => {
        const sameComponentName = widgetPreference.find(wp => (rw.componentId == wp.componentId));
        //todo:If found it, replace widget preference from personal theme to render widgets
        if (sameComponentName) {
          // rw.name = sameComponentName.name
          rw.cols = sameComponentName.cols;
          rw.rows = sameComponentName.rows;
          rw.x = sameComponentName.x;
          rw.y = sameComponentName.y;
          rw.copy = sameComponentName.copy;
          rw.copyed = sameComponentName.copyed;
          rw.hidden = sameComponentName.hidden;
          rw.data = sameComponentName.data;
          rw.color = sameComponentName.color;
        } else {
          rw.hidden = true
        }
        // console.log('sameComponentId:', sameComponentId)
      })
    } else {
      this._gridService.saveTempPersonalTheme(currentUrl, renderWidgets)
    }
    // console.log('renderWidgets:', renderWidgets)
    return renderWidgets
  }
}
