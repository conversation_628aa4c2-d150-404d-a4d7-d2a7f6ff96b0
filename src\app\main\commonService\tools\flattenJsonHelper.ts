import { Injectable } from "@angular/core";

export interface JsonNode {
  id?: number;
  key: string;
  value: any;
  level: number;
  type: 'boolean' | 'string' | 'number' | 'object' | 'array' | 'primitive';
  parent?: JsonNode;
}

@Injectable({ providedIn: 'root' })
export class flattenJsonHelper {
  constructor() { }

  flattenJson(
    json: any,
    level = 0,
    parentNode: JsonNode | null = null
  ): JsonNode[] {
    const nodes: JsonNode[] = [];
    if (typeof json === 'object' && json !== null) {
      const isArray = Array.isArray(json);
      const keys: string[] | number[] = isArray ? json.map((_, index) => index) : Object.keys(json);
      const parentNode: JsonNode = {
        key: isArray ? 'Array' : 'Object',
        value: json,
        level,
        type: isArray ? 'array' : 'object'
      };
      keys.forEach((key) => {
        const value = json[key];
        const currentNode: JsonNode = {
          key: isArray ? `[${key}]` : key,
          value,
          level: level + 1,
          type: this.getNodeType(value),
          parent: parentNode
        };
        if (this.isComplexType(value)) {
          nodes.push(currentNode);
          nodes.push(
            ...this.flattenJson(value, level + 2, currentNode)
          );
        } else {
          nodes.push(currentNode);
        }
      });
    } else {
      nodes.push({
        key: parentNode ? parentNode.key : 'Root',
        value: json,
        level,
        type: 'primitive'
      });
    }
    return nodes;
  }

  formatNodes(
    json: any,
    level = 0,
    parentNode: JsonNode | null = null
  ): JsonNode[] {
    const nodes = this.flattenJson(json, level, parentNode);
    nodes.forEach((node,index) => {
      node.id = index;
      node.value = this.renderJsonNode(node);
    });
    return nodes;
  }

  getNodeType(value: any): JsonNode['type'] {
    if (typeof value === 'string') return 'string';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (value === null) return 'primitive';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    return 'primitive';
  }

  isComplexType(value: any): boolean {
    return (
      value !== null &&
      (typeof value === 'object' || Array.isArray(value))
    );
  }

  renderJsonNode(node: JsonNode): string {
    if (['string', 'number', 'boolean', 'primitive'].includes(node.type)) {
      return `${this.formatValue(node.value)}`;
    }
    if (node.type === 'array') {
      return `Array(${node.value.length})`;
    }
    return '';
  }

  formatValue(value: any): string {
    if (value === null) return 'null';
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'function') return 'function()';
    return String(value);
  }

}