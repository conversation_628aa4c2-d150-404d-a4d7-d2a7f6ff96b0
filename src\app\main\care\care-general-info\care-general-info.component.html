<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)"
  >
  <h4 class="card-title">
    {{ accessor.name | translate}}
  </h4>
  <div class="card card-transaction overflow-hidden">
    <div
      class="mh-1px d-flex flex-wrap align-content-start card-body pt-0"
      [perfectScrollbar]>

      <div *ngFor="let item of generalInfoItems" class="transaction-item align-items-start overflow-hidden px-25"
        [ngStyle]="{'width': getWidth()}">
        <div class="w-100">
          <div class="media-body">
            <h6 class="transaction-title" style="font-size: 12px;">
              {{careInfoNameMapping[item.name]}}
            </h6>
            <small class="text-info" *ngIf="checkInvalidDate(item.reportedValue)">
              {{item.reportedValue | date:'MM/dd/yy, HH:mm:ss'}}
            </small>
            <small *ngIf="!checkInvalidDate(item.reportedValue) && item.name =='MAC'">
              <app-beautify-content [content]="item.reportedValue" [placement]="'bottom'"
                [textClass]="item.reportedValue !== 'N/A' ? 'text-info ml-0 small-font-400 text-uppercase' : 'text-secondary ml-0 small-font-400 text-uppercase'"></app-beautify-content>
            </small>
            <small
              *ngIf="!checkInvalidDate(item.reportedValue) && item.name !='tags'  && item.name !='MAC'">
              <app-beautify-content [content]="item.reportedValue" [placement]="'bottom'"
                [textClass]="item.reportedValue !== 'N/A' ? 'text-info ml-0 small-font-400' : 'text-secondary ml-0 small-font-400'"></app-beautify-content>
            </small>
          </div>
        </div>
      </div>

      <!-- <div
        *ngFor="let item of generalInfoItems"
        class="transaction-item align-items-start ">
        <div class="media">
          <div class="media-body">
            <div [ngSwitch]="item.name">
              <div>
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{careInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info" *ngIf="checkInvalidDate(item.reportedValue)">
                  {{item.reportedValue | date:'MM/dd/yy, HH:mm:ss'}}</small>
                <small class="text-info" *ngIf="!checkInvalidDate(item.reportedValue) && item.name =='MAC'">
                  <app-beautify-content
                    [content]="item.reportedValue"
                    [placement]="'left'"
                    [textClass]="'text-info ml-0 small-font-400 text-uppercase'">
                  </app-beautify-content>
                </small>
                <small class="text-info" *ngIf="!checkInvalidDate(item.reportedValue) && item.name !='tags' && item.name !='label' && item.name !='MAC'">
                  <app-beautify-content
                    [content]="item.reportedValue"
                    [placement]="'left'"
                    [textClass]="'text-info ml-0 small-font-400'">
                  </app-beautify-content>
                </small>
              </div>
            </div>
          </div>
        </div>
      </div> -->

      
    </div>
  </div>
</core-card>