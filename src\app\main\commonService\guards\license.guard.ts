import { CanMatchFn, UrlTree, Router } from '@angular/router';
import { inject } from '@angular/core';
import { UserService } from 'app/auth/service/user.service';
import { map, catchError } from "rxjs/operators";
import { of } from 'rxjs';

export const licenseGuard: CanMatchFn = (route, segments) => {
  const userService = inject(UserService);
  const router = inject(Router);

  // 從 route.data 中取得檢查 License 的參數
  const licenseKey = route.data?.licenseKey;

  return userService.checkLicenceKey(licenseKey).pipe(
    map((isValid: boolean) => {
      if (isValid) {
        return true; // License 有效，允許加載模組
      } else {
        return router.parseUrl('/system/license'); // License 無效，重導向到未授權頁面
      }
    }),
    catchError(() => {
      // 如果發生錯誤，也重導向到未授權頁面
      return of(router.parseUrl('/system/license'));
    })
  );
};