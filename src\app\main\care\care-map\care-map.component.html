<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <!-- warning message -->
  <div *ngIf="ismapErr" class="map-overlay-warning">
    <div class="badge badge-warning" style="white-space: normal;font-size: 14px;">
      <i [data-feather]="'alert-triangle'"></i>
      {{mapType == '0' ? GOOGLEMAPERROR : OSMERROR}}
    </div>
  </div>
  <div
    *ngIf="mapType =='0' && (apiLoaded | async)"
    class="card-body h-100 w-100"
    style="cursor:default;">
    <div
        style="position: absolute;z-index: 100;height: 50px;width: 250px;"
        class="d-flex align-items-center">
        <!-- edit toggle -->
        <!-- <div
            class="custom-control custom-switch custom-switch-success"
            style="margin-left: 5px;"
            ngbTooltip="Edit Location"
            placement="bottom"
            container="body">
            <input
                type="checkbox"
                class="custom-control-input"
                id="customSwitch10"
                (change)="editLocation($event)"
                [checked]="editBtn">
            <label
                class="custom-control-label"
                for="customSwitch10">
                <span class="switch-icon-left">
                <i data-feather="edit"></i>
                </span>
                <span class="switch-icon-right">
                <i data-feather="edit"></i>
                </span>
            </label>
        </div> -->
        <!-- Input Lat,Lng -->
        <input
        *ngIf="updateBtn"
        type="text"
        class="form-control form-control-sm"
        style="margin-right: 5px;width:55px;padding: 2px 5px;"
        (change)="inputLatLngChange(lat,null)"
        [(ngModel)]="lat"
        placeholder="Input Lat">
        <input
        *ngIf="updateBtn"
        type="text"
        class="form-control form-control-sm"
        style="margin-right: 5px;width:55px;padding: 2px 5px;"
        (change)="inputLatLngChange(null,lng)"
        [(ngModel)]="lng"
        placeholder="Input Lng">
        <!-- reset btn -->
        <button
        *ngIf="updateBtn"
        [disabled]="updateBtnDisabled"
        type="button"
        class="btn btn-primary btn-sm"
        style="margin-right: 5px;padding: 0.486rem 10px;"
        placement="bottom"
        ngbTooltip="{{ 'DEVICES.RESET' | translate }}"
        rippleEffect
        (click)="resetLocation()">
        <span [data-feather]="'rotate-ccw'"></span>
        </button>
        <!-- Update btn -->
        <button
        *ngIf="updateBtn"
        [disabled]="updateBtnDisabled"
        type="button"
        class="btn btn-primary btn-sm"
        style="margin-right: 5px;padding: 0.486rem 10px;"
        placement="bottom"
        ngbTooltip="{{ 'COMMON.UPDATE' | translate }}"
        rippleEffect
        (click)="saveDragendLocation(lat,lng)">
        <span [data-feather]="'save'"></span>
        </button>
    </div>
    <!-- Google Maps Marker with Tooltip Component -->
    <google-map
        [zoom]="markerZoom"
        [options]="mapOptions"
        height="100%"
        width="100%"
        [center]="LocationCenter">
        <!-- [title]="marker.title" -->
        <map-marker
        #markerElem="mapMarker"
        *ngFor="let marker of markers"
        [position]="marker.position"
        [options]="marker.options"
        (mapClick)="openInfo(markerElem)"
        (mapDragend)="mapDragend($event)">
        </map-marker>
        <map-info-window
        *ngFor="let marker of markers"
        [position]="marker.position">
        <div style="color: black;">
            <p style="font-weight:bold;">S/N : {{serialNumber}}</p>
            <p style="font-weight:bold;">IP : {{ip | formatIp}}</p>
            <p style="font-weight:bold;">Product : {{productName}}</p>
            <p style="font-weight:bold;">Status : {{status}}</p>
            <p style="font-weight:bold;">Latitude : {{lat}}</p>
            <p style="font-weight:bold;">Longitude : {{lng}}</p>
        </div>
        </map-info-window>
    </google-map>
    <!-- / Google Maps Marker with Tooltip Component -->
    </div>
  <div
    *ngIf="mapType =='1'"
    class="card-body h-100 w-100"
    style="cursor:default;">
    
    <div
      id='map'
      #map
      style="height: 100%;width: 100%;margin: 0;padding: 0;"></div>
  </div>
</core-card>
