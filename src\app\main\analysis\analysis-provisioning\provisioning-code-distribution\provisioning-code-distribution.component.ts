import { Component, Input,ChangeDetectorRef } from '@angular/core';
import { AnalysisProvisioningService } from 'app/main/analysis/analysis-provisioning/analysis-provisioning.service';
import { timer } from 'rxjs';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-provisioning-code-distribution',
  templateUrl: './provisioning-code-distribution.component.html',
  styleUrls: ['./provisioning-code-distribution.component.scss']
})
export class ProvisioningCodeDistributionComponent extends Unsubscribe {
  @Input() accessor: any;
  public blockUIStatus = false;
  public personalTheme;
  public isShowNoData = true

  constructor(
    private _analysisProvisioningService: AnalysisProvisioningService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
  ) {
    super();
    this.customSubscribe(route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  public chartData = []

  public componentId = "Analysis/" + this.constructor.name;
  public productNameSelect: any
  public selectedProductName = 'All'

  changeFn(selected) {
    this.selectedProductName = selected.target.value
    // console.log(this.selectedProductName);
    this._analysisProvisioningService.saveSelectedProductName(this.componentId, { productName: selected.target.value });
    this.reportAnalysisProvisioningRes(this.selectedProductName)
  }

  /**
    * get AnalysisProvisioning product name
    */
  getAnalysisProvisioningProductName() {
    this.customSubscribe(this._analysisProvisioningService.onAvailStatisticsProductNamesChanged, res=> {
      if (res === null) {
        this._analysisProvisioningService.getAnalysisProvisioningProductName().then()
      } else {
        this.productNameSelect = res;
      }
    })
  }
  /**
    * report AnalysisProvisioning
    */
  reportAnalysisProvisioningRes(productName) {
    this.blockUIStatus = true;
    this.isShowNoData = false;
    this._analysisProvisioningService.reportAnalysisProvisioning(productName, 'provisioningCode').then((res) => {
      // console.log(res)
      this.parsePieChartDistributionData(res, "Provisioning Code Distribution");
      this.isShowNoData = this.chartData.length > 0 ? false : true
      this.cdr.detectChanges();
    }).catch((err) => {
      console.error(err)

        this.chartData=[];
        this.isShowNoData = true

    }).finally(() => {
      this.blockUIStatus = false;
    });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.reportAnalysisProvisioningRes(this.selectedProductName)
        break;
      case 'download':
        this.triggerDownloadData()
        break;
      case 'maximize':
        this.triggerModal()
        break;
    }
  };
  public downloadData = false;
  triggerDownloadData() {
    // console.log('this.downloadData',this.downloadData)
    if (this.downloadData) {
      this.downloadData = false;
      setTimeout(() => this.downloadData = true, 0);
    } else {
      this.downloadData = true;
    }
  }
  public openModalFlag = false;
  triggerModal() {
    // console.log('this.openModalFlag',this.openModalFlag)
    if (this.openModalFlag) {
      this.openModalFlag = false;
     setTimeout(() => this.openModalFlag = true, 0);
    } else {
      this.openModalFlag = true;
    }
  }


  parsePieChartDistributionData = function (data, title) {
    var seriesData = [];
    data["Provisioning Code"].forEach(item => {
      seriesData.push({ name: item.provisioningCode || (item.provisioningCode === null ? 'N/A' : '-'), value: item.count });
    });
    // 按照 value 的大小進行降序排序
    seriesData.sort((a, b) => b.value - a.value);
    this.chartData = seriesData
    // console.log(seriesData)
  }

  ngOnInit(): void {
    this.componentId = this.accessor?.componentId;
    let data = this._analysisProvisioningService.getSelectedProductName(this.componentId);
    this.selectedProductName = data && data.productName || 'All';
    this.getAnalysisProvisioningProductName()
    this.reportAnalysisProvisioningRes(this.selectedProductName)
    this.customSubscribe(timer(600000, 600000), () => {
      this.reportAnalysisProvisioningRes(this.selectedProductName)
    });
  }

}
