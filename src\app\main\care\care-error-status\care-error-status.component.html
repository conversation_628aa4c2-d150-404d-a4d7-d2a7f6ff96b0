<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)"
  >
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <ngx-datatable
    #tableRowDetails
    appDatatableRecalculate
    [datatable]="tableRowDetails"
    [rows]="errorStatusItems"
    [rowHeight]="37"
    class="bootstrap core-bootstrap"
    [limit]="selectedOption"
    [columnMode]="ColumnMode.force"
    [headerHeight]="45"
    [footerHeight]="50"
    [scrollbarH]="true"
    [scrollbarV]="true">
    <!-- ErrorCode -->
    <ngx-datatable-column
      [sortable]="false"
      name="{{ 'DEVICES.ERRORCODE' | translate }}"
      prop="ErrorCode">
      <ng-template
        let-row="row"
        let-ErrorCode="value"
        ngx-datatable-cell-template>
        <div>{{ErrorCode}}</div>
      </ng-template>
    </ngx-datatable-column>
    <!-- ErrorDescript -->
    <ngx-datatable-column
      [sortable]="false"
      name="{{ 'DEVICES.ERRORDESCRIPT' | translate }}"
      prop="ErrorDescript">
      <ng-template
        let-row="row"
        let-ErrorDescript="value"
        ngx-datatable-cell-template>
        <app-beautify-content
          [content]="ErrorDescript"></app-beautify-content>
      </ng-template>
    </ngx-datatable-column>
    <!-- ErrorTime -->
    <ngx-datatable-column
      [sortable]="false"
      name="{{ 'DEVICES.ERRORTIME' | translate }}"
      prop="ErrorTime">
      <ng-template
        let-row="row"
        let-ErrorTime="value"
        ngx-datatable-cell-template>
        <div>{{ErrorTime | date:'MM/dd/yy, HH:mm:ss'}}</div>
      </ng-template>
    </ngx-datatable-column>
  </ngx-datatable>
</core-card>
