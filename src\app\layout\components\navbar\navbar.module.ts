import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DatePipe } from '@angular/common';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import {
  PerfectScrollbarConfigInterface,
  PerfectScrollbarModule,
  PERFECT_SCROLLBAR_CONFIG
} from 'ngx-perfect-scrollbar-portable';
import { NgSelectModule } from '@ng-select/ng-select';

import { CoreCommonModule } from '@core/common.module';
import { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';

import { NavbarComponent } from 'app/layout/components/navbar/navbar.component';
import { NavbarBookmarkComponent } from 'app/layout/components/navbar/navbar-bookmark/navbar-bookmark.component';
import { NavbarSearchComponent } from 'app/layout/components/navbar/navbar-search/navbar-search.component';

import { NavbarNotificationComponent } from 'app/layout/components/navbar/navbar-notification/navbar-notification.component';
import { NavbarSummaryComponent } from './navbar-summary/navbar-summary.component';
import { NavbarPersonalThemeComponent } from './navbar-personal-theme/navbar-personal-theme.component';
import { ToastrModule } from 'ngx-toastr';
import { TranslateModule } from '@ngx-translate/core';
// import { UsersModule } from 'app/main/users/users.module';
import { SharedModule } from 'app/main/shared/shared.module';
import { NavbarNewwidigetComponent } from './navbar-newwidiget/navbar-newwidiget.component';
import { NavbarChangePasswordComponent } from './navbar-change-password/navbar-change-password.component';
import { NavbarRebootNodeComponent } from './navbar-reboot-node/navbar-reboot-node.component';
import { NavbarShutdownNodeComponent } from './navbar-shutdown-node/navbar-shutdown-node.component';

const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
  suppressScrollX: true,
  wheelPropagation: true
};

@NgModule({
  declarations: [NavbarComponent, NavbarSearchComponent, NavbarBookmarkComponent, NavbarNotificationComponent, NavbarSummaryComponent, NavbarPersonalThemeComponent, NavbarNewwidigetComponent, NavbarChangePasswordComponent, NavbarRebootNodeComponent, NavbarShutdownNodeComponent],
  imports: [RouterModule, NgbModule, CoreCommonModule, PerfectScrollbarModule, CoreTouchspinModule, NgSelectModule, ToastrModule, TranslateModule, SharedModule],
  providers: [
    {
      provide: PERFECT_SCROLLBAR_CONFIG,
      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG,
    },
    DatePipe
  ],
  exports: [NavbarComponent]
})
export class NavbarModule { }
