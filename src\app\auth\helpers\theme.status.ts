import { Component, inject, Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanDeactivateFn, Router, RouterStateSnapshot } from "@angular/router";
import { CoreConfigService } from "@core/services/config.service";
import { TranslateService } from "@ngx-translate/core";
import { GridSystemService } from "app/main/commonService/grid-system.service";
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import Swal from "sweetalert2";

@Injectable({ providedIn: 'root' })
class ThemeStatusService {
    constructor(
        private _router: Router,
        private _gridSystemService: GridSystemService,
        private _translateService: TranslateService,
        private _coreConfigService: CoreConfigService,
        private _genWidgetService: GenWidgetService,
        private _toastrUtilsService: ToastrUtilsService,
    ) { }

    canDeactivate(component: Component, currentRoute: ActivatedRouteSnapshot, currentState: RouterStateSnapshot, nextState?: RouterStateSnapshot) {
        if (this._gridSystemService.notSave()) {
            Swal.fire({
                title: this._translateService.instant('CONFIRM.NOTSAVETITLE'),
                text: this._translateService.instant('CONFIRM.CHANGEWILLBELOSE'),
                icon: 'warning',
                showCancelButton: true,
                showCloseButton: true,
                confirmButtonText: this._translateService.instant('COMMON.SAVE'),
                cancelButtonText: this._translateService.instant('COMMON.DONTSAVE'),
                customClass: {
                    confirmButton: 'btn btn-primary',
                    cancelButton: 'btn btn-danger ml-1',
                }
            }).then((result) => {
                if (result.value) {
                    this._genWidgetService.save().then(() => {
                        this._coreConfigService.setConfig({ layout: { editMode: false } });
                        this._genWidgetService.unEditMode();
                        this._toastrUtilsService.showSuccessMessage(``, this._translateService.instant('DEVICES.ACTION.SAVESuccess'));
                        this._router.navigate([nextState.url]);
                        return result.value
                    });
                } else {
                    this._genWidgetService.resetChange();
                    this._coreConfigService.setConfig({ layout: { editMode: false } });
                    this._gridSystemService.unEditMode();
                    this._router.navigate([nextState.url]);
                }
            });
            return false;
        } else {
            if (this._gridSystemService.isEditMode) {
                this._coreConfigService.setConfig({ layout: { editMode: false } });
                this._gridSystemService.unEditMode();
            }
            if (nextState.url === '/pages/authentication/login-v2'){
                const currentPathname = window.location.pathname;
                window.location.href = currentPathname;
            }
            return true;
        }
    }
}

export const ThemeStatus: CanDeactivateFn<Component> = (component: Component, currentRoute: ActivatedRouteSnapshot, currentState: RouterStateSnapshot, nextState?: RouterStateSnapshot): boolean => {
    return inject(ThemeStatusService).canDeactivate(component, currentRoute, currentState, nextState);
}