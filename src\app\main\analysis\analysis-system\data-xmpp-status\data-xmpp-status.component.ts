import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input } from '@angular/core';
import { AnalysisSystemService } from '../analysis-system.service'
import moment from 'moment'
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import{CalculatorWidthService} from 'app/main/commonService/calculator-width.service'

@UntilDestroy()
@Component({
  selector: 'app-data-xmpp-status',
  templateUrl: './data-xmpp-status.component.html',
  styleUrls: ['./data-xmpp-status.component.scss']
})
export class DataXmppStatusComponent extends Unsubscribe implements OnInit {
  @Input() accessor: any;
  public blockUIStatus = this._analysisSystemService.serverStatus;
  public xmppInfoItems: any = []
  public xmppSupported: any = false;
  inservice:boolean
  public componentWidth: number = 0;
  constructor(
    private _analysisSystemService: AnalysisSystemService,
    private _gridSystemService: GridSystemService,
    private _calculatorWidthService: CalculatorWidthService,
  ) {
    super();
  }

  public xmppInfoNameMapping = {
    'onlineusers': 'Online Devices',
    'outgoing_s2s_number': 'Outgoing s2s number',
    'incoming_s2s_number': 'Incoming s2s number',
    'uptimeseconds': 'Uptime',
    'registeredusers': 'Registered Devices'
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getXMPPInfoRes();
        break;
    }
  };

  getXMPPInfoRes() {
    this._analysisSystemService.onServerStatusChanged.pipe(untilDestroyed(this)).subscribe(res => {
      if (!res["xmpp"]) {
        this.xmppInfoItems = Object.keys(this.xmppInfoNameMapping).map(item => {
          return {
            "name": item, "reportedValue": ''
          }
        })
      } else {
        this.xmppInfoItems = []
        for (let i in res["xmpp"]) {
          this.xmppInfoItems.push({ "name": i, "reportedValue": res["xmpp"][i] })
        }
      }
      this.xmppSupported = res.hasOwnProperty("xmppSupported") ? res.xmppSupported : true
      if(this.xmppSupported==false){
        this.inservice=false
      }else{
        this.inservice=true
      }
      this._analysisSystemService.onBlockUIStatusChanged.next(false)
    })
  }

  ngOnInit(): void {
    this.getXMPPInfoRes();
  }

  getWidth() {
    return this._calculatorWidthService.calculateWidth(this.componentWidth, this.xmppInfoItems.length);
  }
  ngAfterViewInit(): void {
    this.customSubscribe(this._gridSystemService.onGridsterItemComponentInfoChanged, res => {
      this.componentWidth = res['Analysis/DataXmppStatusComponent'] ? res['Analysis/DataXmppStatusComponent']['width'] : 0
    })
  }

}
