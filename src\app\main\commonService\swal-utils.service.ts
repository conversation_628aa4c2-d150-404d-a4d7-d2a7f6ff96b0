import { Injectable } from '@angular/core';
import Swal from 'sweetalert2';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class SwalUtilsService {

  constructor(
    private translateService: TranslateService
  ) {}

  commonSwal(title: string, text: string, icon: any, confirme: () => void, width?: string, dismiss = () => {}) {
    Swal.fire({
      width: width,
      title: title,
      html: text,
      icon: icon,
      showCancelButton: true,
      confirmButtonColor: '#7367F0',
      cancelButtonColor: '#E42728',
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        confirme();
      } else if (result.isDismissed) {
        dismiss();
      }
    });
  }
}
