import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { Role } from 'app/auth/models';
import { EncryptUtilsService } from 'app/main/commonService/encrypt-utils.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { StorageService } from 'app/main/commonService/storage.service';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class AuthenticationService {
  //public
  public currentUser: Observable<any>;
  public currentUserAuthority = null;
  public reset = false;
  //private
  private currentUserSubject: BehaviorSubject<any>;
  private themeKey: any;

  private isManagerRole = false;
  private isAdminRole = false;
  private isCSRRole = false;

  /**
   *
   * @param {HttpClient} _http
   */
  constructor(
    private _http: HttpClient,
    private _encryptUtilsService: EncryptUtilsService,
    private _toastrUtilsService: ToastrUtilsService,
    private _gridSystemService: GridSystemService,
    private _storageService: StorageService,
    private location: Location,
    private _router: Router,
    private _route: ActivatedRoute
  ) {
    this.currentUserSubject = new BehaviorSubject<any>(this._storageService.get('currentUser'));
    this.currentUser = this.currentUserSubject.asObservable();
    this.currentUserAuthority = this.currentUserSubject?.value?.formatAuthority || {};
    this.isAdminRole = this.currentUserSubject?.value?.isAdmin
    this.isManagerRole = this.currentUserSubject?.value?.isManager
    this.isCSRRole = this.currentUserSubject?.value?.isCSR
  }

  // getter: currentUserValue
  public get currentUserValue(): any {
    return this.currentUserSubject.value;
  }

  public set currentUserValue(v) {
    this.currentUserSubject.next(v)
  }

  /**
   *  Confirms if user is manager
   */
  public get isManager() {
    return this.isManagerRole
  }

  /**
   *  Confirms if user is admin
   */
  public get isAdmin() {
    return this.isAdminRole
  }
  /**
   *  Confirms if user is csr
   */
  public get isCSR() {
    return this.isCSRRole
  }

  public set isManager(newRole) {
    this.isManagerRole = newRole;
  }

  public set isAdmin(newRole) {
    this.isAdminRole = newRole;
  }

  public set isCSR(newRole) {
    this.isCSRRole = newRole;
  }

  public set avatar(v: string) {
    this.currentUserSubject.value.avatar = v;
    this.currentUserSubject.next(this.currentUserSubject.value);
    this.saveLocalStorage()
  }

  public get avatar(): string {
    return this.currentUserSubject?.value?.avatar;
  }

  public set email(v: string) {
    this.currentUserSubject.value.email = v;
    this.currentUserSubject.next(this.currentUserSubject.value);
    this.saveLocalStorage()
  }

  public get email(): string {
    return this.currentUser && this.currentUserSubject.value.email;
  }

  public set mfaEnabled(v: any) {
    this.currentUserSubject.value.mfaEnabled = v;
    this.currentUserSubject.next(this.currentUserSubject.value);
    this.saveLocalStorage()
  }

  public get mfaEnabled(): any {
    return this.currentUser && this.currentUserSubject.value.mfaEnabled;
  }


  public get username(): string {
    return this.currentUserSubject?.value?.userName;
  }

  saveLocalStorage() {
    this._storageService.set('currentUser', this.currentUserSubject.value);
  }

  updateProductName(name: any) {
    if (this.currentUserSubject.value.productName[0] !== 'ALL') {
      this.currentUserSubject.value.productName.push(name);
      this.currentUserSubject.next(this.currentUserSubject.value);
    }
  }

  _removeProductName(name: any) {
    if (this.currentUserSubject.value.productName[0] !== 'ALL') {
      let i = this.currentUserSubject.value.productName.indexOf(name);
      if (i !== -1) {
        this.currentUserSubject.value.productName.splice(i, 1);
        return true;
      }
    }
    return false;
  }

  removeProductName(param: any) {
    if (typeof param === 'string') {
      if (this._removeProductName(param)) {
        this.currentUserSubject.next(this.currentUserSubject.value);
      }
    } else {
      let result = false;
      param.forEach(name => {
        result = this._removeProductName(name) || result;
      });
      if (result) {
        this.currentUserSubject.next(this.currentUserSubject.value);
      }
    }
  }

  checkUrl() {
    if (this.isCSR) {
      if (this.location.path() != '/care' && this.location.path() != '/pages/authentication/login-v2') {
        this._router.navigate(['/care'])
      }
    }
  }

  check(className: string, subClassName: string, type: string = 'read'): boolean {

    // console.log('className',className,subClassName)
    // console.log('his.currentUserAuthority',this.currentUserAuthority)
    return this.currentUserAuthority?.[className]?.[subClassName]?.[type];
  }

  public get currAuthority(): object {
    return this.currentUserAuthority;
  }

  formatAuthority(role, authorityList) {
    // format current user authority
    let result = {};
    authorityList.forEach(classItem => {
      let className = classItem.class;
      result[className] = {};
      classItem.subClassList.forEach(subClassItem => {
        let subClassName = subClassItem.subClass;
        result[className][subClassName] = { ...subClassItem }
      });
    });
    return result;
  }

  public set personalThemeKey(key) {
    this.themeKey = key
  }

  public get personalThemeKey(): string {
    return this.themeKey
  }

  /**
   * User login
   *
   * @param email
   * @param password
   * @returns user
   */
  login(username: string, password: string, newKey = '') {
    let date = new Date();
    if (newKey) {
      date = new Date(Number(newKey));
      this.reset = false;
    } else {
      this.reset = true;
    }
    this._gridSystemService.unEditMode();
    const key = date.toISOString();
    const passwd = this._encryptUtilsService.encryptPasswd(username, password, key);
    const token = this._encryptUtilsService.b64EncodeUnicode(username + ":" + passwd + ":" + date.getTime());
    return this._http
      .get<any>('nbi/user/accountAdmin/verify', { headers: { 'Authorization': "Basic " + token } })
      .pipe(
        map(user => {
          // login successful if there's a jwt token in the response
          if (user && user.token) {
            this._storageService.tokenSubject.next(user.token);
            this._storageService.setCookie('sessionId', user.sessionId);
            // user.formatRole = user.roles[0].replace('ROLE_', '');
            user.formatRole = user.authority;
            this.isAdminRole = user.formatRole === Role.Admin || user.formatRole === Role.Super;
            user.isAdmin = this.isAdminRole;
            this.isManagerRole = user.formatRole === Role.Manager;
            user.isManager = this.isManagerRole;
            this.isCSRRole = user.formatRole === Role.Csr;
            user.isCSR = this.isCSRRole;
            this.currentUserAuthority = this.formatAuthority(user.formatRole, user.authorityList);
            user.formatAuthority = this.currentUserAuthority;
            // notify
            this.currentUserSubject.next(user);
            // store user details and jwt token in local storage to keep user logged in between page refreshes
            this._storageService.set('currentUser', user);
            // setTimeout(() => {
            //   this._toastrUtilsService.showSuccessMessage('👋 Welcome !', 'You have successfully logged in to Askey DMS. Now you can start to explore. Enjoy! 🎉')
            // }, 2500);
          }

          return user;
        }),
        catchError(err => {
          if (err.status == 400) {
            if (this.reset) {
              return this.login(username, password, err.error);
            }
          }
          throw err;
        })
      );
  }

  _logout() {
    // remove user from local storage to log user out
    this.themeKey = undefined;
    this.isAdminRole = false;
    this.isManagerRole = false;
    this.currentUser = undefined;
    this.currentUserAuthority = null;
    this._storageService.clear();
    this._storageService.clearCookie();
    this._gridSystemService.clear();
    // notify
    this.currentUserSubject.next(null);
  }

  /**
   * User logout
   *
   */
  logout(flag = false) {
    if (flag) {
      return new Promise<void>((resolve, reject) => {
        if (this.currentUserSubject?.value) {
          this._http.get('nbi/logout', { responseType: "text" }).subscribe({
            next: () => {
              this._logout()
              resolve()
            },
            error: reject
          })
        }
      });
    } else {
      this._logout()
      return Promise.resolve()
    }
  }

  loginWithMFA(params) {
    return new Promise((resolve, reject) => {
      this._http.post('nbi/common/extraVerify', params).subscribe({
        next: (response: any) => {
          // console.log(response)
          resolve(response);
        },
        error: reject
      });
    });
  }

  changeEnableWithMFA(params) {
    return new Promise((resolve, reject) => {
      this._http.post('nbi/user/accountAdmin/enabledMFA', params).subscribe({
        next: (response: any) => {
          // console.log(response)
          resolve(response);
        },
        error: reject
      });
    });
  }

  getQRCode() {
    return new Promise((resolve, reject) => {
      this._http.get('nbi/common/getQRCode').subscribe({
        next: (response: any) => {
          // console.log(response)
          resolve(response);
        },
        error: reject
      });
    });
  }

  getSSORuning() {
    return new Promise((resolve, reject) => {
      this._http.get('api/v1/sso/running').subscribe({
        next: (response: any) => {
          // console.log(response)
          resolve(response);
        },
        error: reject
      });
    });
  }

  loginWithSAML() {
    return new Promise((resolve, reject) => {
      this._http.get('api/v1/sso/saml/login', { observe: "response" }).subscribe({
        next: (response: any) => {
          // console.log(response)
          resolve(response);
        },
        error: reject
      });
    });
  }

  getUserInfoWithSAMLRequest(token) {
    return new Promise((resolve, reject) => {
      this._http.get(`api/v1/sso/verify?token=${token}`).subscribe({
        next: (response: any) => {
          // console.log(response)
          resolve(response);
        },
        error: reject
      });
    });
  }

  loginWithOIDC() {
    return new Promise((resolve, reject) => {
      this._http.get('api/v1/sso/oidc/login', { observe: "response" }).subscribe({
        next: (response: any) => {
          // console.log(response)
          resolve(response);
        },
        error: reject
      });
    });
  }
}
