import { DeviceActionsService } from "app/main/devices/device-actions/device-actions.service";
import { ToastrUtilsService } from "../toastr-utils.service";
import { TranslateService } from "@ngx-translate/core";
import { DeviceInfoService } from "app/main/devices/device-info/device-info.service";
import { Injectable } from "@angular/core";

@Injectable({ providedIn: 'root' })
export class CRQHelper {
    constructor(
        private _deviceActionService: DeviceActionsService,
        private _toastrUtilsService: ToastrUtilsService,
        private translateService: TranslateService,
        private _deviceInfoService: DeviceInfoService
    ) { }


    setData(deviceId, params, done, callback, errorCallback) {
        let promise;
        if (Array.isArray(params)) {
            promise = Promise.all(params.map(item => this._deviceInfoService.setDeviceData(deviceId, item)))
        } else {
            promise = this._deviceInfoService.setDeviceData(deviceId, params)
        }
        promise.then((res: any) => {
            if (done) {
                let SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
                let UPDATEDEV = this.translateService.instant('CONFIRM.UPDATEDEV');
                this._toastrUtilsService.showSuccessMessage(SUCCESS, UPDATEDEV);
            } else {
                callback()
            }
        }, (err) => {
            let title = this.translateService.instant('CONFIRM.BADREQ');
            let msg = this.translateService.instant('CONFIRM.SETOPERFAIL');
            if (err && err.status == 409) {
                title = this.translateService.instant('CONFIRM.CONFLICT');
            }
            this._toastrUtilsService.showErrorMessage(title, msg);
            errorCallback()
        })
    }

    connectRequest(deviceId, serialNumber, callback) {
        let assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
        let assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
        let assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
        let assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
        let assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
        this._deviceActionService.connectionRequest(deviceId).then((res) => {
            if (String(res) == 'true') {
                this._toastrUtilsService.showInfoMessage(assignOperationSucc, `${serialNumber}  ${assignSuccessMsg}`)
            } else {
                this._toastrUtilsService.showWarningMessage(assignOperation, `${assignWarningMsg1} ${serialNumber} ${assignWarningMsg2}`);
            }
        }).catch((res) => {
            this._toastrUtilsService.showWarningMessage(assignOperation, `${assignWarningMsg1} ${serialNumber} ${assignWarningMsg2}`);
        }).finally(() => {
            callback()
        });
    }
}