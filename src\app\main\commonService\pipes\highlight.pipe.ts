import { Pipe, PipeTransform } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { HighlightManagerService } from '../highlight-manager.service';

@Pipe({
  name: 'highlight'
})
export class HighlightPipe implements PipeTransform {

  constructor(
    private sanitizer: DomSanitizer,
    private highlightManager: HighlightManagerService
  ) { }

  transform(node, searchQuery: string, qid: string = '', outerIndex: number = -1, virtualScrollIndex: number = -1): SafeHtml {
    if (!node) {
      return node;
    }
    node = this.applyHighlight(node, searchQuery, qid, outerIndex, virtualScrollIndex);
    return this.sanitizer.bypassSecurityTrustHtml(node);
  }

  private applyHighlight(content: string, searchQuery: string, qid: string = '', outerIndex: number = -1, virtualScrollIndex: number = -1): string {
    if (!searchQuery || !content) {
      return content; // No highlighting needed, just return the original content
    }
    const escapedSearchQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedSearchQuery, 'gi');
    if (outerIndex === -1) {
      return String(content).replace(regex, (match) =>
        `<mark class="node-search-highlight">${match}</mark>`
      );
    } else {
      let result = String(content);
      let lastIndex = 0;
      let finalResult = '';
      while (true) {
        const match = regex.exec(result);
        if (!match) break;
        qid = virtualScrollIndex === -1 ? qid : '';
        const innerIndex = this.highlightManager.getInnerHighlightIndex(
          outerIndex,
          virtualScrollIndex,
          match.index,
          match[0],
          qid
        );
        const before = result.slice(lastIndex, match.index);
        const highlighted = `<mark class="node-search-highlight"
                                data-outer-index="${outerIndex}"
                                data-inner-index="${innerIndex}"
                                data-qid="${qid}"
                                data-virtual-scroll-index="${virtualScrollIndex}">${match[0]}</mark>`;

        finalResult += before + highlighted;
        lastIndex = match.index + match[0].length;
      }
      finalResult += result.slice(lastIndex);
      return finalResult;
    }
  }
}