export const selectPMClassify = [
    // General
    { name: 'RRC Establishment Attempts', value: 'gnb.RRC.ConnEstabSetup.sum', group: 'General' },
    { name: 'RRC Successful Establishments', value: 'RRC.ConnEstabSucc.sum', group: 'General' },
    { name: 'RRC Successful Rate', value: 'RRC.Rate.EstabRate', group: 'General' },
    { name: 'Upload Throughput (kbps)', value: 'DRB.UEThpUl.sum', group: 'General' },
    { name: 'Download Throughput (kbps)', value: 'DRB.UEThpDl.sum', group: 'General' },
    { name: 'Upload Throughput (kbps)', value: 'DRB.IPThpUl.sum', group: 'General' },
    { name: 'Download Throughput (kbps)', value: 'DRB.IPThpDl.sum', group: 'General' },
    // LTE
    { name: 'ERAB Establishment Attempts', value: 'ERAB.EstabInitAttNbr.QCI.Sum', group: 'LTE' },
    { name: 'ERAB Successful Establishments', value: 'ERAB.EstabInitSuccNbr.QCI.Sum', group: 'LTE' },
    { name: 'ERAB Rate', value: 'ERAB.Rate.EstabRate', group: 'LTE' },
    { name: 'CSFB Establishment Attempts', value: 'CSFB.Att.Sum', group: 'LTE' },
    { name: 'CSFB Successful Establishments', value: 'CSFB.Succ.Sum', group: 'LTE' },
    { name: 'CSFB Rate', value: 'CSFB.Rate.EstabRate', group: 'LTE' },
    { name: 'Hand-out Attempts', value: 'HO.InterEnbOutAtt.Sum', group: 'LTE' },
    { name: 'Hand-out Successful', value: 'HO.InterEnbOutSucc.Sum', group: 'LTE' },
    { name: 'Hand-out Rate', value: 'HO.Rate.HandoutRate', group: 'LTE' },
    { name: 'Hand-in Attempts', value: 'HO.FromeNBAtt.Sum', group: 'LTE' },
    { name: 'Hand-in Successful', value: 'HO.FromeNBSucc.Sum', group: 'LTE' },
    { name: 'Hand-in Rate', value: 'HO.Rate.HandinRate', group: 'LTE' },
    // 5G SA
    { name: 'UE Context Establishment Attempts', value: 'UECNTX.ConnEstabAtt.sum', group: '5G SA' },
    { name: 'UE Context Successful Establishments', value: 'UECNTX.ConnEstabSucc.sum', group: '5G SA' },
    { name: 'UE Context Successful Rate', value: 'UECNTX.Rate.EstabRate', group: '5G SA' },
    { name: 'PDU Session Establishment Attempts', value: 'SM.PDUSessionSetupReq', group: '5G SA' },
    { name: 'PDU Session Successful Establishments', value: 'SM.PDUSessionSetupSucc', group: '5G SA' },
    { name: 'PDU Session Successful Rate', value: 'SM.Rate.PduRate', group: '5G SA' },
    { name: 'VoNR Establishment Attempts', value: 'QF.EstabAttNbr.5QI1', group: '5G SA' },
    { name: 'VoNR Successful Establishments', value: 'QF.EstabSuccNbr.5QI1', group: '5G SA' },
    { name: 'VoNR Successful Rate', value: 'QF.Rate.VonrRate', group: '5G SA' },
    { name: '5G Handover Attempts', value: 'MM.HoExeInterReq', group: '5G SA' },
    { name: '5G Handover Successful', value: 'MM.HoExeInterSucc', group: '5G SA' },
    { name: '5G Handover Successful Rate', value: 'MM.Rate.HandoverRate', group: '5G SA' },
    { name: '5G EPS Fallback Attempts', value: 'MM.HoOutExe5gsToEpsReq', group: '5G SA' },
    { name: '5G EPS Fallback Successful', value: 'MM.HoOutExe5gsToEpsSucc', group: '5G SA' },
    { name: '5G EPS Fallback Successful Rate', value: 'MM.Rate.EpsRate', group: '5G SA' },
    { name: 'HO Early', value: 'HO.IntraSys.TooEarly', group: '5G SA' },
    { name: 'HO Late', value: 'HO.IntraSys.TooLate', group: '5G SA' },
    // 5G NSA
    { name: 'Endc Setup Received', value: 'X2.Cntrl.EndcSetupReqReceived', group: '5G NSA' },
    { name: 'Endc Setup Sent', value: 'X2.Cntrl.EndcSetupResponseSent', group: '5G NSA' },
    { name: 'Endc Setup Rate', value: 'X2.Rate.EndcRate', group: '5G NSA' },
    { name: 'SgNB Add Req', value: 'X2.Cntrl.SgNBAddReqReceived', group: '5G NSA' },
    { name: 'SgNB Add ReqAck', value: 'X2.Cntrl.SgNBAddReqAckSent', group: '5G NSA' },
    { name: 'SgNB Add Rate', value: 'X2.Rate.SgnbRate', group: '5G NSA' },
    { name: 'SN SgNB Rel Sent', value: 'X2.Cntrl.SgNBRelRequiredSent', group: '5G NSA' },
    { name: 'SN SgNB Rel Received', value: 'X2.Cntrl.SgNBRelConfReceived', group: '5G NSA' },
    { name: 'SN SgNB Rel Rate', value: 'X2.Rate.SgnbSNRate', group: '5G NSA' },
    { name: 'MN SgNB Rel Received', value: 'X2.Cntrl.SgNBRelReqReceived', group: '5G NSA' },
    { name: 'MN SgNB Rel Sent', value: 'X2.Cntrl.SgNBRelReqAckSent', group: '5G NSA' },
    { name: 'MN SgNB Rel Rate', value: 'X2.Rate.SgnbMNRate', group: '5G NSA' },
    // Manual
    { name: 'RRC', value: 'RRC.', group: 'Manual' },
    { name: 'UECNTX', value: 'UECNTX.', group: 'Manual' },
    { name: 'SM', value: 'SM.', group: 'Manual' },
    { name: 'MM', value: 'MM.', group: 'Manual' },
    { name: 'QF', value: 'QF.', group: 'Manual' },
    { name: 'RRU', value: 'RRU.', group: 'Manual' },
    { name: 'DRB (SA)', value: 'DRB.', group: 'Manual' },
    { name: 'X2 (NSA)', value: 'X2.', group: 'Manual' },
]