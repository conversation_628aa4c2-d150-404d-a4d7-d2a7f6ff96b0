import { PerformanceReportComponent } from './performance-report/performance-report.component';
// import { TimeRangeComponent } from './time-range/time-range.component';
// import { LteRrcEstablishmentComponent } from './lte-rrc-establishment/lte-rrc-establishment.component';
// import { LteErabEstablishmentComponent } from './lte-erab-establishment/lte-erab-establishment.component';
// import { LteMobilityHandoutComponent } from './lte-mobility-handout/lte-mobility-handout.component';
// import { LteMobilityHandinComponent } from './lte-mobility-handin/lte-mobility-handin.component';
// import { LteCsfbComponent } from './lte-csfb/lte-csfb.component';
// import { LteThroughputComponent } from './lte-throughput/lte-throughput.component';
// import { SaRrcEstablishmentComponent } from './sa-rrc-establishment/sa-rrc-establishment.component';
// import { SaUeContextEstablishmentComponent } from './sa-ue-context-establishment/sa-ue-context-establishment.component';
// import { SaPduSessionEstablishmentComponent } from './sa-pdu-session-establishment/sa-pdu-session-establishment.component';
// import { SaHandoverComponent } from './sa-handover/sa-handover.component';
// import { SaEpsFallbackComponent } from './sa-eps-fallback/sa-eps-fallback.component';
// import { NsaEndcComponent } from './nsa-endc/nsa-endc.component';
// import { NsaSgnbaddComponent } from './nsa-sgnbadd/nsa-sgnbadd.component';
// import { NsaSgnbrelComponent } from './nsa-sgnbrel/nsa-sgnbrel.component';
import { Widget } from 'app/layout/widget';

const widgets: Widget[] = [
    {
        componentId: 'Analysis/PerformanceReportComponent',
        component: PerformanceReportComponent,
        name: 'PM.PERFORMANCEREPORT',
        description: 'PM.PERFORMANCEREPORT_DESCRIPTION',
        class: 'analysis',
        subClass: 'pmStatistics',
        render: false,
        hidden: false,
        cols: 36,
        rows: 15,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'table',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
]

export { widgets }