import { TotalDeviceComponent } from './total-device/total-device.component';
import { OnlineDeviceComponent } from './online-device/online-device.component';
import { NewDeviceComponent } from './new-device/new-device.component';
import { SessionsComponent } from './sessions/sessions.component';
import { EventCodeComponent } from './event-code/event-code.component';
import { Widget } from 'app/layout/widget';

const widgets: Widget[] = [
    {
        componentId: 'Analysis/TotalDeviceComponent',
        component: TotalDeviceComponent,
        name: 'ANALYSIS.TOTALDEVICE',
        description: 'ANALYSIS.TOTALDEVICEDESCRIPTION',
        class: 'analysis',
        subClass: 'deviceStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/OnlineDeviceComponent',
        component: OnlineDeviceComponent,
        name: 'COMMON.ONLINEDEVICE',
        description: 'ANALYSIS.ONLINEDEVICEDESCRIPTION',
        class: 'analysis',
        subClass: 'deviceStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/NewDeviceComponent',
        component: NewDeviceComponent,
        name: 'ANALYSIS.NEWDEVICE',
        description: 'ANALYSIS.NEWDEVICEDESCRIPTION',
        class: 'analysis',
        subClass: 'deviceStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/SessionsComponent',
        component: SessionsComponent,
        name: 'ANALYSIS.SESSIONS',
        description: 'ANALYSIS.SESSIONSDESCRIPTION',
        class: 'analysis',
        subClass: 'deviceStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    },
    {
        componentId: 'Analysis/EventCodeComponent',
        component: EventCodeComponent,
        name: 'ANALYSIS.EVENTCODE',
        description: 'ANALYSIS.EVENTCODEDESCRIPTION',
        class: 'analysis',
        subClass: 'deviceStatistics',
        render: false,
        hidden: false,
        cols: 18,
        rows: 8,
        y: 0,
        x: 0,
        minItemCols: 0,
        minItemRows: 0,
        grade: '',
        dmsType: 'barChart',
        protocalType: '',
        extension: '',
        data: [],
        color: 'sceondary',
    }
]

export { widgets }