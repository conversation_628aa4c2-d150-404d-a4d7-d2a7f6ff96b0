import { Until<PERSON><PERSON><PERSON>, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewEncapsulation, ViewChild, Output, EventEmitter } from '@angular/core';
import { DeviceActionsService } from '../device-actions.service';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { FilesService } from 'app/main/provisioning/files/files.service';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { AuthenticationService } from 'app/auth/service/authentication.service';
@UntilDestroy()
@Component({
  selector: 'app-connectivity-test',
  templateUrl: './connectivity-test.component.html',
  styleUrls: ['./connectivity-test.component.scss']
})
export class ConnectivityTestComponent extends Unsubscribe implements OnInit {
  @Output() loadingEvt = new EventEmitter<string>();
  @Input() mode: string;
  @Input() row: string;
  @Input() selectedDevice?: string;
  @Input() selectedDeviceSerial?: string;
  @Input() selectedDevices?;
  @Input() productClass: any;
  @Input() protocol: any;
  public loading: boolean = false
  public currentSerial: string
  public currentDevice: string
  public downloadTitle: boolean
  public uploadTitle: boolean
  public completeTitle: boolean
  public speedTestSpinner: boolean
  public url: string
  public uploadTestValue: string
  public downloadTestValue: string
  public progress: any
  public uploadDiagnosticSpeed: string
  public downloadDiagnosticSpeed: string
  public uploadSpeed: any
  public downloadSpeed: any
  public SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');

  public uploadState: any = ''
  public uploadStateMesaage: any = ''
  public downloadState: any = ''
  public downloadStateMessage: any = ''

  protocolType: string;
  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'dataModel', 'write');
  }
  constructor(
    private _deviceActionsService: DeviceActionsService,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private _toastrUtilsService: ToastrUtilsService,
    private _filesService: FilesService,
    private translateService: TranslateService,
    private _authenticationService: AuthenticationService,
  ) {
    super();
    this.customSubscribe(route.data, res => {
      // console.log(res)
      if (res.basicInfo) {
        this.currentSerial = res.basicInfo.deviceIdStruct.serialNumber;
        this.protocolType = res.basicInfo.protocol
      }
    });
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
    })
  }

  loadingChange(cal) {
    this.loadingEvt.emit(cal);
  }


  modalOpenBD(modalBD) {
    this.loading = true
    this.loadingChange('true')
    this.modalService.open(modalBD, {
      backdrop: false,
      size: 'lg',
      scrollable: true,
    });
    this.progress = 20
    this.speedTestSpinner = true
    this.uploadTitle = true
    this.downloadTitle = false
    this.completeTitle = false
    this.uploadDiagnosticSpeed = ''
    this.downloadDiagnosticSpeed = ''
    this.uploadState = ''
    this.uploadStateMesaage = ''
    this.downloadState = ''
    this.downloadStateMessage = ''
    this.getVendorSpecificFilesUrl()
  }

  getVendorSpecificFilesUrl() {
    this._filesService.getFileList().pipe(untilDestroyed(this)).subscribe(res => {
      let vendorSpecificFiles = this.getVendorSpecificFiles(res).filter(item => {
        return item.fileType == "X ASKEY CONNECTIVITYTEST"
      })
      // console.log(vendorSpecificFiles)

      if (vendorSpecificFiles.length == 0) {
        this.url = 'http://files.askeydms.com:8880/speedtest/100MB.bin'
      }
      else if (vendorSpecificFiles.length > 0) {
        this.url = vendorSpecificFiles[0].url
      }
      this.upLoadDiagnosticSpv(this.url)
    })
  }

  getVendorSpecificFiles(data) {
    return data.filter(item => {
      return item.fileType?.indexOf("X ") > -1 || item.instancePath?.indexOf("X ") > -1
    }).map(items => {
      if (items.url) {
        if (items.url.indexOf("/") > -1) {
          var index = items.url.lastIndexOf("/")
          items.name = items.url.substring(index + 1, items.url.length)
        } else {
          items.name = ""
        }
      }
      if (!items.source) items.source = 'Default'
      return items
    })
  }

  public completeSuccess = this.translateService.instant('DEVICES.CONNECTIVITYTEST') + ' ' + this.translateService.instant('COMMON.SUCC');
  public uploadFail = this.translateService.instant('COMMON.UPLOAD') + ' ' + this.translateService.instant('DEVICES.ACTION.FAIL');
  public uploadSuccess = this.translateService.instant('COMMON.UPLOAD') + ' ' + this.translateService.instant('COMMON.SUCC');
  public downloadFail = this.translateService.instant('COMMON.DOWNLOAD') + ' ' + this.translateService.instant('DEVICES.ACTION.FAIL');
  public downloadSuccess = this.translateService.instant('COMMON.DOWNLOAD') + ' ' + this.translateService.instant('COMMON.SUCC');



  upLoadDiagnosticSpv(url: string) {
    const params = {
      "messages": [{
        "parameterList": [{
          "name": "Device.IP.Diagnostics.UploadDiagnostics.DiagnosticsState",
          "type": "string",
          "value": "Requested"
        }, {
          "name": "Device.IP.Diagnostics.UploadDiagnostics.UploadURL",
          "type": "string",
          "value": url,
        }, {
          "name": "Device.IP.Diagnostics.UploadDiagnostics.TimeBasedTestDuration",
          "type": "unsignedInt",
          "value": "15"
        }, {
          "name": "Device.IP.Diagnostics.UploadDiagnostics.TestFileLength",
          "type": "unsignedInt",
          "value": "50000000"
        }],
        "messageType": "SetParameterValues"
      }]
    };

    const paramsUSP = {
      "messages": [{
        "command": "Device.IP.Diagnostics.UploadDiagnostics()",
        "inputArgs": [{
          "param": "UploadURL",
          "value": url,
        }, {
          "param": "TestFileLength",
          "value": "50000000"
        }, {
          "param": "TimeBasedTestDuration",
          "value": "15"
        }],
        "sendResp": true,
        "messageType": "Operate"
      }]
    };

    const handleSuccess = (res) => {
      this.progress = 20;
      console.log('upload spv success');
      // this.uploadcrq(res);
      if (this.protocolType === 'usp'){
        this.getUspUploadDiagnosticsState(res);
      }else{
        this.uploadcrq(res);
      }
    };

    const handleFailure = () => {
      console.log('upload spv fail');
      this.progress = 50;
      this.uploadTitle = false;
      this.downloadTitle = true;
      this.downloadDiagnosticSpv(this.url);
      this.uploadState = `Upload Test fail.`;
      this.uploadStateMesaage = '[W101] Upload Test assign parameter fail.';
      this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);
    };

    switch (this.mode) {
      case 'currentDevice':
        this._deviceActionsService.deviceOperationWaitRes(this.currentDevice, this.protocolType === 'usp' ? paramsUSP : params, this.protocolType === 'usp' ? "?waitRes=false" : '')
          .then(res => {
            // console.log("res", res)
            handleSuccess(res)
          })
          .catch(handleFailure);
        break;

      case 'singleDevice':
        this._deviceActionsService.deviceOperationWaitRes(this.selectedDevice, this.protocolType === 'usp' ? paramsUSP : params, this.protocolType === 'usp' ? "?waitRes = false" : "")
          .then(handleSuccess)
          .catch(handleFailure);
        break;
    }
  }


  uploadcrq(data) {
    const handleSuccess = (res: any) => {
      if (String(res) === 'true') {
        this.progress = 30;
        console.log('upload cr success');
        if(this.protocolType === 'usp'){
          this.getUspUploadDiagnosticsState(data);
        }else{
          this.getUploadDiagnosticsState();
        }
        
      } else {
        handleFailure();
      }
    };

    const handleFailure = () => {
      this.progress = 50;
      this.uploadTitle = false;
      this.downloadTitle = true;
      this.downloadDiagnosticSpv(this.url);
      this.uploadState = `Upload Test fail.`;
      this.uploadStateMesaage = '[W200] File upload was unsuccessful during the transmission process.';
      this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);
    };

    switch (this.mode) {
      case 'currentDevice':
        this._deviceActionsService.connectionRequest(this.currentDevice)
          .then(handleSuccess)
          .catch(handleFailure);
        break;
      case 'singleDevice':
        this._deviceActionsService.connectionRequest(this.selectedDevice)
          .then(handleSuccess)
          .catch(handleFailure);
        break;
    }
  }

  getUspUploadDiagnosticsState(data) {
    const operationId = data.replace(/"/g, '');
    this.progress = 40
    switch (this.mode) {
      case 'currentDevice':
        setTimeout(() => {
        let timeoutId: ReturnType<typeof setTimeout>;
        let intervalId: ReturnType<typeof setInterval>;

        // 开始轮询
        intervalId = setInterval(() => {
          this._deviceActionsService.getUspParamsValue(this.currentDevice, operationId).then(res => {
            // console.log('getUspUploadDiagnosticsState', res);
            this.uploadData = res;
            //如果一上来就失败，直接结束upload,但存在upload失败，download成功的情况，需要走下载流程
            if (this.uploadData.state == 'FAILED') {
                this.progress = 50
                this.loadingChange('false');
                this.uploadState = `Upload Test fail. `
                this.uploadStateMesaage = `[W101] Get Upload Test result fail.`
                this.uploadTitle = false;
                this.downloadTitle = true;
                this.downloadDiagnosticSpv(this.url);
              // 停止轮询
              clearInterval(intervalId);
              clearTimeout(timeoutId);
            }
            if (this.uploadData.entries[0].operComplete?.reqOutputArgs?.outputArgs){
              // 获取 uploadState 的值
              // const uploadState = this.uploadData.entries[0].outputArgs?.Status ? this.uploadData.entries[0].outputArgs?.Status : 'N/A';
              const uploadState = this.uploadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.Status ? this.uploadData.entries[0].operComplete.reqOutputArgs.outputArgs.Status : 'N/A';
              // console.log('uploadState', uploadState);

              // 获取 TotalBytesSent 的值
              this.uploadSpeed = this.uploadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.TotalBytesSent ? this.uploadData.entries[0].operComplete.reqOutputArgs.outputArgs.TotalBytesSent : 'N/A';
              // console.log('uploadSpeed', this.uploadSpeed);

              // TotalBytesSent / ((EOMTime - BOMTime) / 1000)) / (1000 * 1000)  
              //标准协议state 为 Complete
              if (uploadState === 'Complete' || this.uploadSpeed !== 'N/A') {
                console.log('upload get speed success');
                this.progress = 50;
                this.uploadTitle = false;
                this.downloadTitle = true;

                const entry = this.uploadData.entries[0].operComplete.reqOutputArgs;
                const BOMTimeStr = entry.outputArgs.BOMTime.trim();
                const EOMTimeStr = entry.outputArgs.EOMTime.trim();
                const BOMTime = new Date(BOMTimeStr).getTime();
                const EOMTime = new Date(EOMTimeStr).getTime();
                const timeDifference = (EOMTime - BOMTime) / 1000;
                // console.log('timeDifference', timeDifference, EOMTime, BOMTime);
                const speed = this.uploadSpeed / timeDifference;
                this.uploadDiagnosticSpeed = (speed / (1000 * 1000)).toFixed(2) + ' Mbps';
                // console.log('uploadDiagnosticSpeed', this.uploadDiagnosticSpeed);
                this.downloadDiagnosticSpv(this.url);
              }else if (uploadState != 'Complete'){
                this.progress = 50;
                this.uploadTitle = false
                this.downloadTitle = true
                this.downloadDiagnosticSpv(this.url)
                this.uploadState = `Upload Test fail. (${uploadState})`
                this.uploadStateMesaage = `[W101] Get Upload Test result fail. (${uploadState})`
                this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);
                
              }
              // 停止轮询
              clearInterval(intervalId);
              clearTimeout(timeoutId);
            }
           
          }).catch(error => {
            console.error('Error in getUspParamsValue:', error);
            // 停止轮询
            clearInterval(intervalId);
            clearTimeout(timeoutId);
          });
        }, 5000); // 每5秒轮询一次

        // 设置超时时间
        timeoutId = setTimeout(() => {
          console.log('upload get speed fail due to timeout');
          this.progress = 50;
          this.uploadTitle = false;
          this.downloadTitle = true;
          this.downloadDiagnosticSpv(this.url);
          this.uploadState = 'Upload Test fail. (Timeout)';
          this.uploadStateMesaage = '[W101] Get Upload Test result fail. (Timeout)';
          this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);

          // 停止轮询
          clearInterval(intervalId);
        }, 100000); // 1分钟后超时
        }, 15000);
        break;
      case 'singleDevice':
        setTimeout(() => {
        let timeoutId: ReturnType<typeof setTimeout>;
        let intervalId: ReturnType<typeof setInterval>;

        // 开始轮询
        intervalId = setInterval(() => {
          this._deviceActionsService.getUspParamsValue(this.selectedDevice, operationId).then(res => {
            // console.log('getUspUploadDiagnosticsState', res);
            this.uploadData = res;
            //如果一上来就失败，直接结束upload,但存在upload失败，download成功的情况，需要走下载流程
            if (this.uploadData.state == 'FAILED') {
                this.progress = 50
                this.loadingChange('false');
                this.uploadState = `Upload Test fail. `
                this.uploadStateMesaage = `[W101] Get Upload Test result fail.`
                this.uploadTitle = false;
                this.downloadTitle = true;
                this.downloadDiagnosticSpv(this.url);
              // 停止轮询
              clearInterval(intervalId);
              clearTimeout(timeoutId);
            }
            if (this.uploadData.entries[0].operComplete?.reqOutputArgs?.outputArgs){
              // 获取 uploadState 的值
              const uploadState = this.uploadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.Status ? this.uploadData.entries[0].operComplete.reqOutputArgs.outputArgs.Status : 'N/A';
              // console.log('uploadState', uploadState);

              // 获取 TotalBytesSent 的值
              this.uploadSpeed = this.uploadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.TotalBytesSent ? this.uploadData.entries[0].operComplete.reqOutputArgs.outputArgs.TotalBytesSent : 'N/A';
              // console.log('uploadSpeed', this.uploadSpeed);

              // TotalBytesSent / ((EOMTime - BOMTime) / 1000)) / (1000 * 1000)  
              //标准协议state 为 Complete
              if (uploadState === 'Complete' || this.uploadSpeed !== 'N/A') {
                console.log('upload get speed success');
                this.progress = 50;
                this.uploadTitle = false;
                this.downloadTitle = true;

                const entry = this.uploadData.entries[0].operComplete.reqOutputArgs;
                const BOMTimeStr = entry.outputArgs.BOMTime.trim();
                const EOMTimeStr = entry.outputArgs.EOMTime.trim();
                const BOMTime = new Date(BOMTimeStr).getTime();
                const EOMTime = new Date(EOMTimeStr).getTime();
                const timeDifference = (EOMTime - BOMTime) / 1000;
                // console.log('timeDifference', timeDifference, EOMTime, BOMTime);
                const speed = this.uploadSpeed / timeDifference;
                this.uploadDiagnosticSpeed = (speed / (1000 * 1000)).toFixed(2) + ' Mbps';
                // console.log('uploadDiagnosticSpeed', this.uploadDiagnosticSpeed);
                this.downloadDiagnosticSpv(this.url);
              }else if (uploadState != 'Complete'){
                this.progress = 50;
                this.uploadTitle = false
                this.downloadTitle = true
                this.downloadDiagnosticSpv(this.url)
                this.uploadState = `Upload Test fail. (${uploadState})`
                this.uploadStateMesaage = `[W101] Get Upload Test result fail. (${uploadState})`
                this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);
                
              }
              // 停止轮询
              clearInterval(intervalId);
              clearTimeout(timeoutId);
            }
           
          }).catch(error => {
            console.error('Error in getUspParamsValue:', error);
            // 停止轮询
            clearInterval(intervalId);
            clearTimeout(timeoutId);
          });
        }, 5000); // 每5秒轮询一次

        // 设置超时时间
        timeoutId = setTimeout(() => {
          console.log('upload get speed fail due to timeout');
          this.progress = 50;
          this.uploadTitle = false;
          this.downloadTitle = true;
          this.downloadDiagnosticSpv(this.url);
          this.uploadState = 'Upload Test fail. (Timeout)';
          this.uploadStateMesaage = '[W101] Get Upload Test result fail. (Timeout)';
          this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);

          // 停止轮询
          clearInterval(intervalId);
        }, 100000); // 1分钟后超时
        }, 15000);
        break;
    }
  }


  public uploadData: any
  getUploadDiagnosticsState() {
    this.progress = 40
    switch (this.mode) {
      case 'currentDevice':
        setTimeout(() => {
          this._deviceActionsService.getParamsValue(this.currentDevice, 'Device.IP.Diagnostics.UploadDiagnostics').then(res => {
            // console.log(res)
            this.uploadData = res
            let uploadState = this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.DiagnosticsState"
            }) ? this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.DiagnosticsState"
            }).reportedValue : 'N/A'
            // console.log(uploadState)
            this.uploadSpeed = this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.X_ASKEY.Speed"
            }) ? this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.X_ASKEY.Speed"
            }).reportedValue : 'N/A'
            // console.log('uploadSpeed', this.uploadSpeed)

            if (uploadState == 'Completed' || this.uploadSpeed != 'N/A') {
              console.log('upload get speed success')
              this.progress = 50;
              this.uploadTitle = false
              this.downloadTitle = true
              this.uploadDiagnosticSpeed = (this.uploadSpeed / 1000).toFixed(2) + ' Mbps'
              this.downloadDiagnosticSpv(this.url)
            } else {
              console.log('upload get speed fail')
              this.progress = 50;
              this.uploadTitle = false
              this.downloadTitle = true
              this.downloadDiagnosticSpv(this.url)
              this.uploadState = `Upload Test fail. (${uploadState})`
              this.uploadStateMesaage = `[W101] Get Upload Test result fail. (${uploadState})`
              this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);
            }
          })
        }, 20000);
        break;
      case 'singleDevice':
        setTimeout(() => {
          this._deviceActionsService.getParamsValue(this.selectedDevice, 'Device.IP.Diagnostics.UploadDiagnostics').then(res => {
            // console.log(res)
            this.uploadData = res
            let uploadState = this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.DiagnosticsState"
            }) ? this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.DiagnosticsState"
            }).reportedValue : 'N/A'
            // console.log(uploadState)
            this.uploadSpeed = this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.X_ASKEY.Speed"
            }) ? this.uploadData.find(item => {
              return item.name == "Device.IP.Diagnostics.UploadDiagnostics.X_ASKEY.Speed"
            }).reportedValue : 'N/A'
            // console.log('uploadSpeed', uploadSpeed)

            if (uploadState == 'Completed' || this.uploadSpeed != 'N/A') {
              this.progress = 50;
              this.uploadTitle = false
              this.downloadTitle = true
              this.uploadDiagnosticSpeed = (this.uploadSpeed / 1000).toFixed(2) + ' Mbps'
              this.downloadDiagnosticSpv(this.url)
            } else {
              this.progress = 50;
              this.uploadTitle = false
              this.downloadTitle = true
              this.downloadDiagnosticSpv(this.url)
              this.uploadState = `Upload Test fail. (${uploadState})`
              this.uploadStateMesaage = `[W101] Get Upload Test result fail. (${uploadState})`
              this._toastrUtilsService.showErrorMessage(this.uploadFail, this.uploadStateMesaage);
            }
          })
        }, 20000);
        break;
    }
  }


  downloadDiagnosticSpv(url: string) {
    this.progress = 60;

    const params = {
      "messages": [{
        "parameterList": [{
          "name": "Device.IP.Diagnostics.DownloadDiagnostics.DiagnosticsState",
          "type": "string",
          "value": "Requested"
        }, {
          "name": "Device.IP.Diagnostics.DownloadDiagnostics.TimeBasedTestDuration",
          "type": "unsignedInt",
          "value": "15"
        }, {
          "name": "Device.IP.Diagnostics.DownloadDiagnostics.DownloadURL",
          "type": "string",
          "value": url,
        }],
        "messageType": "SetParameterValues"
      }]
    }

    const paramsUSP = {
      "messages": [{
        "command": "Device.IP.Diagnostics.DownloadDiagnostics()",
        "inputArgs": [{
          "param": "DownloadURL",
          "value": url
        }, {
          "param": "TimeBasedTestDuration",
          "value": "15"
        }],
        "sendResp": true,
        "messageType": "Operate"
      }]
    };

    const handleSuccess = (res) => {
      // this.downloadcrq(res);
      this.progress = 70;
      if (this.protocolType === 'usp') {
        this.getUspDownloadDiagnosticsState(res);
      } else {
        this.downloadcrq(res);
      }
    };

    

    const handleFailure = (device: any) => {
      console.log('download spv fail');
      this.loading = false;
      this.loadingChange('false');
      this.progress = 100;
      this.uploadTitle = false;
      this.downloadTitle = false;
      this.completeTitle = true;
      this.speedTestSpinner = false;
      this.downloadState = `Download Test fail.`;
      this.downloadStateMessage = '[W101] Download Test assign parameter fail.';
      this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
      this.postSpeed(device, this.downloadSpeed || this.downloadState, this.uploadSpeed || this.uploadState, this.downloadStateMessage || '', this.uploadStateMesaage || '');
    };

    const device = this.mode === 'currentDevice' ? this.currentDevice : this.selectedDevice;
    const selectedParams = this.protocolType === 'usp' ? paramsUSP : params;
    let waitRes = this.protocolType === 'usp' ? '?waitRes=false' : ''
    this._deviceActionsService.deviceOperationWaitRes(device, selectedParams, waitRes)
      .then(handleSuccess)
      .catch(() => handleFailure(device));
  }
 
  downloadcrq(data) {
    const handleFailure = (device: any) => {
      this.loading = false;
      this.loadingChange('false');
      this.progress = 100;
      this.uploadTitle = false;
      this.downloadTitle = false;
      this.completeTitle = true;
      this.speedTestSpinner = false;
      this.downloadState = `Download Test fail.`;
      this.downloadStateMessage = '[W200] File download was unsuccessful during the transmission process.';
      this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
      this.postSpeed(
        device,
        this.downloadSpeed || this.downloadState,
        this.uploadSpeed || this.uploadState,
        this.downloadStateMessage || '',
        this.uploadStateMesaage || ''
      );
    };

    const handleSuccess = (device: any, res: any) => {
      if (String(res) === 'true') {
        console.log('download cr success');
        if (this.protocolType === 'usp') {
          this.getUspDownloadDiagnosticsState(data);
        } else {
          this.getDownloadDiagnosticsState();
        }
        
      } else {
        console.log('download cr fail');
        handleFailure(device);
      }
    };

    switch (this.mode) {
      case 'currentDevice':
        this._deviceActionsService.connectionRequest(this.currentDevice)
          .then((res) => handleSuccess(this.currentDevice, res))
          .catch(() => handleFailure(this.currentDevice));
        break;
      case 'singleDevice':
        this._deviceActionsService.connectionRequest(this.selectedDevice)
          .then((res) => handleSuccess(this.selectedDevice, res))
          .catch(() => handleFailure(this.selectedDevice));
        break;
    }
  }

  getUspDownloadDiagnosticsState(data) {
    // console.log("data", data)
    const operationId = data.replace(/"/g, '');
    this.progress = 90
    switch (this.mode) {
      case 'currentDevice':
        setTimeout(() => {
          let timeoutId: ReturnType<typeof setTimeout>;
          let intervalId: ReturnType<typeof setInterval>;

          // 开始轮询
          intervalId = setInterval(() => {
            this._deviceActionsService.getUspParamsValue(this.currentDevice, operationId).then(res => {
              // console.log('getUspDownloadDiagnosticsState', res);
              this.downloadData = res;
              //如果一上来就失败，直接结束
              if (this.downloadData.state == 'FAILED') {
                this.progress = 100
                this.loading = false
                this.speedTestSpinner = false
                this.downloadTitle = false
                this.completeTitle = true
                this.loadingChange('false');
                this.uploadState = `Upload Test fail. `
                this.uploadStateMesaage = `[W101] Get Upload Test result fail.`
                this.downloadState = `Download Test fail.`
                this.downloadStateMessage = `[W101] Get Download Test result fail.`
                this.postSpeed(this.currentDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
                // 停止轮询
                clearInterval(intervalId);
                clearTimeout(timeoutId);
              }

              if (this.downloadData.entries[0].operComplete?.reqOutputArgs?.outputArgs) {

              // 获取 downloadState 的值
              // const downloadState = this.downloadData.entries[0].outputArgs?.Status ? this.downloadData.entries[0].outputArgs?.Status : 'N/A';
                const downloadState = this.downloadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.Status ? this.downloadData.entries[0].operComplete.reqOutputArgs.outputArgs.Status : 'N/A';
              // console.log('downloadState', downloadState);

              // 获取 TotalBytesSent 的值
                this.downloadSpeed = this.downloadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.TotalBytesReceived ? this.downloadData.entries[0].operComplete.reqOutputArgs.outputArgs.TotalBytesReceived : 'N/A';

              // console.log('downloadSpeed', this.downloadSpeed);

              // TotalBytesSent / ((EOMTime - BOMTime) / 1000)) / (1000 * 1000)  
              if (downloadState === 'Complete' || this.downloadSpeed !== 'N/A') {
                console.log('download get speed success')
                this.loading = false
                this.loadingChange('false')

                const entry = this.downloadData.entries[0].operComplete.reqOutputArgs;
                const BOMTimeStr = entry.outputArgs.BOMTime.trim();
                const EOMTimeStr = entry.outputArgs.EOMTime.trim();
                const BOMTime = new Date(BOMTimeStr).getTime();
                const EOMTime = new Date(EOMTimeStr).getTime();
                const timeDifference = (EOMTime - BOMTime) / 1000;
                // console.log('timeDifference', timeDifference, EOMTime, BOMTime);
                const speed = this.downloadSpeed / timeDifference;
                this.downloadDiagnosticSpeed = (speed / (1000 * 1000)).toFixed(2) + ' Mbps';
                // console.log('downloadDiagnosticSpeed', this.downloadDiagnosticSpeed);
                this.progress = 100;
                this.downloadTitle = false
                this.completeTitle = true
                this.speedTestSpinner = false
                this.postSpeed(this.currentDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
              } else if (downloadState != 'Complete'){
                console.log('download get speed fail')
                this.loading = false
                this.loadingChange('false')
                this.progress = 100;
                this.uploadTitle = false
                this.downloadTitle = false
                this.completeTitle = true
                this.speedTestSpinner = false
                this.downloadState = `Download Test fail. (${downloadState})`
                this.downloadStateMessage = `[W101] Get Download Test result fail. (${downloadState})`
                this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
                this.postSpeed(this.currentDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
              }
                // 停止轮询
                clearInterval(intervalId);
                clearTimeout(timeoutId);
            }
             
            }).catch(error => {
              console.error('Error in getUspParamsValue:', error);
              // 停止轮询
              clearInterval(intervalId);
              clearTimeout(timeoutId);
            });
          }, 5000); // 每5秒轮询一次

          // 设置超时时间
          timeoutId = setTimeout(() => {
            console.log('download get speed fail due to timeout');
            this.loading = false
            this.uploadTitle = false
            this.completeTitle = true
            this.speedTestSpinner = false
            this.downloadState = 'Download Test fail. (${downloadState})';
            this.downloadStateMessage = '[W101] Get download Test result fail. (Timeout)';
            this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
            this.postSpeed(this.currentDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
            // 停止轮询
            clearInterval(intervalId);
          }, 100000); // 1分钟后超时
        }, 15000);
        break;
      case 'singleDevice':
        setTimeout(() => {
          let timeoutId: ReturnType<typeof setTimeout>;
          let intervalId: ReturnType<typeof setInterval>;

          // 开始轮询
          intervalId = setInterval(() => {
            this._deviceActionsService.getUspParamsValue(this.selectedDevice, operationId).then(res => {
              // console.log('getUspDownloadDiagnosticsState', res);
              this.downloadData = res;
              //如果一上来就失败，直接结束
              if (this.downloadData.state == 'FAILED') {
                this.progress = 100
                this.loading = false
                this.speedTestSpinner = false
                this.downloadTitle = false
                this.completeTitle = true
                this.loadingChange('false');
                this.uploadState = `Upload Test fail. `
                this.uploadStateMesaage = `[W101] Get Upload Test result fail.`
                this.downloadState = `Download Test fail.`
                this.downloadStateMessage = `[W101] Get Download Test result fail.`
                this.postSpeed(this.selectedDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
                // 停止轮询
                clearInterval(intervalId);
                clearTimeout(timeoutId);
              }

              if (this.downloadData.entries[0].operComplete?.reqOutputArgs?.outputArgs) {

              // 获取 downloadState 的值
                const downloadState = this.downloadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.Status ? this.downloadData.entries[0].operComplete.reqOutputArgs.outputArgs.Status : 'N/A';
              // console.log('downloadState', downloadState);

              // 获取 TotalBytesSent 的值
                this.downloadSpeed = this.downloadData.entries[0].operComplete?.reqOutputArgs?.outputArgs?.TotalBytesReceived ? this.downloadData.entries[0].operComplete.reqOutputArgs.outputArgs.TotalBytesReceived : 'N/A';

              // console.log('downloadSpeed', this.downloadSpeed);

              // TotalBytesSent / ((EOMTime - BOMTime) / 1000)) / (1000 * 1000)  
              if (downloadState === 'Complete' || this.downloadSpeed !== 'N/A') {
                console.log('download get speed success')
                this.loading = false
                this.loadingChange('false')

                const entry = this.downloadData.entries[0].operComplete.reqOutputArgs;
                const BOMTimeStr = entry.outputArgs.BOMTime.trim();
                const EOMTimeStr = entry.outputArgs.EOMTime.trim();
                const BOMTime = new Date(BOMTimeStr).getTime();
                const EOMTime = new Date(EOMTimeStr).getTime();
                const timeDifference = (EOMTime - BOMTime) / 1000;
                // console.log('timeDifference', timeDifference, EOMTime, BOMTime);
                const speed = this.downloadSpeed / timeDifference;
                this.downloadDiagnosticSpeed = (speed / (1000 * 1000)).toFixed(2) + ' Mbps';
                // console.log('downloadDiagnosticSpeed', this.downloadDiagnosticSpeed);
                this.progress = 100;
                this.downloadTitle = false
                this.completeTitle = true
                this.speedTestSpinner = false
                this.postSpeed(this.selectedDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
              } else if (downloadState != 'Complete'){
                console.log('download get speed fail')
                this.loading = false
                this.loadingChange('false')
                this.progress = 100;
                this.uploadTitle = false
                this.downloadTitle = false
                this.completeTitle = true
                this.speedTestSpinner = false
                this.downloadState = `Download Test fail. (${downloadState})`
                this.downloadStateMessage = `[W101] Get Download Test result fail. (${downloadState})`
                this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
                this.postSpeed(this.selectedDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
              }
                // 停止轮询
                clearInterval(intervalId);
                clearTimeout(timeoutId);
            }
             
            }).catch(error => {
              console.error('Error in getUspParamsValue:', error);
              // 停止轮询
              clearInterval(intervalId);
              clearTimeout(timeoutId);
            });
          }, 5000); // 每5秒轮询一次

          // 设置超时时间
          timeoutId = setTimeout(() => {
            console.log('download get speed fail due to timeout');
            this.loading = false
            this.uploadTitle = false
            this.completeTitle = true
            this.speedTestSpinner = false
            this.downloadState = 'Download Test fail. (${downloadState})';
            this.downloadStateMessage = '[W101] Get download Test result fail. (Timeout)';
            this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
            this.postSpeed(this.selectedDevice, this.downloadDiagnosticSpeed ? this.downloadDiagnosticSpeed : this.downloadState, this.uploadDiagnosticSpeed ? this.uploadDiagnosticSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
            // 停止轮询
            clearInterval(intervalId);
          }, 100000); // 1分钟后超时
        }, 15000);
        break;
     

    }
  }

  public downloadData: any
  getDownloadDiagnosticsState() {
    this.progress = 90;
    switch (this.mode) {
      case 'currentDevice':
        setTimeout(() => {
          this._deviceActionsService.getParamsValue(this.currentDevice, 'Device.IP.Diagnostics.DownloadDiagnostics').then(res => {
            // console.log(res)
            this.downloadData = res
            let downloadState = this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.DiagnosticsState"
            }) ? this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.DiagnosticsState"
            }).reportedValue : 'N/A'
            // console.log(uploadState)
            this.downloadSpeed = this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.X_ASKEY.Speed"
            }) ? this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.X_ASKEY.Speed"
            }).reportedValue : 'N/A'
            // console.log('downloadSpeed', this.downloadSpeed)

            if (downloadState == 'Completed' || this.downloadSpeed != 'N/A') {
              console.log('download get speed success')
              this.loading = false
              this.loadingChange('false')
              this.downloadDiagnosticSpeed = (this.downloadSpeed / 1000).toFixed(2) + ' Mbps'
              this.progress = 100;
              this.uploadTitle = false
              this.downloadTitle = false
              this.completeTitle = true
              this.speedTestSpinner = false
              this.postSpeed(this.currentDevice, this.downloadSpeed ? this.downloadSpeed : this.downloadState, this.uploadSpeed ? this.uploadSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
            } else {
              console.log('download get speed fail')
              this.loading = false
              this.loadingChange('false')
              this.progress = 100;
              this.uploadTitle = false
              this.downloadTitle = false
              this.completeTitle = true
              this.speedTestSpinner = false
              this.downloadState = `Download Test fail. (${downloadState})`
              this.downloadStateMessage = `[W101] Get Download Test result fail. (${downloadState})`
              this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
              this.postSpeed(this.currentDevice, this.downloadSpeed ? this.downloadSpeed : this.downloadState, this.uploadSpeed ? this.uploadSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
            }
          })
        }, 20000);
        break;
      case 'singleDevice':
        setTimeout(() => {
          this._deviceActionsService.getParamsValue(this.selectedDevice, 'Device.IP.Diagnostics.DownloadDiagnostics').then(res => {
            // console.log(res)
            this.downloadData = res
            let downloadState = this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.DiagnosticsState"
            }) ? this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.DiagnosticsState"
            }).reportedValue : 'N/A'
            // console.log(uploadState)
            this.downloadSpeed = this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.X_ASKEY.Speed"
            }) ? this.downloadData.find(item => {
              return item.name == "Device.IP.Diagnostics.DownloadDiagnostics.X_ASKEY.Speed"
            }).reportedValue : 'N/A'
            // console.log('downloadSpeed', this.downloadSpeed)

            if (downloadState == 'Completed' || this.downloadSpeed != 'N/A') {
              this.loading = false
              this.loadingChange('false')
              this.downloadDiagnosticSpeed = (this.downloadSpeed / 1000).toFixed(2) + ' Mbps'
              this.progress = 100;
              this.uploadTitle = false
              this.downloadTitle = false
              this.completeTitle = true
              this.speedTestSpinner = false
              this.postSpeed(this.selectedDevice, this.downloadSpeed ? this.downloadSpeed : this.downloadState, this.uploadSpeed ? this.uploadSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
            } else {
              this.loading = false
              this.loadingChange('false')
              this.progress = 100;
              this.uploadTitle = false
              this.downloadTitle = false
              this.completeTitle = true
              this.speedTestSpinner = false
              this.downloadState = `Download Test fail. (${downloadState})`
              this.downloadStateMessage = `[W101] Get Download Test result fail. (${downloadState})`
              this._toastrUtilsService.showErrorMessage(this.downloadFail, this.downloadStateMessage);
              this.postSpeed(this.selectedDevice, this.downloadSpeed ? this.downloadSpeed : this.downloadState, this.uploadSpeed ? this.uploadSpeed : this.uploadState, this.downloadStateMessage ? this.downloadStateMessage : '', this.uploadStateMesaage ? this.uploadStateMesaage : '')
            }
          })
        }, 20000);
        break;
    }
  }


  postSpeed(deviceId, downloadSpeed, uploadSpeed, downloadError, uploadError) {
    let download
    let upload
    if (this.downloadState) {
      download = 'Fail'
    } else {
      download = downloadSpeed
    }
    if (this.uploadState) {
      upload = 'Fail'
    } else {
      upload = uploadSpeed
    }

    let param = {
      "downlodSpeed": download,
      "uploadSpeed": upload,
      "uploadError": uploadError,
      "downloadError": downloadError,
    }
    this._deviceActionsService.postSpeed(deviceId, param).then(() => {
    }).catch((res) => {
    });
  }



  closeModal() {
    this.loading = false
  }

  ngOnInit(): void {
    this.currentDevice = this.route.snapshot.paramMap.get('id')
    this.speedTestSpinner = true
    this.uploadTitle = true
    this.downloadTitle = false
    this.completeTitle = false
  }

}
