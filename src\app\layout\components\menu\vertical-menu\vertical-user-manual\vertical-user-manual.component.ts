import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, ViewEncapsulation } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CoreMenuService } from '@core/components/core-menu/core-menu.service';
import { CoreConfigService } from '@core/services/config.service';

@Component({
    selector: '[vertical-user-manual]',
    templateUrl: './vertical-user-manual.component.html',
    styleUrls: ['./vertical-user-manual.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VerticalUserManualComponent implements OnInit {
    currentUser: any;

    @Input()
    layout = 'vertical';

    @Input()
    menu: any = [
        {
            id: 'userManual',
            title: 'User Manual',
            translate: 'USERS.USERMANUAL',
            type: 'item',
            icon: 'book-open',
            url: 'user-manual'
        }];
    editMode: boolean
    //     <a
    //     *ngIf="this.currentUser.role != 'CSR'"
    //     ngbDropdownItem
    //     (click)="$event.stopPropagation();userDropdown.close();"
    //     [routerLink]="['/user-manual']">
    //     <span
    //       [data-feather]="'info'"
    //       [class]="'mr-50'"></span>
    //     {{ 'USERS.USERMANUAL' | translate }}
    //   </a>
    // Private
    private _unsubscribeAll: Subject<void>;

    /**
     *
     * @param {ChangeDetectorRef} _changeDetectorRef
     * @param {CoreMenuService} _coreMenuService
     */
    constructor(private _changeDetectorRef: ChangeDetectorRef, private _coreMenuService: CoreMenuService, _coreConfigService: CoreConfigService) {
        // Set the private defaults
        this._unsubscribeAll = new Subject();
        _coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {
            let editMode = config.layout.editMode
            if (editMode !== this.editMode) {
                this.editMode = editMode
                this._changeDetectorRef.markForCheck();
            }
        });
    }

    // Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // Set the menu either from the input or from the service
        // this.menu = this.menu || this._coreMenuService.getCurrentMenu();

        // Subscribe to the current menu changes
        this._coreMenuService.onMenuChanged.pipe(takeUntil(this._unsubscribeAll)).subscribe(() => {
            this.currentUser = this._coreMenuService.currentUser;

            // Load menu
            //   this.menu = this._coreMenuService.getCurrentMenu();

            this._changeDetectorRef.markForCheck();
        });
    }
    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }
}
