import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { AppConfigService } from 'app/app-config.service';
import { GetPmUrlService } from 'app/main/analysis-pm/get-pm-url.service'
import { StorageService } from 'app/main/commonService/storage.service';
import { GetExternalServerUrlService } from 'app/main/commonService/get-external-server-url.service'

@Injectable()
export class BaseUrlInterceptor implements HttpInterceptor {

    constructor(
        private _appConfigService: AppConfigService,
        private _getPmUrlService: GetPmUrlService,
        private _storageService: StorageService,
        private _getExternalServerUrlService: GetExternalServerUrlService
    ) {
    }

    /**
     * 
     * @param request
     * @param next
     */

    intercept(request: HttpRequest<any>, next: <PERSON>tt<PERSON><PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        const status = this._storageService.checkCookie('sessionId');
        const requestUrl = request.url;
        if (!status && requestUrl.includes('nbi/') && !requestUrl.endsWith('/verify')) {
            return throwError(() => new HttpErrorResponse({
                status: 401,
                statusText: 'Request Cancelled',
            }));
        }
        // const { apiUrl, acsLabUrl, ignoreFolder, fakeFolder } = environment;
        const ignoreFolder = ['assets', '/maps/api/js'];
        const apiUrl = this._appConfigService.apiUrl;
        const pmFolder = 'pm/';
        const pmUrl = this._getPmUrlService.pmUrl;
        const basePmUrl = '/pmStatistics';
        const osmReverseFolder = 'reverse';
        const osmSearchFolder = 'search';
        const osmReverseUrl = 'https://nominatim.openstreetmap.org';

        if (['DELETE', 'PUT', 'POST'].indexOf(request.method) !== -1 && ['blob'].indexOf(request.responseType) === -1) {
            request = request.clone({ responseType: 'text' });
        }

        let baseUrl;
        if (request.url.includes(pmFolder)) {
            baseUrl = pmUrl
        } else if (request.url.includes(basePmUrl)) {
            baseUrl = apiUrl
        } else if (request.url.includes(osmReverseFolder) || request.url.includes(osmSearchFolder)) {
            baseUrl = osmReverseUrl
        } else {
            baseUrl = apiUrl
        }
        const apiReq = request.url.includes(pmFolder) ? request.clone({ url: `${baseUrl}/${request.url}` }) : request.clone({ url: `${baseUrl}/${request.url}` });

        let flag = ignoreFolder.some((item => {
            return request.url.includes(item)
        }))
        if (flag) return next.handle(request);
        return next.handle(apiReq);
    }
}