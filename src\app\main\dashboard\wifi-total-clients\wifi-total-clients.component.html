<core-card
  [actions]="['reload','maximize','download']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="loading"
  (events)="emittedEvents($event)">
  <h4 class="card-title d-flex justify-content-between align-items-center"
  style="width: calc(100% - 65px);">
    <span class="d-flex w-100" >
        <span 
          class="text-truncate"
          ngbTooltip="{{ accessor.name | translate }}" 
          container="body" placement="bottom" >
          {{ accessor.name | translate }}
        </span>
        <app-more-info [tooltip]="tooltip"></app-more-info>
      </span>
  </h4>
  <div
    class="card-body"
    *ngIf="chartData"
    style="padding:0px 10px 1.5rem 10px">
    <main
      class="w-100 h-100"
      #dashboardTotalClients>
      <button
        type="button"
        class="btn btn-primary btn-sm waves-effect waves-float waves-light"
        style="position: absolute; padding: 5px; z-index: 100;"
        (click)="modalOpenBD(modalBD)"
        ngbTooltip="{{'DASHBOARD.TOTALCLIENTSTABLE' | translate}}"
        [placement]="'right'">
        <span [data-feather]="'layout'"></span>
      </button>
      <app-dount-echarts
        [isShowNoData]="isShowNoData"
        [chartRef]="dashboardTotalClients"
        [chartData]="chartData"
        [color_arr]="colorArr"
        [title]="'Total Clients with RSSI Strength'"
        [total]="totalClients"
        [downloadData]="downloadData"
        [openModal]="openModalFlag">
      </app-dount-echarts>
    </main>
  </div>
</core-card>
<!-- Modal -->
<ng-template
  #modalBD
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel4">
      {{'DASHBOARD.CLIENTS' | translate}}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <div aria-hidden="true">&times;</div>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <ngx-datatable
      #tableRowDetails
      appDatatableRecalculate
      [datatable]="tableRowDetails"
      class="bootstrap core-bootstrap"
      [rows]="clientsTable"
      [rowHeight]="37"
      [headerHeight]="45"
      [footerHeight]="50"
      [limit]="selectedOption"
      [scrollbarH]="true"
      [columnMode]="ColumnMode.force">
      <ng-container *ngFor="let col of columnSelectOptions">


        <!-- productName -->
        <ngx-datatable-column
          *ngIf="col.prop === 'name' && col.columnStatus"
          name="{{ col.translate | translate }}"
          prop="name"
          [width]="150"
          [draggable]="false">
          <ng-template
            let-column="column"
            let-row="row"
            let-name="value"
            ngx-datatable-cell-template>
            <app-beautify-content
              [content]="name"
              [placement]="'right'"
              [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>
        </ngx-datatable-column>
        <!-- aps -->
        <ngx-datatable-column
          *ngIf="col.prop === 'aps' && col.columnStatus"
          [width]="col.width"
          name="{{ col.translate | translate }}"
          prop="aps"
          [draggable]="false">
          <ng-template
            let-column="column"
            let-row="row"
            let-aps="value"
            ngx-datatable-cell-template>
            <app-beautify-content
              [content]="aps"
              [placement]="'right'"
              [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>
        </ngx-datatable-column>
        <!-- onlineAps -->
        <ngx-datatable-column
          *ngIf="col.prop === 'onlineAps' && col.columnStatus"
          [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
          prop="onlineAps">
          <ng-template
            let-row="row"
            let-onlineAps="value"
            ngx-datatable-cell-template>
            <app-beautify-content
              [content]="onlineAps"
              [placement]="'right'"
              [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>
        </ngx-datatable-column>
        <!-- excellent -->
        <ngx-datatable-column
          *ngIf="col.prop === 'excellent' && col.columnStatus"
          [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
          prop="excellent">
          <ng-template
            let-row="row"
            let-excellent="value"
            ngx-datatable-cell-template>
            <app-beautify-content
              [content]="excellent"
              [placement]="'right'"
              [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>


        </ngx-datatable-column>
        <!-- good -->
        <ngx-datatable-column
          *ngIf="col.prop === 'good' && col.columnStatus"
          [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
          prop="good">
          <ng-template
            let-row="row"
            let-good="value"
            ngx-datatable-cell-template>
            <app-beautify-content
              [content]="good"
              [placement]="'right'"
              [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>
        </ngx-datatable-column>
        <!-- poor -->
        <ngx-datatable-column
          *ngIf="col.prop === 'poor' && col.columnStatus"
          [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
          prop="poor">
          <ng-template
            let-row="row"
            let-poor="value"
            ngx-datatable-cell-template>
            <app-beautify-content
              [content]="poor"
              [placement]="'right'"
              [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>


        </ngx-datatable-column>


        <!-- totalClients -->
        <ngx-datatable-column
          *ngIf="col.prop === 'totalClients' && col.columnStatus"
          [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
          prop="totalClients">
          <ng-template
            let-row="row"
            let-totalClients="value"
            ngx-datatable-cell-template>
            <app-beautify-content
              [content]="totalClients"
              [placement]="'right'"
              [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>


        </ngx-datatable-column>
      </ng-container>
    </ngx-datatable>


  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Cross click')">
      {{ 'COMMON.CLOSE' | translate }}
    </button>
  </div>
</ng-template>
<!-- / Modal -->
