<core-card
  [actions]="[]"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus">
  <h4 class="card-title">
    {{ accessor.name | translate }} | 
    <span [ngClass]="{
      'bg-light-success': inservice === true,
      'bg-light-secondary': inservice === false
    }" style="font-size: 12px;">{{inservice?"In service":"Out Of Service"}}</span>
  </h4>
  <div class="card card-transaction overflow-hidden">
    <div
      *ngIf="xmppSupported"
      class="mh-1px d-flex flex-wrap align-content-start "
      style="padding: 1.5rem;padding-top:0;cursor:default;"
      [perfectScrollbar]>
      <div
        *ngFor="let item of xmppInfoItems"
        class="transaction-item align-items-start overflow-hidden px-25" 
        [ngStyle]="{'width': getWidth()}">
        <div class="media">
          <div class="media-body">
            <div [ngSwitch]="item.name">
                <div *ngSwitchCase="'uptimeseconds'">
                  <h6
                    class="transaction-title"
                    style="font-size: 12px;">
                    {{xmppInfoNameMapping[item.name]}}
                  </h6>
                  <small class="text-info">
                    {{item.reportedValue | secondToYearMonthDays : "second"}}
                  </small>
                </div>
                <div *ngSwitchDefault>
                  <h6
                    class="transaction-title"
                    style="font-size: 12px;">
                    {{xmppInfoNameMapping[item.name]}}
                  </h6>
                  <small class="text-info">{{item.reportedValue | number}}</small>
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="!xmppSupported" class="card-body overflow-auto business-card justify-content-start p-0">
      <span class="d-flex justify-content-center align-items-center" style="height: 100%;">Not Supported</span>
    </div>
  </div>
</core-card>
