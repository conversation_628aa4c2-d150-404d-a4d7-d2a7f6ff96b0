import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  name: 'formatIp'
})
export class FormatIpPipe implements PipeTransform {
  transform(ip: string): string {
    // 检查是否为 IPv4 格式
    if (/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(ip)) {
      return ip;
    }

    // 处理 ::ffff:xxx.xxx.xxx.xxx 格式的 IPv4 映射的 IPv6 地址
    if (/^::ffff:(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(ip)) {
      const match = ip.match(/^::ffff:(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/);
      return `${match[1]}.${match[2]}.${match[3]}.${match[4]}`;
    }

    // 检查是否为 ::ffff:xxxx:xxxx 格式的 IPv4 映射的 IPv6 地址
    if (/^::ffff:([0-9a-f]{1,4}):([0-9a-f]{1,4})$/i.test(ip)) {
      const match = ip.match(/^::ffff:([0-9a-f]{1,4}):([0-9a-f]{1,4})$/i);
      const firstPart = parseInt(match[1], 16);
      const secondPart = parseInt(match[2], 16);
      const firstOctet = firstPart >> 8;
      const secondOctet = firstPart & 0xff;
      const thirdOctet = secondPart >> 8;
      const fourthOctet = secondPart & 0xff;
      return `${firstOctet}.${secondOctet}.${thirdOctet}.${fourthOctet}`;
    }

    // 检查是否为长格式的 IPv4 映射的 IPv6 地址
    if (/^0{4}:0{4}:0{4}:0{4}:0{4}:ffff:([0-9a-f]{1,4}):([0-9a-f]{1,4})$/i.test(ip)) {
      const match = ip.match(/^0{4}:0{4}:0{4}:0{4}:0{4}:ffff:([0-9a-f]{1,4}):([0-9a-f]{1,4})$/i);
      const firstPart = parseInt(match[1], 16);
      const secondPart = parseInt(match[2], 16);
      const firstOctet = firstPart >> 8;
      const secondOctet = firstPart & 0xff;
      const thirdOctet = secondPart >> 8;
      const fourthOctet = secondPart & 0xff;
      return `${firstOctet}.${secondOctet}.${thirdOctet}.${fourthOctet}`;
    }

    // 处理 IPv6 压缩
    if (/^([0-9a-f]{1,4}:){7}[0-9a-f]{1,4}$/i.test(ip)) {
      const parts = ip.split(':');
      let zeroRuns = [];
      let currentRun = [];
      for (let i = 0; i < parts.length; i++) {
        if (parts[i] === '0000' || parts[i] === '0') {
          currentRun.push(i);
        } else {
          if (currentRun.length > 0) {
            zeroRuns.push(currentRun);
            currentRun = [];
          }
        }
      }
      if (currentRun.length > 0) {
        zeroRuns.push(currentRun);
      }
      if (zeroRuns.length > 0) {
        let longestRun = zeroRuns.reduce((a, b) => a.length > b.length ? a : b);
        let start = longestRun[0];
        let end = longestRun[longestRun.length - 1];
        let newParts = [];
        for (let i = 0; i < parts.length; i++) {
          if (i < start) {
            newParts.push(parts[i].replace(/^0+/, '') || '0');
          } else if (i > end) {
            if (i === end + 1) {
              newParts.push('');
            }
            newParts.push(parts[i].replace(/^0+/, '') || '0');
          }
        }
        if (start === 0) {
          newParts.unshift('');
        }
        if (end === parts.length - 1) {
          newParts.push('');
        }
        return newParts.join(':');
      }
    }

    // 正常 IPv6，不变
    return ip;
  }
}