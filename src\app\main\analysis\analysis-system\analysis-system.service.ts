import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { catchError, tap, share, map } from "rxjs/operators";
import { BehaviorSubject, Observable, Subject, throwError } from 'rxjs';
import { AuthenticationService } from 'app/auth/service';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { FileDownloadService } from 'app/main/commonService/file-download.service';
import { DatePipe } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class AnalysisSystemService {

  // Public
  public dayItems = [
    { key: 'DASHBOARD.AVGSESSIONS.LAST7DAYS', value: 7, icons: 'seven-days' },
    { key: 'DASHBOARD.AVGSESSIONS.LAST15DAYS', value: 15, icons: 'fifteen-days' },
    { key: 'DASHBOARD.AVGSESSIONS.LAST30DAYS', value: 30, icons: 'thirty-days' }
  ]
  public measureItems = [
    { key: 'AVG.', value: 'avg', icons: 'measure-avg' },
    { key: 'MAX.', value: 'max', icons: 'measure-max' },
    { key: 'MIN.', value: 'min', icons: 'measure-min' }
  ]
  public nodeListObservable: Observable<any>;
  public nodeList: Array<string> = [];
  public nodeListDataObservable: Observable<any>;
  public curNodeId: string;
  public curDays: number = this.dayItems[0].value;
  public curMeasure: any = this.measureItems[0].value;
  public blockUIStatus = true;
  public serverStatus: object;


  public onDaysChanged: Subject<number>;
  public onMeasureChanged: Subject<number>;
  public onNodeChanged: Subject<string>;
  public onServerStatusChanged: BehaviorSubject<any>;
  public onBlockUIStatusChanged: BehaviorSubject<any>;

  private key = "analysis-system-data"

  constructor(
    private _httpClient: HttpClient,
    private _authService: AuthenticationService,
    private _gridService: GridSystemService,
    private _fileDownloadService: FileDownloadService,
    private datePipe: DatePipe,
  ) {
    // Set the defaults
    this.onDaysChanged = new Subject();
    this.onMeasureChanged = new Subject();
    this.onNodeChanged = new Subject();
    this.onServerStatusChanged = new BehaviorSubject({});
    this.onBlockUIStatusChanged = new BehaviorSubject(false);
  }

  /**
     * Resolver
     *
     * @param {ActivatedRouteSnapshot} route
     * @param {RouterStateSnapshot} state
     * @returns {Observable<any> | Promise<any> | any}
     */
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
    return new Promise<void>((resolve, reject) => {
      if (this._authService.isAdmin) {
        this.initClusterNodes().subscribe({
          next: () => {
            resolve();
          },
          error: (err) => { }
        });
      } else {
        resolve();
      }
    });
  }

  /**
  * initClusterNodes
  */
  // initClusterNodes() {
  //   return new Promise((resolve, reject) => {
  //     if (this._authService.isAdmin) {
  //       // if (this.nodeList.length === 0) {
  //       this._httpClient.get('nbi/analysis/systemStatistics/cluster').subscribe((response: any) => {
  //         this.nodeList = response;
  //         this.curNodeId = response[0];
  //         this.onAnalysisNodeIdListChanged.next(this.nodeList);
  //         resolve(this.nodeList);
  //       }, reject);
  //       // } else {
  //       //   resolve(this.nodeList)
  //       // }
  //     } else {
  //       resolve([])
  //     }
  //   });
  // }

  initClusterNodes(): Observable<any> {
    if (this._authService.isAdmin) {
      if (this.nodeListDataObservable) {
        return this.nodeListDataObservable;
      } else {
        this.nodeListDataObservable = this._httpClient.get('nbi/analysis/systemStatistics/cluster').pipe(
          map((response: any) => {
            this.nodeList = response;
            let data = this._gridService.getComponentData(this.key)
            if (data?.node && this.nodeList.includes(data.node)) {
              this.curNodeId = data.node
            } else {
              this.curNodeId = response[0];
            }
            if (data?.days) {
              this.curDays = data.days
            }
            if (data?.measure) {
              this.curMeasure = data.measure
            }
            return {
              list: this.nodeList,
              node: this.curNodeId,
              days: this.curDays,
              measure:this.curMeasure
            }
          }),
          share(),
          catchError(error => throwError(() => error))
        )
        return this.nodeListDataObservable;
      }
    }
    return null
  }

  removeNodeListDataObservable() {
    this.nodeListDataObservable = null;
  }

  getDaysItem() {
    return this.dayItems
  }
  getMeasureItem() {
    return this.measureItems
  }

  changeDays(days) {
    this.curDays = days
    this._gridService.setComponentData(this.key, { node: this.curNodeId, days: days ,measure:this.curMeasure})
    this.onDaysChanged.next(days)
  }
  changeMeasure(measure) {
    this.curMeasure = measure
    this._gridService.setComponentData(this.key, { node: this.curNodeId, days: this.curDays ,measure: measure })
    this.onMeasureChanged.next(measure)
  }

  changeNode(node) {
    this.curNodeId = node
    this._gridService.setComponentData(this.key, { node: node, days: this.curDays })
    this.onNodeChanged.next(node)
  }

  /**
  * getAnalysisSystem
  */
  getAnalysisSystem(nodeId: string, days: number = this.curDays): Promise<any[]> {
    let current_day = new Date().toLocaleDateString();
    let date_today = (new Date(current_day)).toJSON();
    let params = new HttpParams()
      .set('days', days);
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/systemStatistics/' + nodeId + '/metrics/All', { params: params }).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  /**
  * getAnalysisSystemData   聚合查詢
  */
  getAnalysisSystemData(type: string, days: number, nodeId = this.curNodeId, measure= 'avg'): Promise<any> {
    days = days || this.curDays
    // console.log(days)
    // console.log(nodeId)
    let current_day = new Date().toLocaleDateString();
    let date_today = (new Date(current_day)).toJSON();
    let params = new HttpParams()
    .set('days', days)
    .set('measure', measure);
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/systemStatistics/' + nodeId + '/metricsAggregation/' + type, { params: params }).subscribe({
        next: (response: any) => {
          resolve(response)
        },
        error: reject
      });
    });
  }
  // /**
  // * getAnalysisSystemRawData  原始數據
  // */
  // getAnalysisSystemRawData(type: string, days: number, nodeId = this.curNodeId): Promise<any> {
  //   days = days || this.curDays
  //   // console.log(days)
  //   // console.log(nodeId)
  //   let current_day = new Date().toLocaleDateString();
  //   let date_today = (new Date(current_day)).toJSON();
  //   let params = new HttpParams()
  //     .set('days', days);
  //   return new Promise((resolve, reject) => {
  //     this._httpClient.get('nbi/analysis/systemStatistics/' + nodeId + '/metrics/' + type, { params: params }).subscribe({
  //       next: (response: any) => {
  //         resolve(response)
  //       },
  //       error: reject
  //     });
  //   });
  // }


   /**
  * getAnalysisSystemRawData  原始數據下載
  */
  // getAnalysisSystemRawData(type: string, days: number, nodeId = this.curNodeId): Promise<any> {
  //   days = days || this.curDays;
  //   let params = new HttpParams().set('days', days);
    
  //   return new Promise((resolve, reject) => {
  //     this._httpClient.get(`nbi/analysis/systemStatistics/${nodeId}/metrics/${type}`, { params: params }).subscribe({
  //       next: (response: any) => {
  //         // 取得數據後，執行下載
  //         const filePrefix = `NBI_${type}`;
  //         const formatType = 'json'; // 可改為 'csv' 或其他格式
  //         this._fileDownloadService.createLocalDownload(filePrefix, formatType, response);
  
  //         resolve(response);
  //       },
  //       error: reject
  //     });
  //   });
  // }
  getAnalysisSystemRawData(type: string, days: number, valueKey: string,nodeId = this.curNodeId,titleName: string,totalData?): Promise<any> {
    days = days || this.curDays;
    let params = new HttpParams().set('days', days);
  
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/analysis/systemStatistics/${nodeId}/metrics/${type}`, { params: params }).subscribe({
        next: (response: any) => {
          const currentDate = new Date();
          const formattedDate = this.datePipe.transform(currentDate, 'yyyyMMdd_HHmmss');
          const excelData = this.convertToExcelData(response, valueKey,nodeId, titleName, totalData);
          const filePrefix = `${titleName}_${formattedDate}`;
          // 使用現有的 exportToExcel 方法
          this._fileDownloadService.exportToExcel(excelData, filePrefix);
  
          resolve(response);
        },
        error: reject
      });
    });
  }
  
  
  private convertToExcelData(data: any[], valueKey: string,nodeId: string,titleName: string,totalData?): any[] {
    if (!data || data.length === 0) {
      // console.warn('No data available for Excel export.');
      return [];
    }
  
    return data.map(item => {
      let value: any;
      if (titleName === 'SystemStatistics-MemoryUtilization') {
        // 計算記憶體使用率
        const usedMem = item[valueKey]; // 假設 valueKey 對應的是 usedMem
        if (usedMem !== undefined && totalData) {
          // console.log(usedMem,"/",totalData)
          // value = `${((usedMem / totalData) * 100).toFixed(2)}(MB)`; // 計算百分比並保留兩位小數
          value = `${((usedMem / totalData) * 100).toFixed(2)}%(${(usedMem/ 1000000).toFixed(2)}MB)`; // 計算百分比並保留兩位小數
        } else {
          value = 'N/A';
        }
      }else if  (titleName === 'SystemStatistics-FreeMemory') {
        // 計算記憶體使用率
        const usedMem = item[valueKey]; // 假設 valueKey 對應的是 usedMem
        if (usedMem !== undefined && totalData) {
          // console.log(usedMem,"/",totalData)
          // value = `${((usedMem / totalData) * 100).toFixed(2)}(MB)`; // 計算百分比並保留兩位小數
          value = `${(((totalData-usedMem) / totalData) * 100).toFixed(2)}%(${((totalData-usedMem)  / 1000000).toFixed(2)}MB)`; // 計算百分比並保留兩位小數
        } else {
          value = 'N/A';
        }
      }else if  (titleName === 'SystemStatistics-DiskUtilization') {
        // 計算記憶體使用率
        const usedDisk = item[valueKey]; // 假設 valueKey 對應的是 usedMem
        if (usedDisk !== undefined && totalData) {
          // console.log(usedDisk,"/",totalData)
          // value = `${((usedDisk / totalData) * 100).toFixed(2)}(MB)`; // 計算百分比並保留兩位小數
          value = `${((usedDisk / totalData) * 100).toFixed(2)}%(${(usedDisk / 1000000000).toFixed(2)}GB)`; // 計算百分比並保留兩位小數
        } else {
          value = 'N/A';
        }
      }else if  (titleName === 'SystemStatistics-FreeDisk') {
        // 計算記憶體使用率
        const usedDisk = item[valueKey]; // 假設 valueKey 對應的是 usedDisk
        if (usedDisk !== undefined && totalData) {
          // console.log(usedDisk,"/",totalData)
          // value = `${((usedDisk / totalData) * 100).toFixed(2)}(MB)`; // 計算百分比並保留兩位小數
          value = `${(((totalData - usedDisk) / totalData) * 100).toFixed(2)}%(${((totalData - usedDisk) / 1000000000).toFixed(2)}GB)`; // 計算百分比並保留兩位小數
        } else {
          value = 'N/A';
        }
      }else {
        // 其他情況直接取值
        value = item[valueKey] !== undefined ? item[valueKey] : 'N/A';
      }
      // console.log("Item:", item); // 確認 item 內容
      // console.log("Value Key:", valueKey); // 確認 valueKey 是否正確
      // console.log("Extracted Value:", item[valueKey]); // 確認是否能正確取值
  
      return {
        time: item.established?this.datePipe.transform(item.established, 'yyyy/MM/dd HH:mm:ss') : '', // 確保時間欄位存在
        [nodeId]: value // 確保 value 欄位存在
      };
    });
  }
  

  

  getNodes(): Observable<any[]> {
    if (this.nodeListObservable) {
      return this.nodeListObservable;
    } else {
      this.nodeListObservable = this._httpClient.get('nbi/system/systemNodes/list').pipe(
        share(),
        catchError(error => throwError(() => error))
      );
      return this.nodeListObservable;
    }
  }

  getServerStatus(): Promise<any[]> {
    if (this._authService.isAdmin) {
      return new Promise((resolve, reject) => {
        this.onBlockUIStatusChanged.next(this.blockUIStatus)
        this._httpClient.get('nbi/analysis/systemStatistics/serverStatus').subscribe({
          next: (response: any) => {
            this.serverStatus = response
            this.onServerStatusChanged.next(this.serverStatus)
            resolve(response);
          },
          error: reject
        });
      });
    }
  }


  getBasicPM(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get('nbi/analysis/systemStatistics/pmAgentStatus').subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }
}
