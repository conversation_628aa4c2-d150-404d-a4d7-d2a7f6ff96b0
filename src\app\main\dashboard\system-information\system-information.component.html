<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus"
  (events)="emittedEvents($event)">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <div class="card card-transaction overflow-hidden">
    <div
      class="mh-1px d-flex flex-wrap align-content-start card-body pt-0"
      [perfectScrollbar]>
      <div
        *ngFor="let item of systemInfoItems"
        class="transaction-item align-items-start overflow-hidden px-25"
        [ngStyle]="{'width': getWidth()}">
        <div class="w-100">
          <div class="media-body">
            <h6
              class="transaction-title"
              style="font-size: 12px;">
              {{item.name}}
            </h6>
            <small>
              <app-beautify-content
                [content]="item.name == 'UpTime' ? (item.reportedValue | secondToYearMonthDays:'millisecond') : item.reportedValue"
                [placement]="'left'"
                [textClass]="item.reportedValue !== 'N/A' ? 'text-info ml-0 small-font-400' : 'text-secondary ml-0 small-font-400'"></app-beautify-content>
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</core-card>
