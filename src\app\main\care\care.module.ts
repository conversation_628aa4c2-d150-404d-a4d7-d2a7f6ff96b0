import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CoreCommonModule } from '@core/common.module';
import { CoreCardModule } from '@core/components/core-card/core-card.module';
import { SharedModule } from '../shared/shared.module';
import { ChartModule } from '../shared/chart/chart.module';
import { EchartsModule } from '../shared/echarts/echarts.module';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { PerfectScrollbarModule } from 'ngx-perfect-scrollbar-portable';
import { GridsterModule } from 'angular-gridster2';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { NgxDatatableModule } from '@almaobservatory/ngx-datatable';
import { AuthGuard, Navigation, ThemeStatus } from 'app/auth/helpers';
import { PersonalThemeService } from '@core/services/personal-theme.service';

import { CareComponent } from './care.component';
import { CareToolbarComponent } from './care-toolbar/care-toolbar.component';
import { CareGeneralInfoComponent } from './care-general-info/care-general-info.component';
import { CareMapComponent } from './care-map/care-map.component';
import { CareWifiClientsListComponent } from './care-wifi-clients-list/care-wifi-clients-list.component';
import { CareErrorStatusComponent } from './care-error-status/care-error-status.component';
import { CareButtonActionsComponent } from './care-button-actions/care-button-actions.component';
import { UpgradeDeviceComponent } from './care-button-actions/upgrade-device/upgrade-device.component';
import { RebootDeviceComponent } from './care-button-actions/reboot-device/reboot-device.component';
import { FactoryResetComponent } from './care-button-actions/factory-reset/factory-reset.component';
import { UploadLogComponent } from './care-button-actions/upload-log/upload-log.component';
import { LiveUpdateComponent } from './care-button-actions/live-update/live-update.component';
import { WifiClientsStatusModalComponent } from './wifi-clients-status-modal/wifi-clients-status-modal.component';

import { FormatIpPipe } from 'app/main/commonService/pipes/format-ip.pipe';

const routes = [
  {
    path: '',
    component: CareComponent,
    canActivate: [AuthGuard], //添加用户权限
    // canDeactivate: [ThemeStatus],
    data: { animation: 'CareComponent' },
    resolve: {
      // sts: SystemSettingService,
      pts: PersonalThemeService
    }
  },

];

@NgModule({
  declarations: [
    CareComponent,
    CareToolbarComponent,
    CareGeneralInfoComponent,
    CareMapComponent,
    CareWifiClientsListComponent,
    CareErrorStatusComponent,
    CareButtonActionsComponent,
    UpgradeDeviceComponent,
    RebootDeviceComponent,
    FactoryResetComponent,
    UploadLogComponent,
    WifiClientsStatusModalComponent,
    LiveUpdateComponent,
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    CoreCommonModule,
    CoreCardModule,
    SharedModule,
    NgSelectModule,
    PerfectScrollbarModule,
    GridsterModule,
    TranslateModule,
    GoogleMapsModule,
    NgxDatatableModule,
    ChartModule,
    EchartsModule,
  ],
  providers: [FormatIpPipe],
})
export class CareModule { }
