<core-card
  [actions]="['reload']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="loading"
  (events)="emittedEvents($event)">
  <h4 class="card-title"></h4>
  <div class="card-body">
    <main class="w-100 h-100 d-flex justify-content-between align-items-center">
      <div>
        <h2 class="font-weight-bolder mb-0">{{totalClients}}</h2>
        <p class="card-text">{{ title }}</p>
      </div>
      <div
        class="avatar bg-light-success p-50 m-0"
        (click)="modalOpenBD(modalBD)">
        <div class="avatar-content">
          <i
            data-feather="wifi"
            class="font-medium-5"></i>
        </div>
      </div>
    </main>
  </div>
</core-card>
<!-- Modal -->
<ng-template
  #modalBD
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel4">
      {{'DASHBOARD.CLIENTS' | translate}}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <div aria-hidden="true">&times;</div>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>

      <ngx-datatable
        #tableRowDetails
        appDatatableRecalculate
        [datatable]="tableRowDetails"
        class="bootstrap core-bootstrap"
        [rows]="clientsTable"
        [rowHeight]="37"
        [headerHeight]="45"
        [minRows]="5"
        [footerHeight]="50"
        [limit]="selectedOption"
        [scrollbarH]="true"
        [columnMode]="ColumnMode.force">
        <ng-container *ngFor="let col of columnSelectOptions">
          
          <!-- productName -->
          <ngx-datatable-column
          *ngIf="col.prop === 'name' && col.columnStatus"
          name="{{ col.translate | translate }}"
          prop="name"
          [width]="150"
          [draggable]="false">
          <ng-template
            let-column="column"
            let-row="row"
            let-name="value"
            ngx-datatable-cell-template>
            <app-beautify-content
                  [content]="name"
                  [placement]="'right'"
                  [textClass]="'text-truncate'"></app-beautify-content>
          </ng-template>
          </ngx-datatable-column>
          <!-- aps -->
          <ngx-datatable-column
          *ngIf="col.prop === 'aps' && col.columnStatus"
            [width]="col.width"
            name="{{ col.translate | translate }}"
            prop="aps"
          [draggable]="false">
            <ng-template
              let-column="column"
              let-row="row"
              let-aps="value"
              ngx-datatable-cell-template>
              <app-beautify-content
                    [content]="aps"
                    [placement]="'right'"
                    [textClass]="'text-truncate'"></app-beautify-content>
            </ng-template>
          </ngx-datatable-column>
          <!-- onlineAps -->
          <ngx-datatable-column
          *ngIf="col.prop === 'onlineAps' && col.columnStatus"
            [width]="col.width"
          [draggable]="false"
            name="{{ col.translate | translate }}"
            prop="onlineAps">
            <ng-template
              let-row="row"
              let-onlineAps="value"
              ngx-datatable-cell-template>
              <app-beautify-content
                    [content]="onlineAps"
                    [placement]="'right'"
                    [textClass]="'text-truncate'"></app-beautify-content>
            </ng-template>
          </ngx-datatable-column>
          <!-- excellent -->
          <ngx-datatable-column
          *ngIf="col.prop === 'excellent' && col.columnStatus"
            [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
            prop="excellent">
            <ng-template
              let-row="row"
              let-excellent="value"
              ngx-datatable-cell-template>
              <app-beautify-content
                    [content]="excellent"
                    [placement]="'right'"
                    [textClass]="'text-truncate'"></app-beautify-content>
            </ng-template>

          </ngx-datatable-column>
          <!-- good -->
          <ngx-datatable-column
          *ngIf="col.prop === 'good' && col.columnStatus"
            [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
            prop="good">
            <ng-template
              let-row="row"
              let-good="value"
              ngx-datatable-cell-template>
              <app-beautify-content
                    [content]="good"
                    [placement]="'right'"
                    [textClass]="'text-truncate'"></app-beautify-content>
            </ng-template>
          </ngx-datatable-column>
          <!-- poor -->
          <ngx-datatable-column
          *ngIf="col.prop === 'poor' && col.columnStatus"
            [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
            prop="poor">
            <ng-template
              let-row="row"
              let-poor="value"
              ngx-datatable-cell-template>
              <app-beautify-content
                    [content]="poor"
                    [placement]="'right'"
                    [textClass]="'text-truncate'"></app-beautify-content>
            </ng-template>
            
          </ngx-datatable-column>
          
          <!-- totalClients -->
          <ngx-datatable-column
          *ngIf="col.prop === 'totalClients' && col.columnStatus"
            [width]="col.width"
          [draggable]="false"
          name="{{ col.translate | translate }}"
            prop="totalClients">
            <ng-template
              let-row="row"
              let-totalClients="value"
              ngx-datatable-cell-template>
              <app-beautify-content
                    [content]="totalClients"
                    [placement]="'right'"
                    [textClass]="'text-truncate'"></app-beautify-content>
            </ng-template>
            
          </ngx-datatable-column>
        </ng-container>
      </ngx-datatable>

  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Cross click')">
      {{ 'COMMON.CLOSE' | translate }}
    </button>
  </div>
</ng-template>
<!-- / Modal -->
