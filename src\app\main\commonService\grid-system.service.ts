import { Injectable } from '@angular/core';
import {
  GridsterConfig,
  GridsterItem,
  GridType,
  CompactType,
} from 'angular-gridster2';
import { Router } from '@angular/router';
import { BehaviorSubject, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import isEmpty from 'lodash/isEmpty';
import isObject from 'lodash/isObject';
import { StorageService } from 'app/main/commonService/storage.service';
import { filter } from 'rxjs/operators';
import { NavigationEnd } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class GridSystemService {
  gridsterHeight;
  pageResize = false;
  personalThemeKey: string;
  personalTheme: any = { theme: [] };
  tempPersonalTheme: Array<any> = [];
  mProductName;
  gridsterItemComponentInfo: any = {}
  public onGridsterItemComponentInfoChanged: BehaviorSubject<any>;
  public onEditModeChanged: BehaviorSubject<boolean>;
  gridOption: GridsterConfig = {
    draggable: {
      enabled: false,
      ignoreContent: true,
      dragHandleClass: 'card-header'
    },
    resizable: {
      enabled: false,
    },
    swap: true,
    pushItems: true,
    pushDirections: { north: false, east: false, south: true, west: false },
    disableScrollHorizontal: true,
    displayGrid: 'none',
    mobileBreakpoint: 1200,
    compactType: 'compactUp',
    gridType: 'verticalFixed',
    // setGridSize: true,
    fixedRowHeight: 28,
    keepFixedHeightInMobile: true,
    useBodyForBreakpoint: true,
    outerMargin: false,
    margin: 10,
    minCols: 36,
    maxCols: 36,
    itemChangeCallback: this.onWidgetChange.bind(this),
    itemResizeCallback: this.onWidgetResize.bind(this)
  };
  gridOption$: GridsterConfig = new BehaviorSubject<any>(this.gridOption);
  configInit = new Subject()
  castGridOption$ = this.gridOption$.asObservable();
  navAndFooterHeight;

  private personalThemeUrl: string = 'nbi/addOn/personalTheme';
  private separator: string = "***";
  constructor(
    private _router: Router,
    private _httpClient: HttpClient,
    private _storageService: StorageService,
  ) {
    this.onGridsterItemComponentInfoChanged = new BehaviorSubject({});
    this.onEditModeChanged = new BehaviorSubject(false);
    this._router.events.pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.clearComponentReferences();
      });
  }

  /**
   * 清理组件引用，防止内存泄漏
   */
  clearComponentReferences(): void {
    for (const key in this.gridsterItemComponentInfo) {
      if (this.gridsterItemComponentInfo[key] && this.gridsterItemComponentInfo[key].widget) {
        this.gridsterItemComponentInfo[key].widget = null;
      }
    }
    this.gridsterItemComponentInfo = {};
    this.onGridsterItemComponentInfoChanged.unsubscribe();
    this.onGridsterItemComponentInfoChanged = new BehaviorSubject({});
  }

  /**
   * Init personal theme cache
   */
  init(personalThemeObj: any = {}) {
    if (personalThemeObj.key && personalThemeObj.key !== this.personalThemeKey) {
      this.personalThemeKey = personalThemeObj.key;
      this.personalTheme = personalThemeObj.personalTheme;
      this.tempPersonalTheme = this.personalTheme.theme ? cloneDeep(this.personalTheme.theme) : [];
      if (this.personalTheme.config) {
        this.configInit.next(this.personalTheme.config)
      }
    }
  }

  /**
   * Clear personal theme cache
   */
  clear() {
    this.unEditMode();
    this.personalThemeKey = undefined;
    this.personalTheme = { theme: [] };
    this.tempPersonalTheme = [];
  }

  /**
   * Change grid configuration to editable if Edit Mode is toggled to true
   */
  editMode() {
    this.personalTheme.theme = cloneDeep(this.tempPersonalTheme);
    const option = this.gridOption;
    option.displayGrid = 'always';
    option.draggable.enabled = true;
    option.resizable.enabled = true;
    option.api.optionsChanged();
    this.onEditModeChanged.next(true);
    this.gridOption$.next(option);
  };

  /**
   * Change gird configuration to uneditable if Edit Mode is toggled to false
   */
  unEditMode() {
    const option = this.gridOption;
    option.displayGrid = 'none';
    option.draggable.enabled = false;
    option.resizable.enabled = false;
    if (option.api) {
      option.api.optionsChanged();
    }
    this.onEditModeChanged.next(false);
    this.gridOption$.next(option)
  };

  public set productName(v: string) {
    this.mProductName = v;
  }

  public get isEditMode() {
    return this.personalTheme.config && this.personalTheme.config.layout.editMode;
  }

  /**
   * Broadcast the latest grid configuration 
   */
  getGridOption() {
    return this.castGridOption$
  }

  /**
   * Store user's edition in the tempPersonalTheme
   * @param gridsterItem 
   * @param gridsterItemComponent 
   */
  onWidgetChange(gridsterItem, gridsterItemComponent) {
    // const currentUrl = this._router.url;
    const currentUrl = this.getPersonalThemeUrl();
    let tempPersonalTheme = this.tempPersonalTheme;
    // console.info('tempPersonalTheme:', tempPersonalTheme);
    let samePageurl = tempPersonalTheme.find(tpt => tpt.pageUrl == currentUrl);
    if (samePageurl) {
      const widgetPreference = samePageurl.widgets;
      const sameComponentId = widgetPreference.find(wp => wp.componentId == gridsterItem.componentId);
      if (sameComponentId) {
        sameComponentId.name = gridsterItem.name;
        sameComponentId.cols = gridsterItem.cols;
        sameComponentId.rows = gridsterItem.rows;
        sameComponentId.x = gridsterItem.x;
        sameComponentId.y = gridsterItem.y;
        sameComponentId.copy = gridsterItem.copy;
        sameComponentId.copyed = gridsterItem.copyed;
        sameComponentId.hidden = gridsterItem.hidden;
        sameComponentId.data = gridsterItem.data;
        sameComponentId.color = gridsterItem.color;
      } else {
        widgetPreference.push(
          {
            componentId: gridsterItem.componentId,
            name: gridsterItem.name,
            cols: gridsterItem.cols,
            rows: gridsterItem.rows,
            x: gridsterItem.x,
            y: gridsterItem.y,
            copy: gridsterItem.copy,
            copyed: gridsterItem.copyed,
            hidden: gridsterItem.hidden,
            data: gridsterItem.data,
            color: gridsterItem.color,
          }
        );
      }
    } else {
        samePageurl = {
          pageUrl: currentUrl,
          widgets: [
            {
              componentId: gridsterItem.componentId,
              name: gridsterItem.name,
              cols: gridsterItem.cols,
              rows: gridsterItem.rows,
              x: gridsterItem.x,
              y: gridsterItem.y,
              copy: gridsterItem.copy,
              copyed: gridsterItem.copyed,
              hidden: gridsterItem.hidden,
              data: gridsterItem.data,
              color: gridsterItem.color,
            }
          ]
        };
        tempPersonalTheme.push(samePageurl);     
    }
  };

  onWidgetResize(gridsterItem, gridsterItemComponent) {
    this.gridsterItemComponentInfo[gridsterItemComponent.item.componentId] = { width: parseInt(gridsterItemComponent.width), componentId: gridsterItemComponent.item.componentId, widget: gridsterItemComponent }
    this.onGridsterItemComponentInfoChanged.next(this.gridsterItemComponentInfo);
  }

  saveTempPersonalTheme(currentUrl, widgets) {
    let samePageurl = {
      pageUrl: currentUrl,
      widgets: []
    };
    widgets.forEach(widget => {
      if (widget.render && !widget.hidden) {
        samePageurl.widgets.push({
          componentId: widget.componentId,
          name: widget.name,
          cols : widget.cols,
          rows : widget.rows,
          x : widget.x,
          y : widget.y,
          copy : widget.copy,
          copyed : widget.copyed,
          hidden : widget.hidden,
          data : widget.data,
          color : widget.color
        })
      }
    });
    this.tempPersonalTheme.push(samePageurl);    
  }

  getPersonalThemeUrl() {
    let url = this._router.url;
    if (this.mProductName && /\/[a-z0-9]{24}\//.test(url)) {
      return url.replace(/\/[a-z0-9]{24}\//, `/${this.mProductName}/`);
    } else if (url === '/care' && this.mProductName) {
      return url + '/' + this.mProductName
    } else if (url.includes('/user-manual')) {
      return '/user-manual'
    }
    return url;
  }

  private updateStatus(theme, currentUrl, changed) {
    let samePageUrl = theme.find(tpt => tpt.pageUrl == currentUrl);
    if (samePageUrl) {
      const widgetPreference = samePageUrl.widgets;
      changed.forEach(item => {
        const sameComponentId = widgetPreference.find(wp => wp.componentId == item.componentId);
        if (sameComponentId) {
          // if (sameComponentId.hidden !== item.hidden) {
          sameComponentId.name = item.name;
          sameComponentId.cols = item.cols;
          sameComponentId.rows = item.rows;
          sameComponentId.x = item.x;
          sameComponentId.y = item.y;
          sameComponentId.copy = item.copy;
          sameComponentId.copyed = item.copyed;
          sameComponentId.hidden = item.hidden;
          sameComponentId.data = item.data;
          sameComponentId.color = item.color;
          // }
        } else {
          widgetPreference.push(item);
        }
      });
    } else {
      samePageUrl = {
        pageUrl: currentUrl,
        widgets: changed
      }
      theme.push(samePageUrl)
    }
    return samePageUrl;
  }

  public get config(): any {
    return this.personalTheme && this.personalTheme.config;
  }

  public getComponentData(id: string) {
    if (this.personalTheme && this.personalTheme.data && this.personalTheme.data[id]) {
      return this.personalTheme.data[id];
    }
    return null;
  }

  public setComponentData(id: string, data: any) {
    if (!this.personalTheme.hasOwnProperty('data')) {
      this.personalTheme.data = {};
    }
    this.personalTheme.data[id] = data;
    let obj = {};
    obj[`data.${id}`] = data;
    this._savePersonalTheme(obj);
  }

  diffBetweenObjects(object, base) {
    let result = {};
    const changes = function (object, base, baseKey) {
      for (var key in object) {
        if (key !== 'editMode' && !isEqual(object[key], base[key])) {
          let newKey = baseKey + '.' + key;
          if (isObject(object[key]) && isObject(base[key])) {
            changes(object[key], base[key], newKey);
          } else {
            result[newKey] = object[key];
          }
        }
      }
    }
    changes(object, base, 'config');
    return result;
  }

  isWidgetsDiff(widgets1, widgets2) {
    if (widgets1.length !== widgets2.length) {
      return true;
    }
    return widgets1.some(widget1 => {
      let widget2 = widgets2.find(item => widget1.componentId == item.componentId);
      if (widget2) {
        return !isEqual(widget1, widget2);
      } else {
        return true;
      }
    })
  }

  notSave() {
    if (this.isEditMode) {
      let currentUrl = this.getPersonalThemeUrl();
      let originalTheme = this.personalTheme.theme.find(tpt => tpt.pageUrl == currentUrl);
      let currentTheme = this.tempPersonalTheme.find(tpt => tpt.pageUrl == currentUrl);
      if (originalTheme === undefined && currentTheme === undefined) {
        return false;
      } else if (originalTheme && currentTheme) {
        return this.isWidgetsDiff(originalTheme.widgets, currentTheme.widgets)
      } else {
        return true;
      }
    } else {
      return false;
    }
  }

  resetChange() {
    this.tempPersonalTheme = cloneDeep(this.personalTheme.theme);
  }

  /**
   * Save skin, language... into personal theme
   */
  savePersonalConfig(config) {
    // Set config to local storage if enableLocalStorage parameter is true
    if (config.layout.enableLocalStorage) {
      // localStorage.setItem('config', JSON.stringify(config));
      this._storageService.set('config', config);
    }
    if (!this.check()) {
      return;
    }
    let changes = isEmpty(this.personalTheme.config) ? { config } : this.diffBetweenObjects(config, this.personalTheme.config);
    this.personalTheme.config = config;
    if (!isEmpty(changes)) {
      this._savePersonalTheme(changes);
    }
  }

  saveComponentStatus(changed: Array<any>) {
    if (!this.check()) {
      return;
    }
    this.updateStatus(this.tempPersonalTheme, this.getPersonalThemeUrl(), changed);
  }

  getPersonalColumns(title) {
    const currentUrl = this.getPersonalThemeUrl();
    let key = currentUrl + this.separator + title;
    if (this.personalTheme.columns) {
      let item = this.personalTheme.columns.find(item => {
        return item.key === key;
      });
      if (item) {
        return item;
      }
    }
    return {};
  }

  savePersonalColumns(title, tableOption?) {
    if (!this.check()) {
      return;
    }
    const currentUrl = this.getPersonalThemeUrl();
    let key = currentUrl + this.separator + title;
    let param = {};
    if (Array.isArray(tableOption)) {
      param = { key, tableOption };
    } else if (Object.prototype.toString.call(tableOption) === '[object Object]') {
      param = { key, option: tableOption };
    }
    if (this.personalTheme.columns) {
      let item = this.personalTheme.columns.find(item => {
        return item.key === key;
      });
      if (item) {
        if (param.hasOwnProperty('option')) {
          item.option = Object.assign({}, tableOption);
        } else {
          item.tableOption = tableOption || item.tableOption;
        }
      } else {
        this.personalTheme.columns.push(param);
      }
    } else {
      this.personalTheme.columns = [param];
    }
    return this._savePersonalTheme({ columns: [param] })
  }

  removeCopyedWidget(personalTheme) {
    personalTheme.forEach(pageTheme => {
      if (pageTheme.widgets && pageTheme.widgets.length) {
        pageTheme.widgets = pageTheme.widgets.filter(widget => !widget.copyed || !widget.hidden)
      }
    })
  }

  /**
   * Save temp personal theme with changed widgets
   */
  savePersonalTheme(currPageTheme?) {
    if (currPageTheme) {
      this.removeCopyedWidget([currPageTheme]);
      return this._savePersonalTheme({ theme: [currPageTheme] });
    } else {
      this.removeCopyedWidget(this.tempPersonalTheme);
      this.personalTheme.theme = cloneDeep(this.tempPersonalTheme);
      return this._savePersonalTheme({ theme: this.tempPersonalTheme });
    }
  };

  /**Change personal theme to default*/
  resetPersonalTheme(allPage = false) {
    return allPage ? this.resetAllPersonalTheme() : this.resetCurrentPersonalTheme()
  }

  resetCurrentPersonalTheme() {
    const currentUrl = this.getPersonalThemeUrl();
    this.tempPersonalTheme = this.tempPersonalTheme.filter(tpt => tpt.pageUrl !== currentUrl);
    this.personalTheme.theme = cloneDeep(this.tempPersonalTheme);
    if (this.personalTheme.columns) {
      this.personalTheme.columns = this.personalTheme.columns.filter(item => !item.key.startsWith(currentUrl))
    }
    return this._savePersonalTheme({ theme: this.personalTheme.theme, columns: this.personalTheme.columns });
  }

  resetAllPersonalTheme() {
    this.tempPersonalTheme = [];
    this.personalTheme.theme = [];
    this.personalTheme.columns = [];
    return this._savePersonalTheme({ theme: this.personalTheme.theme, columns: this.personalTheme.columns });
  };

  private check() {
    // return localStorage.hasOwnProperty('currentUser') && this.personalThemeKey;
    return this._storageService.hasOwnProperty('currentUser') && this.personalThemeKey;
  }

  private _personalThemeSubject = new BehaviorSubject<any>(null);
  personalTheme$ = this._personalThemeSubject.asObservable();

  private notifyPersonalThemeChange() {
    // Trigger the next value on the BehaviorSubject
    this._personalThemeSubject.next(true);
  }


  private _savePersonalTheme(param) {
    return new Promise((resolve, reject) => {
      // if (localStorage.hasOwnProperty('currentUser')) {
      if (this._storageService.hasOwnProperty('currentUser')) {
        this._httpClient.put(this.personalThemeUrl, param, { 'responseType': 'text' }).subscribe({
          next: (response: any) => {
            this.notifyPersonalThemeChange();
            resolve(response);
          },
          error: reject
        });
      } else {
        resolve(null);
      }
    }).catch(err => { });
  }
}