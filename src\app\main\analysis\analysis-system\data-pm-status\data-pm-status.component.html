<core-card
  [actions]="[]"
  [componentId]="accessor.componentId"
  [blockUIStatus]="blockUIStatus">
  <h4 class="card-title">{{ accessor.name | translate }} | 
    <span [ngClass]="{
      'bg-light-success': inservice === true,
      'bg-light-secondary': inservice === false
    }" style="font-size: 12px;">{{inservice?"In service":"Out Of Service"}}</span>
  </h4>
  <div class="card card-transaction overflow-hidden">
    <div
      class="mh-1px d-flex flex-wrap  align-content-start "
      style="padding: 1.5rem;padding-top:0;cursor:default;"
      [perfectScrollbar]>
      <div
        *ngFor="let item of pmInfoItems"
        class="transaction-item align-items-start " [ngStyle]="{'width': getWidth()}">
        <div class="media">
          <div class="media-body">
            <div [ngSwitch]="item.name">
              <div *ngSwitchCase="'freeSpace'">
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{pmInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info">{{item.reportedValue | bytesToSize}}</small>
              </div>
              <div *ngSwitchCase="'uptime'">
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{pmInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info">
                  {{item.reportedValue | secondToYearMonthDays : "second"}}
                </small>
              </div>
              <div *ngSwitchCase="'totalSpace'">
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{pmInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info">{{item.reportedValue | bytesToSize}}</small>
              </div>
              <div *ngSwitchCase="'freemem'">
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{pmInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info">{{item.reportedValue | bytesToSize}}</small>
              </div>
              <div *ngSwitchCase="'totalmem'">
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{pmInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info">{{item.reportedValue | bytesToSize}}</small>
              </div>
             
              <div *ngSwitchCase="'date'">
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{pmInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info">{{item.reportedValue | date:'MM/dd/yy, HH:mm:ss'}}</small>
              </div>
              <div *ngSwitchDefault>
                <h6
                  class="transaction-title"
                  style="font-size: 12px;">
                  {{pmInfoNameMapping[item.name]}}
                </h6>
                <small class="text-info">{{item.reportedValue || '-'}}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</core-card>
