import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
import { DashboardService } from 'app/main/dashboard/dashboard.service';
import { GoogleMap, MapMarker, MapInfoWindow } from '@angular/google-maps';
import { MarkerClusterer, Cluster } from '@googlemaps/markerclusterer';
import 'leaflet.markercluster';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { StorageService } from 'app/main/commonService/storage.service';
import { GoogleMapService } from 'app/main/commonService/google-map.service';
import { UserService } from 'app/auth/service/user.service';
import { NominatimService } from 'app/main/commonService/nominatim.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { DeviceInfoService } from 'app/main/devices/device-info/device-info.service';
import { DataStoreService } from 'app/main/commonService/data-store.service';
import * as L from 'leaflet';
import { timer, Subscription, BehaviorSubject } from 'rxjs';
import { NotificationsService } from 'app/layout/components/navbar/navbar-notification/notifications.service';
import { forkJoin, from, Observable } from 'rxjs';
import { switchMap, map } from 'rxjs/operators'
import { FormatIpPipe } from 'app/main/commonService/pipes/format-ip.pipe';
const iconRetinaUrl = 'assets/marker-icon-2x.png';
const iconUrl = 'assets/marker-icon.png';
const shadowUrl = 'assets/marker-shadow.png';
const iconDefault = L.icon({
  iconRetinaUrl,
  iconUrl,
  shadowUrl,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  tooltipAnchor: [16, -28],
  shadowSize: [41, 41]
});
L.Marker.prototype.options.icon = iconDefault;

@UntilDestroy()
@Component({
  selector: 'app-dashboard-device-map',
  templateUrl: './dashboard-device-map.component.html',
  styleUrl: './dashboard-device-map.component.scss'
})
export class DashboardDeviceMapComponent extends Unsubscribe implements OnInit, AfterViewInit {
  @ViewChild('osmmap', { static: true }) osmmap: ElementRef;
  @ViewChild('googleMap', { static: false }) googlemap!: GoogleMap;
  @Input() accessor: any;
  public blockUIStatus = false;
  public basicInfo: any
  public markerZoom = 18;
  public streetZoom = 12;
  public infoContent: any
  public device_apiLoaded: boolean;
  public apiKey;
  public mapType: any
  private geocoder: google.maps.Geocoder;
  public get noPermission(): boolean {
    return !this._authenticationService.check('device', 'networkLocation', 'write');
  }

  constructor(
    private _dashboardService: DashboardService,
    private route: ActivatedRoute,
    private _toastrUtilsService: ToastrUtilsService,
    httpClient: HttpClient,
    private translateService: TranslateService,
    private _storageService: StorageService,
    private cdr: ChangeDetectorRef,
    private _userService: UserService,
    private nominatimService: NominatimService,
    private _authenticationService: AuthenticationService,
    private router: Router,
    private _deviceInfoService: DeviceInfoService,
    private _dataStoreService: DataStoreService,
    private googleMapsService: GoogleMapService,
    private _notificationsService: NotificationsService,
    private formatIpPipe: FormatIpPipe
  ) {
    super();
    this.mapType = this._storageService.get('locationType') ? this._storageService.get('locationType').toString() : this._userService.getInformation().pipe(untilDestroyed(this)).subscribe((response: any) => { return this._storageService.get('locationType') })
    // console.log(this.mapType)
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CONFIRMSAVELOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMSAVELOCA');
      this.DOSAVELOCA = this.translateService.instant('DEVICES.ACTION.DOSAVELOCA');
      this.CONFIRMRESETLOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMRESETLOCA');
      this.DORESETLOCA = this.translateService.instant('DEVICES.ACTION.DORESETLOCA');
      this.SAVESUCC = this.translateService.instant('DEVICES.ACTION.SAVESUCC');
      this.SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
      this.SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
      this.OSMERROR = this.translateService.instant('DEVICES.OSMERROR');
      this.GOOGLEMAPERROR = this.translateService.instant('DEVICES.GOOGLEMAPERROR');
    })
  }

  public deviceId: string;
  public serialNumber: string;
  public ip: string;
  public productName: string;
  public status: string;
  public lat: any;
  public lng: any;
  public LocationCenter: google.maps.LatLngLiteral = { lat: 27.835577, lng: 20.752790 };
  public cloneRes: any
  public CONFIRMSAVELOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMSAVELOCA');
  public DOSAVELOCA = this.translateService.instant('DEVICES.ACTION.DOSAVELOCA');
  public CONFIRMRESETLOCA = this.translateService.instant('DEVICES.ACTION.CONFIRMRESETLOCA');
  public DORESETLOCA = this.translateService.instant('DEVICES.ACTION.DORESETLOCA');
  public SAVESUCC = this.translateService.instant('DEVICES.ACTION.SAVESUCC');
  public SAVEFAIL = this.translateService.instant('DEVICES.ACTION.SAVEFAIL');
  public OSMERROR = this.translateService.instant('DEVICES.OSMERROR');
  public GOOGLEMAPERROR = this.translateService.instant('DEVICES.GOOGLEMAPERROR');
  public ismapErr: boolean = false


  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getMapRes();
        break;
    }
  };

  isNumber(value) {
    if (value && /^\d+$/.test(value)) {
      return true;
    }
    return false;
  }

  public mapOptions = {
    mapTypeControl: false,
    scaleControl: false,
    streetViewControl: false,
    rotateControl: false,
    fullscreenControl: true,
    icon: null,
    scrollwheel: false,
    zoomControl: true,
    gestureHandling: "cooperative"
  }

  defaultValues = {
    lastIp: "",
    firmwareVersion: "",
    mac: "",
    lastConnected: "",
    TimeZone: "",
    productName: "",
    provisioningType: "",
    sn: "",
    status: "Offline",
    isActive: false,
    label: "",
    tags: [],
    ModelName: "",
    protocol: "",
    dev_Location: {},
    deviceId: "",
    productId: "",
    userDefindLat: undefined,
    userDefindLng: undefined,
    countryCode: "",
    postCode: "",
    country: "",
    city: "",
    suburb: "",
    road: ""
  };


  getMapRes(): void {
    this.blockUIStatus = true;
    forkJoin([
      this.getLocationRes(),
      this.getNotificationMessage(),
    ]).pipe(untilDestroyed(this)).subscribe({
      next: ([locationData, alarmData]) => {
        // console.log('locationData', locationData)
        // console.log('alarmData', alarmData)

        let merageData = this.mergeAlarmData(alarmData, locationData)
        // console.log('merageData', merageData)

        if (this.mapType === '0') {
          this.initGoogleMap(merageData);
        } else if (this.mapType === '1') {
          this.osmInitMap(merageData);
        }

      },
      error: () => {
        this.blockUIStatus = false;
      },
      complete: () => {
        this.blockUIStatus = false;
      }
    });
  }


  getLocationRes(): Observable<any> {
    return from(
      this._dashboardService.getLocation().then((res: any) => {
        // console.log(res);
        let mergedData
        if (res && res.length > 1000) {
          // 取 lastConnected 最新的前 1000 筆
          const sortedData = res.sort((a, b) => {
            const dateA = new Date(a.lastConnected).getTime();
            const dateB = new Date(b.lastConnected).getTime();
            return dateB - dateA; // 由新到舊排序
          });
          const latestData = sortedData.slice(0, 1000);
          mergedData = latestData.map(item => {
            return Object.keys(this.defaultValues).reduce((acc, key) => {
              acc[key] = item[key] ?? this.defaultValues[key];
              return acc;
            }, {});
          });
        } else {
          mergedData = res.map(item => {
            return Object.keys(this.defaultValues).reduce((acc, key) => {
              acc[key] = item[key] ?? this.defaultValues[key];
              return acc;
            }, {});
          });
        }

        // console.log('merageData', mergedData)

        return mergedData;
      }).catch(error => {
        console.error(error);
        return [];
      })
    );
  }


  getNotificationMessage(): Observable<any> {
    return this._notificationsService.getNotificationsData().pipe(
      map((res: any) => {
        const result: { [key: string]: { [key: string]: number } } = {};

        res.data.forEach(({ serialNumber, PerceivedSeverity }) => {
          if (!result[serialNumber]) {
            result[serialNumber] = {};
          }
          result[serialNumber][PerceivedSeverity] =
            (result[serialNumber][PerceivedSeverity] || 0) + 1;
        });

        const finalResult = Object.keys(result).map((serial) => ({
          serialNumber: serial,
          ...result[serial],
        }));

        // console.log('finalResult', finalResult);
        return finalResult;
      })
    );
  }

  mergeAlarmData(alarmData, mergeData) {
    const alarmMap: { [key: string]: { [key: string]: number } } = {};
    alarmData.forEach((alarm) => {
      alarmMap[alarm.serialNumber] = { ...alarm };
      delete alarmMap[alarm.serialNumber].serialNumber;
    });

    // 遍歷 mergeData，根據 sn 找到對應的 alarm 資料並加入
    const updatedMergeData = mergeData.map((item) => {
      if (alarmMap[item.sn]) {
        return { ...item, alarm: alarmMap[item.sn] };
      }
      return item; // 沒有對應的 alarm 資料則保持不變
    });

    return updatedMergeData;
  }

  markers: any[] = [];
  oms: any;
  markerCluster: MarkerClusterer | undefined;
  public address: any
  private projectionReady = false;
  public mapCenter: google.maps.LatLngLiteral = { lat: 27.835577, lng: 20.752790 }
  public mapZoom = 2

  initGoogleMap(mergedData: any): void {
    const OverlappingMarkerSpiderfier = require('overlapping-marker-spiderfier');

    // 確認 Google Map 是否初始化
    if (!this.googlemap || !this.googlemap.googleMap) {
      console.error('Google Map is not initialized.');
      return;
    }

    const mapInstance = this.googlemap.googleMap;

    // 驗證 mapInstance 類型
    if (!(mapInstance instanceof google.maps.Map)) {
      console.error('Invalid Google Map instance.');
      return;
    }

    // 確保地圖的投影已就緒
    const ensureProjectionReady = (): Promise<void> => {
      return new Promise<void>((resolve) => {
        if (this.projectionReady) {
          resolve();
        } else {
          google.maps.event.addListenerOnce(mapInstance, 'idle', () => {
            this.projectionReady = true;
            resolve();
          });
        }
      });
    };

    const mapDiv = mapInstance.getDiv();
    if (mapDiv) {
      mapDiv.addEventListener('fullscreenchange', () => {
        const isFullscreen = !!document.fullscreenElement;
        mapInstance.setOptions({
          scrollwheel: isFullscreen,
        });
      });
    }

    // 初始化標記和 OverlappingMarkerSpiderfier
    ensureProjectionReady()
      .then(() => {
        this.setupOMSAndMarkers(mapInstance, mergedData, OverlappingMarkerSpiderfier);
      })
      .catch((error) => {
        console.error('Error ensuring projection readiness:', error);
      });
  }

  private setupOMSAndMarkers(
    mapInstance: google.maps.Map,
    mergedData: any[],
    OverlappingMarkerSpiderfier: any
  ) {
    const markerObjects: google.maps.Marker[] = [];
    const bounds = new google.maps.LatLngBounds();

    // 初始化OMS
    const oms = new OverlappingMarkerSpiderfier(mapInstance, {
      markersWontMove: true,
      markersWontHide: true,
      keepSpiderfied: true,
      circleSpiralSwitchover: 100,
      nearbyDistance: 80,
    });

    oms.addListener('click', (marker, event) => {
      const device = this.markers.find(m => m.markerObject === marker)?.deviceData;
      if (device) {
        this.openInfoWindow(marker, device);
      }
    });

    // 清空現有標記
    this.markers = [];

    // 建立並加入標記
    mergedData.forEach((device) => {
      const location = (device.userDefindLat && device.userDefindLat !== '' && device.userDefindLng && device.userDefindLng !== '')
        ? { lat: parseFloat(device.userDefindLat), lng: parseFloat(device.userDefindLng) }
        : device.dev_Location?.GPS || device.dev_Location?.Manual || device.dev_Location?.External || { lat: 0, lng: 0 };

      if (location) {
        const iconUrl = (device.status === 'Online')
          ? (device.alarm && device.alarm.Critical
            ? 'assets/images/device_critical_alarm.gif'
            : 'assets/images/eap_online.png')
          : 'assets/images/eap_offline.png';

        const marker = new google.maps.Marker({
          position: {
            lat: location.lat,
            lng: location.lng,
          },
          title: device.sn || 'No Serial Number',
          icon: { url: iconUrl },
        });

        oms.addMarker(marker);

        markerObjects.push(marker);
        this.markers.push({
          markerObject: marker,
          deviceData: device,
        });
        bounds.extend(new google.maps.LatLng(location.lat, location.lng));
      }
    });

    if (this.markers.length > 0) {
      mapInstance.fitBounds(bounds);
      const minZoom = 2;
      if (mapInstance.getZoom() < minZoom) {
        mapInstance.setZoom(2);
      }
    } else {
      mapInstance.setCenter({ lat: 0, lng: 0 });
      mapInstance.setZoom(2); // Default zoom level if no markers
    }

    this.reloadMarkers(markerObjects);
  }

  private reloadMarkers(markerObjects: google.maps.Marker[]): void {
    if (this.markerCluster) {
      this.markerCluster.clearMarkers();
    }

    // Define the cluster renderer
    const customRenderer = {
      render: (cluster: any, stats: any, map: google.maps.Map) => {
        // Determine if the cluster contains critical alarms
        const hasCriticalAlarm = cluster.markers.some((marker: google.maps.Marker) => {
          const device = this.markers.find((m) => m.markerObject === marker)?.deviceData;
          return device?.status === 'Online' && device?.alarm?.Critical;
        });

        // Create a marker for the cluster
        return new google.maps.Marker({
          position: cluster.position,
          icon: {
            url: hasCriticalAlarm
              ? 'assets/images/cluster_red.png'
              : 'assets/images/cluster_blue.png',
            scaledSize: new google.maps.Size(40, 40),
          },
          label: {
            text: String(cluster.markers.length),
            color: '#ffffff',
            fontSize: '12px',
          },
          map: map,
        });
      },
    };

    // Initialize or update MarkerClusterer
    this.markerCluster = new MarkerClusterer({
      map: this.googlemap.googleMap,
      markers: markerObjects,
      renderer: customRenderer,
      onClusterClick: (event, cluster) => {
        const map = this.googlemap.googleMap;
        const clusterPosition = cluster.position;
        const currentZoom = map.getZoom() || 0;
        const maxZoom = 21;

        if (clusterPosition) {
          map.setCenter(clusterPosition)
        }

        if (currentZoom < 10 && cluster.bounds) {
          map.fitBounds(cluster.bounds);
        } else if (currentZoom < maxZoom) {
          map.setZoom(currentZoom + 2);
        } else {
          console.log("Reached max zoom level.");
        }
      }
    });

  }

  private infoWindow: google.maps.InfoWindow | null = null;
  openInfoWindow(marker: google.maps.Marker, device: any): void {
    // console.log(device)
    const tagsContent = device.tags.map(tag => `<span class="badge badge-light-success mr-50">${tag}</span>`).join(' ');
    const formattedIp = this.formatIpPipe.transform(device.lastIp);
    const alarmContent = (device.alarm)
      ? Object.entries(device.alarm)
        .map(([key, value]) => {
          let badgeClass = '';
          switch (key) {
            case 'Critical':
              badgeClass = 'badge badge-light-danger';
              break;
            case 'Major':
              badgeClass = 'badge badge-light-warning';
              break;
            case 'Minor':
            case 'Warning':
              badgeClass = 'badge badge-light-secondary';
              break;
          }
          return `<span class="${badgeClass} mr-50">${key}: ${value}</span>`;
        })
        .join(' ')
      : `<span>-</span>`;
    const infoWindowContent = `
      <div style="color:black">
        <span>Serial Number : <span class="cursor-pointer text-primary" id="device">${device.sn || '-'}</span></span><br>
        <span>Status : <span class="${device.status === 'Online' ? 'text-success' : 'text-secondary'}">${device.status || '-'}</span></span><br>
        <span>Provisioning Type : <span>${device.provisioningType || '-'}</span></span><br>
        <span>Product Name : <span class="cursor-pointer text-primary" id="product">${device.productName || '-'}</span></span><br>
        <span>MAC : <span>${device.mac || '-'}</span></span><br>
        <span>IP : <span>${formattedIp || '-'}</span></span><br>
        <span>Label : <span>${device.label || '-'}</span></span><br>
        <span>Tags : <span>${device.tags.length != 0 ? tagsContent : '-'}</span></span><br>
        <span>Alarm : <span>${alarmContent}</span></span><br>
      </div>
    `;

    if (!this.infoWindow) {
      this.infoWindow = new google.maps.InfoWindow();
    } else {
      this.infoWindow.close();
    }
    this.infoWindow.setContent(infoWindowContent);
    this.infoWindow.open(this.googlemap.googleMap, marker);


    // 使用 google.maps.event.addListener 來處理 InfoWindow 內部的 click 事件
    google.maps.event.addListener(this.infoWindow, 'domready', () => {
      const deviceSnElement = document.getElementById('device');
      const productElement = document.getElementById('product');
      if (deviceSnElement) {
        deviceSnElement.addEventListener('click', () => {
          this.navigateToDeviceInfo(device.deviceId);
        });
      }
      if (productElement) {
        productElement.addEventListener('click', () => {
          this.navigateToProduct(device.deviceId);
        });
      }
    });
  }

  osmMap: any
  marker: any
  osmInitMap(mergedData: any[]): void {
    this.ismapErr = false
    if (this.osmMap) {
      this.osmMap.remove();
    }

    this.osmMap = L.map('osmmap', {
      center: [0, 0],
      zoom: 1,
      zoomControl: false,
      scrollWheelZoom: false,
    });

    const tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 18,
      minZoom: 1,
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).on('tileerror', (error) => {
      // console.log(error)
      this.ismapErr = true
    }).addTo(this.osmMap);

    // 添加缩放控件
    L.control.zoom({
      position: 'bottomright',
    }).addTo(this.osmMap);

    tiles.addTo(this.osmMap);

    const markerClusterGroup = L.markerClusterGroup({
      spiderfyOnMaxZoom: true,
      showCoverageOnHover: false,
      zoomToBoundsOnClick: true,
      spiderLegPolylineOptions: { weight: 1.5, color: '#222', opacity: 0.5 },
      iconCreateFunction: function (cluster) {
        const markers = cluster.getAllChildMarkers();
        let hasCriticalAlarm = false;

        markers.forEach(marker => {
          const device = marker.deviceData;
          if (device?.status === 'Online' && device?.alarm?.Critical) {
            hasCriticalAlarm = true;
          }
        });
        const clusterIconUrl = hasCriticalAlarm
          ? 'assets/images/cluster_red.png'
          : 'assets/images/cluster_blue.png';

        return L.divIcon({
          html: `<div style="background-image: url('${clusterIconUrl}'); width: 40px; height: 40px; display: flex; justify-content: center; align-items: center; font-size: 14px; color: #fff;">${cluster.getChildCount()}</div>`,
          className: 'custom-cluster-icon',
          iconSize: [40, 40],
        });
      },
    });

    const bounds = L.latLngBounds();
    mergedData.forEach((device) => {
      const location = (device.userDefindLat && device.userDefindLng)
        ? { lat: device.userDefindLat, lng: device.userDefindLng }
        : device.dev_Location?.GPS || device.dev_Location?.Manual || device.dev_Location?.External || { lat: 0, lng: 0 };

      const iconUrl = (device.status === 'Online')
        ? (device.alarm && device.alarm.Critical
          ? 'assets/images/device_critical_alarm.gif'
          : 'assets/images/eap_online.png')
        : 'assets/images/eap_offline.png';

      if (location) {
        // 創建標記
        const customIcon = L.icon({
          iconUrl: iconUrl,
          iconAnchor: [18, 0],
        });

        const marker = L.marker(
          [parseFloat(location.lat), parseFloat(location.lng)],
          { icon: customIcon }
        );

        const tagsContent = device.tags.map(tag => `<span class="badge badge-light-success mr-50">${tag}</span>`).join(' ');
        const alarmContent = (device.alarm)
          ? Object.entries(device.alarm)
            .map(([key, value]) => {
              let badgeClass = '';
              switch (key) {
                case 'Critical':
                  badgeClass = 'badge badge-light-danger';
                  break;
                case 'Major':
                  badgeClass = 'badge badge-light-warning';
                  break;
                case 'Minor':
                case 'Warning':
                  badgeClass = 'badge badge-light-secondary';
                  break;
              }
              return `<span class="${badgeClass} mr-50">${key}: ${value}</span>`;
            })
            .join(' ')
          : `<span>-</span>`;
          const formattedIp = this.formatIpPipe.transform(device.lastIp);
        const popupContent = `
          <div>
            <span>Serial Number : <span class="cursor-pointer text-primary" id="device">${device.sn || '-'}</span></span><br>
            <span>Status : <span class="${device.status === 'Online' ? 'text-success' : 'text-secondary'}">${device.status || '-'}</span></span><br>
            <span>Provisioning Type : <span>${device.provisioningType || '-'}</span></span><br>
            <span>Product Name : <span class="cursor-pointer text-primary" id="product">${device.productName || '-'}</span></span><br>
            <span>MAC : <span>${device.mac || '-'}</span></span><br>
            <span>IP : <span>${formattedIp || '-'}</span></span><br>
            <span>Label : <span>${device.label || '-'}</span></span><br>
            <span>Tags : <span>${device.tags.length != 0 ? tagsContent : '-'}</span></span><br>
            <span>Alarm : <span>${alarmContent}</span></span><br>
          </div>
        `;

        marker.on('popupopen', () => {
          // Attach click event to custom elements inside popup
          const deviceSnElement = document.getElementById('device');
          const productElement = document.getElementById('product');
          if (deviceSnElement) {
            deviceSnElement.addEventListener('click', () => {
              this.navigateToDeviceInfo(device.deviceId);
            });
          }
          if (productElement) {
            productElement.addEventListener('click', () => {
              this.navigateToProduct(device.deviceId);
            });
          }
        });

        marker.bindPopup(popupContent);
        marker.on('click', () => {
          this.osmMap.setView([parseFloat(location.lat), parseFloat(location.lng)], 18);
        });

        markerClusterGroup.addLayer(marker);
        const latLng = marker.getLatLng();
        bounds.extend(latLng);
      }
    });

    tiles.on('load', () => {
      this.osmMap.addLayer(markerClusterGroup);
    });
    this.osmMap.fitBounds(bounds);
    const minZoom = 2;
    if (this.osmMap.getZoom() < minZoom) {
      this.osmMap.setZoom(minZoom);
    }
    setTimeout(() => {
      this.osmMap.invalidateSize();
    }, 0);

    this.blockUIStatus = false;
  }


  navigateToDeviceInfo(deviceId) {
    this.router.navigate([`/devices/${deviceId}/device-info`]);
  }
  navigateToProduct(deviceId) {
    this._deviceInfoService.getProductInfo(deviceId).then((res: any) => {
      this._dataStoreService.setQueryName('product', { name: res.name, deploymentMode: res.deploymentMode });
    }).finally(() => {
      this.router.navigate(['/products'], { skipLocationChange: false });
    });
  }

  ngAfterViewInit() {
    if (this.mapType == '1') {
      this.getMapRes()
      this.cdr.detectChanges();
    }
  }

  public deviceApiLoaded$ = new BehaviorSubject<boolean>(false);
  ngOnInit() {
    if (this.mapType === '0') {
      this.googleMapsService.loadGoogleMapsApi()
        .then(() => {
          Promise.resolve().then(() => {
            this.deviceApiLoaded$.next(true);
            this.getMapRes();
          });
          this.ismapErr = false
          this.cdr.detectChanges();
        })
        .catch(error => {
          Promise.resolve().then(() => {
            this.deviceApiLoaded$.next(false);
          });
          // console.error('Google Maps API 加載失敗:', error);
          this.ismapErr = true
          this.cdr.detectChanges();
        });
    }
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getMapRes();
    });
  }



  ngOnDestroy(): void {
    if (this.osmMap) {
      this.osmMap.remove();
    }
  }

}
