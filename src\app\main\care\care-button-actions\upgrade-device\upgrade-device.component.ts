import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit,Input, Output,EventEmitter} from '@angular/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import Swal from 'sweetalert2';
import { TranslateService } from '@ngx-translate/core';
import { CareService } from 'app/main/care/care.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
@UntilDestroy()
@Component({
  selector: 'app-upgrade-device',
  templateUrl: './upgrade-device.component.html',
  styleUrls: ['./upgrade-device.component.scss']
})
export class UpgradeDeviceComponent extends Unsubscribe implements OnInit {
  @Output() loadingEvt = new EventEmitter<string>();
  public upgradeStatus;
  public loading: boolean = false
  public currentDeviceSN: string;
  public deviceId : string;
  @Input() editMode: boolean;
  public CONFIRMUPLOADLOG = this.translateService.instant('DEVICES.ACTION.CONFIRMUPLOADLOG');
  public DOUPLOADLOG = this.translateService.instant('DEVICES.ACTION.DOUPLOADLOG');
  public CONFIRMFIRMWARE = this.translateService.instant('DEVICES.ACTION.CONFIRMFIRMWARE');
  public DOFIRMWARE = this.translateService.instant('DEVICES.ACTION.DOFIRMWARE');
  public FAILFIRMWARE = this.translateService.instant('DEVICES.ACTION.FAILFIRMWARE');
  public UPGRADEFIRMWARE = this.translateService.instant('DEVICES.ACTION.UPGRADEFIRMWARE');
  public UPGRADEFIRMWARESUCCMESSAGE = this.translateService.instant('DEVICES.ACTION.UPGRADEFIRMWARESUCCMESSAGE');

  public assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
  public assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
  public assignOperationFail = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL');
  public assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
  public assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
  public assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
  public assignFailMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL_MESSAGE');
  public fileTitle = this.translateService.instant('PROVISIONING.TARGETNAME');;


  constructor(
    private translateService: TranslateService,
    private  _careService: CareService,
    private _toastrUtilsService: ToastrUtilsService,
  ) {
    super();
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.CONFIRMUPLOADLOG = this.translateService.instant('DEVICES.ACTION.CONFIRMUPLOADLOG');
      this.DOUPLOADLOG = this.translateService.instant('DEVICES.ACTION.DOUPLOADLOG');
      this.CONFIRMFIRMWARE = this.translateService.instant('DEVICES.ACTION.CONFIRMFIRMWARE');
      this.DOFIRMWARE = this.translateService.instant('DEVICES.ACTION.DOFIRMWARE');
      this.FAILFIRMWARE = this.translateService.instant('DEVICES.ACTION.FAILFIRMWARE');
      this.fileTitle =this.translateService.instant('PROVISIONING.TARGETNAME');
      this.UPGRADEFIRMWARE = this.translateService.instant('DEVICES.ACTION.UPGRADEFIRMWARE');
      this.UPGRADEFIRMWARESUCCMESSAGE = this.translateService.instant('DEVICES.ACTION.UPGRADEFIRMWARESUCCMESSAGE');

      this.assignOperation = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION');
      this.assignOperationSucc = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCC');
      this.assignOperationFail = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL');
      this.assignSuccessMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_SUCCESS_MESSAGE');
      this.assignWarningMsg1 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE1');
      this.assignWarningMsg2 = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_WARNING_MESSAGE2');
      this.assignFailMsg = this.translateService.instant('DEVICES.ACTION.ASSIGN_OPERATION_FAIL_MESSAGE');
    })
  }

  loadingChange(cal) {
    this.loadingEvt.emit(cal);
  }

  upgradeDevice() {
    this.loading = true
    this._careService.latestFirmware(this.deviceId).then((res: any) => {
      let fileName = res.fileURL.split('/')[res.fileURL.split('/').length - 1]
      console.log(fileName)
      this.upgradeFirmware(this.deviceId, res, this.currentDeviceSN, fileName)
    }).catch((res) => {
      this.loading = false
      this.loadingChange('false')
      this._toastrUtilsService.showErrorMessage(this.assignOperationFail, res.error);
    });
    
  }

  upgradeFirmware(currentDeviceId, res, sn, fileNmae) {
    let title = this.upgradeStatus ? `<span>${this.CONFIRMFIRMWARE + sn}<span class="text-danger" style="font-size:18px;font-weight:bold;">（Upgrading）</span></span>` : `${this.CONFIRMFIRMWARE + sn}`
    Swal.fire({
      title,
      html: `<span style="font-weight:bold;">${this.fileTitle}:${fileNmae}</span><br>${this.DOFIRMWARE}</div>`,
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._careService.upgradeFirmware(currentDeviceId, res.id, this.upgradeStatus).then(() => {
          this.crq(currentDeviceId, sn)
        }).catch((res) => {
          if (res == 'OK') {
            this.crq(currentDeviceId, sn)
          } else {
            this.loading = false
            this.loadingChange('false')
            console.log(res)
            this._toastrUtilsService.showErrorMessage(this.assignOperationFail, `${sn} ${this.assignFailMsg} \n ${res.error}`);
          }
        });
      } else {
        this.loading = false
        this.loadingChange('false')
      }
    });
  }

  crq(deviceId, sn) {
    this._careService.connectionRequest(deviceId).then((res) => {
      if (String(res) == 'true') {
        this.loading = false
        this.loadingChange('false')
        this._toastrUtilsService.showInfoMessage(this.assignOperationSucc, `${sn}  ${this.assignSuccessMsg}`)
      } else {
        this.loading = false
        this.loadingChange('false')
        this._toastrUtilsService.showWarningMessage(this.assignOperation, `${this.assignWarningMsg1} ${sn} ${this.assignWarningMsg2}`);
      }
    }).catch((res) => {
      this.loading = false
      this.loadingChange('false')
      this._toastrUtilsService.showWarningMessage(this.assignOperation, `${this.assignWarningMsg1} ${sn} ${this.assignWarningMsg2}`);
    });
  }

  getDeviceId(): void {
    this.customSubscribe(this._careService.careDeviceChanged, res => {
      if (res && res.id) {
        this.deviceId = res.id;
        if (res.protocol == 'netconf') {
          this.checkUpgradeStatus(this.deviceId)
        }
        if (res.serialNumber) {
          this.currentDeviceSN = res.serialNumber;
        } else {
          this.currentDeviceSN = res.deviceIdStruct.serialNumber;
        }
      }else{
        this.deviceId = '';
      }
    })
  }

  checkUpgradeStatus(data) {
    this._careService.getUpgradeStatus(data).then(res => {
      this.upgradeStatus = res
    })
  }

  ngOnInit(): void {
      this.getDeviceId()
  }

}
