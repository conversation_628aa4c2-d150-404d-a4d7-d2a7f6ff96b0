import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { Component,Input, OnInit } from '@angular/core';
import { ColumnMode } from '@almaobservatory/ngx-datatable';
import { CareService } from 'app/main/care/care.service';
import { ActivatedRoute } from '@angular/router';
import { CareDataFormatService } from '../care-data-format.service';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';

@UntilDestroy()
@Component({
  selector: 'app-care-error-status',
  templateUrl: './care-error-status.component.html',
  styleUrls: ['./care-error-status.component.scss']
})
export class CareErrorStatusComponent extends Unsubscribe implements OnInit {
  @Input() accessor: any;
  public deviceId: string;
  public blockUIStatus = false;
  public errorStatusItems: any;
  public selectedOption = 5;
  public ColumnMode = ColumnMode;

  constructor(
    private _careService: CareService,
    private route: ActivatedRoute,
    private _careDataFormatService: CareDataFormatService,
  ) {
    super();
   }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getErrorStatusRes(this.deviceId);
        break;
    }
  };

    /**
  * get Error Status
  */
    getErrorStatusRes(deviceId) {
      this.blockUIStatus = true;
      this._careService.getErrorStatus(deviceId).then((res: any) => {
        this.errorStatusItems = this._careDataFormatService.dataFormat(res)
      }).finally(() => {
        this.blockUIStatus = false;
      });
    }


    getDeviceId(): void {
      this._careService.careDeviceChanged.pipe(untilDestroyed(this)).subscribe(res => {
        if (res && res.id) {
          this.deviceId = res.id
          this.getErrorStatusRes(this.deviceId)
        }else{
          this.errorStatusItems = []
        }
      })
    }

  ngOnInit(): void {
    this.getDeviceId()
  }

}
