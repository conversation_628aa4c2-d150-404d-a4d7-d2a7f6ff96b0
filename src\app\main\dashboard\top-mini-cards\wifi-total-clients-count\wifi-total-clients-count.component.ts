import { Component, OnInit, ViewChild, ChangeDetectorRef, Input } from '@angular/core';
import { DashboardService } from '../../dashboard.service';
import { timer, Subscription } from 'rxjs';
import { ColumnMode, DatatableComponent, SelectionType } from '@almaobservatory/ngx-datatable';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
	selector: 'app-wifi-total-clients-count',
	templateUrl: './wifi-total-clients-count.component.html',
	styleUrls: ['./wifi-total-clients-count.component.scss']
})
export class WifiTotalClientsCountComponent implements OnInit {

	constructor(
		private _dashboardService: DashboardService,
		private modalService: NgbModal,
		private translateService: TranslateService
	) {

	}
	@Input() accessor: any;
	@ViewChild(DatatableComponent) tableRowDetails: DatatableComponent;
	public loading: boolean = false;
	public data: number
	public totalClients: number
	public totalExcellent: number
	public totalGood: number
	public totalPoor: number
	public ColumnMode = ColumnMode;
	public SelectionType = SelectionType;
	public chartData
	public tempData;
	public clientsTable
	public title
	public selectedOption = 10;

	ngOnInit(): void {
		this.title = this.translateService.instant(this.accessor.name);
		this.getTotalClients();
	}

	emittedEvents(event: string): void {
		switch (event) {
			case 'reload':
				this.getTotalClients();
				break;
		}
	};

	public columnSelectOptions = [
		{
			name: 'Name', prop: 'name', translate: 'DASHBOARD.PRODUCTNAME', width:
				100, flexGrow: 100, columnStatus: true
		},
		{ name: 'APs', prop: 'aps', translate: 'DASHBOARD.APS', width: 100, flexGrow: 100, columnStatus: true },
		{ name: 'Online APs', prop: 'onlineAps', translate: 'DASHBOARD.ONLINEAPS', width: 100, flexGrow: 100, columnStatus: true },
		{ name: 'Excellent clients', prop: 'excellent', translate: 'DASHBOARD.EXCELLENTCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
		{ name: 'Good clients', prop: 'good', translate: 'DASHBOARD.GOODCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
		{ name: 'Poor clients', prop: 'poor', translate: 'DASHBOARD.POORCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
		{ name: 'Total clients', prop: 'totalClients', translate: 'DASHBOARD.TOTALCLIENTS', width: 100, flexGrow: 100, columnStatus: true },
	]


	getTotalClients() {
		this.loading = true
		this._dashboardService.getTotalClients()
			.then(res => {

				let deviceClientsWithTimeSeries = [];
				res.forEach((item) => {
					deviceClientsWithTimeSeries.push(item)
				})
				// console.log(deviceClientsWithTimeSeries)

				let sum2g5gClientsWithTimeSeries = [];
				deviceClientsWithTimeSeries.forEach(function (item) {
					if (item.hasOwnProperty('2G_associatedDevice') == false) {
						item['2G_associatedDevice'] = { excellentCount: 0, goodCount: 0, poorCount: 0 }
					};
					if (item.hasOwnProperty('5G_associatedDevice') == false) {
						item['5G_associatedDevice'] = { excellentCount: 0, goodCount: 0, poorCount: 0 }
					};
					if (item.hasOwnProperty('6G_associatedDevice') == false) {
						item['6G_associatedDevice'] = { excellentCount: 0, goodCount: 0, poorCount: 0 }
					};
					sum2g5gClientsWithTimeSeries.push(
						{
							totalExcellent:
								item['2G_associatedDevice'].excellentCount +
								item['5G_associatedDevice'].excellentCount +
								item['6G_associatedDevice'].excellentCount,
							totalGood:
								item['2G_associatedDevice'].goodCount +
								item['5G_associatedDevice'].goodCount +
								item['6G_associatedDevice'].goodCount,
							totalPoor:
								item['2G_associatedDevice'].poorCount +
								item['5G_associatedDevice'].poorCount +
								item['6G_associatedDevice'].poorCount,
							name: item.name,
							aps: item.aps,
							onlineAps: item.onlineAps
						}
					)
				})
				sum2g5gClientsWithTimeSeries =
					sum2g5gClientsWithTimeSeries.map(function (item) {
						return {
							totalClients: { value: item.totalExcellent + item.totalGood + item.totalPoor },
							totalExcellent: { value: item.totalExcellent },
							totalGood: { value: item.totalGood },
							totalPoor: { value: item.totalPoor },
						}
					})

				this.totalClients = 0;
				this.totalExcellent = 0;
				this.totalGood = 0;
				this.totalPoor = 0;

				sum2g5gClientsWithTimeSeries.forEach(item => {
					this.totalClients += item.totalClients.value;
					this.totalExcellent += item.totalExcellent.value;
					this.totalGood += item.totalGood.value;
					this.totalPoor += item.totalPoor.value;
				})

				// console.log(sum2g5gClientsWithTimeSeries)
				this.chartData = [
					{ name: 'Excellent RSSI', value: this.totalExcellent },
					{ name: 'Good RSSI', value: this.totalGood },
					{ name: 'Poor RSSI', value: this.totalPoor },
				]

				// console.log(this.totalClients)

				this.tempData = res.map(item => {
					return {
						name: item.name || '',
						aps: item.aps || 0,
						onlineAps: item.onlineAps || 0,
						excellent:
							item['2G_associatedDevice'].excellentCount +
							item['5G_associatedDevice'].excellentCount +
							item['6G_associatedDevice'].excellentCount,
						good:
							item['2G_associatedDevice'].goodCount +
							item['5G_associatedDevice'].goodCount +
							item['6G_associatedDevice'].goodCount,
						poor:
							item['2G_associatedDevice'].poorCount +
							item['5G_associatedDevice'].poorCount +
							item['6G_associatedDevice'].poorCount,

						totalClients:
							item['2G_associatedDevice'].excellentCount +
							item['5G_associatedDevice'].excellentCount +
							item['6G_associatedDevice'].excellentCount +
							item['2G_associatedDevice'].goodCount +
							item['5G_associatedDevice'].goodCount +
							item['6G_associatedDevice'].goodCount +
							item['2G_associatedDevice'].poorCount +
							item['5G_associatedDevice'].poorCount +
							item['6G_associatedDevice'].poorCount,
						tags: item.tags
					}
				});
				this.clientsTable = this.tempData
				// console.log(this.tempData)
				// console.log(this.clientsTable)
			}).catch((err) => {
				console.error(err);
			})
			.finally(() => this.loading = false)
	}
	modalOpenBD(modalBD) {

		this.modalService.open(modalBD, {
			backdrop: false,
			size: 'lg',
			scrollable: true
		});
	}

	ngOnDestroy(): void {
	}
}
