import { Component, OnInit, ViewChild, Input, ViewEncapsulation, ChangeDetectorRef } from '@angular/core';
import { ColumnMode, SelectionType } from '@almaobservatory/ngx-datatable';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import Swal from 'sweetalert2';
import { TranslateService } from '@ngx-translate/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { AnalysisRefurbishmentService } from '../ananlysis-refurbishment.service';
import { DatatableCustomizeService } from 'app/main/commonService/datatable-customize.service';
import { BehaviorSubject, Subject } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { debounceTime } from 'rxjs/operators';
import { ResizeObservableService } from 'app/main/commonService/resize-observable.service';
import cloneDeep from 'lodash/cloneDeep';
export interface IqueryParam {
  filter: {},
  page: number;
  size: number
}
@UntilDestroy()
@Component({
  selector: 'app-refurbishment-history',
  templateUrl: './refurbishment-history.component.html',
  styleUrls: ['./refurbishment-history.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RefurbishmentHistoryComponent extends Unsubscribe implements OnInit {
  @ViewChild('tableRowDetails') tableRowDetails: any;
  @Input() accessor: any;
  public tableWidth = 0;
  public gridScrollHeight = 0;
  public scrollSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  public tableOptionStatus = false;
  public norNormalFiles: any;
  public selectedOption = 10;
  public ColumnMode = ColumnMode;
  public SelectionType = SelectionType;
  public rows = []
  public tempNormalData = []
  public blockUIStatus = false;
  public selected = []
  public filterMap = {}
  public queryParams: IqueryParam = {
    filter: {
      'serialNumber': '',
    },
    page: 1,
    size: 10
  }
  public pageCount: number
  filterChanged: Subject<string> = new Subject<string>();
  public tableOption = [
    { name: 'Serial Number', prop: 'serial', translate: 'DEVICES.SERIAL_NUMBER', width: 130, flexGrow: 130, columnStatus: true },
    { name: 'OUI', prop: 'OUI', translate: 'OUI', width: 120, flexGrow: 120, columnStatus: true },
    { name: 'Product Name', prop: 'productName', translate: 'PRODUCTS.PRODUCTNAME', width: 120, flexGrow: 120, columnStatus: true },
    // { name: 'ProductClass', prop: 'ProductClass', translate: 'PM.PRODUCTCLASS', width: 120, flexGrow: 120, columnStatus: true },
    { name: 'Tags', prop: 'tags', translate: 'DEVICES.TAGS', width: 120, flexGrow: 120, columnStatus: false },
    { name: 'Label', prop: 'label', translate: 'DEVICES.LABEL', width: 120, flexGrow: 120, columnStatus: false },
    { name: 'Refurbishment Time', prop: 'refurbishmentTime', translate: 'REFURBISHMENT.REFURBISHMENTTIME', width: 160, flexGrow: 160, columnStatus: true },
    { name: 'Last Connected', prop: 'lastConnected', translate: 'DEVICES.LAST_CONNECTED', width: 160, flexGrow: 160, columnStatus: true },
    { name: 'Established Time', prop: 'established', translate: 'REFURBISHMENT.INSTALLATIONTIME', width: 160, flexGrow: 160, columnStatus: true },
  ];
  constructor(
    private modalService: NgbModal,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService,
    private _AnalysisRefurbishmentService: AnalysisRefurbishmentService,
    private _datatableCustomizeService: DatatableCustomizeService,
    private _resizeObservableService: ResizeObservableService,
    private cdr: ChangeDetectorRef,
  ) {
    super()
    this.filterChanged.pipe(
      debounceTime(1000),
      untilDestroyed(this))
      .subscribe(model => {
        // console.log(model);
        this.queryParams.page = 1;
        this.getRefurbishmentDeviceList(this.cloneParams(this.queryParams))
      });
  }

  /**
* Core card event
* @param event 
*/
  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getRefurbishmentDeviceList(this.cloneParams(this.queryParams))
        break;
    }
  };

  ngAfterViewChecked(): void {
    const { scrollHeight, innerWidth: tableWidth } = this.tableRowDetails.bodyComponent;
    const { scrollHeight: gridScrollHeight } = this.accessor.gridRef?.el;
    if (((gridScrollHeight && gridScrollHeight > this.gridScrollHeight) || (tableWidth && tableWidth !== this.tableWidth)) && scrollHeight) {
      this.tableWidth = tableWidth;
      this.gridScrollHeight = gridScrollHeight;
      this.scrollSubject.next({ gridScrollHeight, tableWidth });
    }
    this.cdr.detectChanges();
  }

  changeColumn(event): void {
    if (event.cloumns) {
      this.tableOption = [...event.cloumns];
    }
  }

  onResize({ column, newValue }) {
    this.tableOption = this._datatableCustomizeService.resize(column, newValue, this.tableOption, this.accessor, this.tableRowDetails);
  }

  onSelect({ selected }) {
    //console.log('Select Event', selected, this.selected);

    this.selected.splice(0, this.selected.length);
    this.selected.push(...selected);
  }

  /**
  * @description: get file list response
  * @return {*}
  */
  getRefurbishmentDeviceList(param) {
    var params = {
      params: param
    };
    this._AnalysisRefurbishmentService.getRefurbishmentDeviceList(params).then((res: any) => {
      res["data"].forEach(item => {
        item.ProductClass = item.deviceInfo.ProductClass
        item.SerialNumber = item.deviceInfo.SerialNumber
        item.OUI = item.deviceInfo.OUI
      });
      this.rows = res.data
      this.pageCount = res.num
    }).finally(() => {
      this.selected = []
    })
  }

  onFooterPage(e) {
    // console.log(e)
    this.queryParams.page = e.page
    // console.log(this.queryParams)
    this.getRefurbishmentDeviceList(this.cloneParams(this.queryParams))
  }

  changePageCount(e) {
    this.queryParams.page = 1
    this.queryParams.size = e
    this.getRefurbishmentDeviceList(this.cloneParams(this.queryParams))
  }

  cloneParams(queryParams) {
    let cloneQueryParamsClone
    cloneQueryParamsClone = cloneDeep(queryParams)
    for (let key in cloneQueryParamsClone.filter) {
      if (cloneQueryParamsClone.filter[key] == '') {
        delete cloneQueryParamsClone.filter[key]
      }
    }

    cloneQueryParamsClone.filter = JSON.stringify(cloneQueryParamsClone.filter)
    cloneQueryParamsClone.page = cloneQueryParamsClone.page - 1
    // console.log('queryParamsClone', cloneQueryParamsClone)
    return cloneQueryParamsClone
  }

  filterInputChange(obj) {
    // this.queryParams.filter[prop] = obj.text;
    this.filterChanged.next(obj.text);
  }

  download() {
    // let structList = this.selected;
    // let data = [['Name', 'FileType', 'Description', 'All Target Type', 'Target']];
    // structList.map(item => {
    //   var _structInfoArry = [];
    //   _structInfoArry.push(item.name.toString());
    //   _structInfoArry.push(item.fileType.toString());
    //   _structInfoArry.push(item.description ? item.description.toString() : '');
    //   _structInfoArry.push(item.source.toString());
    //   _structInfoArry.push(
    //     item.source === "Default" ? '-' : item.source === "Product" ? item.targetProductName.toString() : item.targetDeviceSn.toString()
    //   );
    //   return _structInfoArry;
    // }).forEach(struct => {
    //   data.push(struct);
    // });
    // this._fileDownloadService.generateFileToXLSX({
    //   data,
    //   name: "Files_Table"
    // })
  }

  ngOnInit(): void {
    this.tableOptionStatus = !!this._datatableCustomizeService.getTableOption(this.accessor.componentId);
    this.tableOption = this._datatableCustomizeService.mergeOption(this.accessor.componentId, this.tableOption);
    const dueTime = this.tableOptionStatus ? 400 : 0;
    this.scrollSubject.pipe(untilDestroyed(this), debounceTime(dueTime)).subscribe(res => {
      if (res.gridScrollHeight && res.tableWidth && !this._resizeObservableService.windowResizeState) {
        this.tableOption = this._datatableCustomizeService.formatColumn(this.tableRowDetails, this.tableOption, this.tableWidth, this.accessor);
      }
    })
    this.getRefurbishmentDeviceList(this.cloneParams(this.queryParams))
  }

  ngOnDestroy(): void {
    this.filterChanged.complete()
  }

}
