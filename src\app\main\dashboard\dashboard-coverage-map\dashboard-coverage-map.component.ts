import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { GroupInfoService } from 'app/main/groups/group-info/group-info.service';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { GroupDataService } from 'app/main/groups/group-data.service'
import { timer, Subscription } from 'rxjs';

@UntilDestroy()
@Component({
  selector: 'app-dashboard-coverage-map',
  templateUrl: './dashboard-coverage-map.component.html',
  styleUrls: ['./dashboard-coverage-map.component.scss']
})
export class DashboardCoverageMapComponent implements OnInit, OnDestroy {
  @Input() accessor: any;

  public blockUIStatus = false;
  public groupId: string
  public groupName: string
  public rfData: any
  public groupList = []
  public groupselected: any

  constructor(
    private _groupInfoService: GroupInfoService,
    private _groupListService: GroupListService,
    private _groupDataService: GroupDataService,
  ) {
    this.getGroupList()
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        // this.getGroupList()
        this.getRfmapInfo(this.groupId)
        break;
    }
  };

  getGroupList() {
    this.blockUIStatus = true
    this._groupListService.getGroupNameList().pipe(untilDestroyed(this)).subscribe({
      next: res => {
        this.groupList = res.sort((a, b) => {
          if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
          return 1
        })
        this.groupselected = res[0]?.id || null
        this.selectedGroup(res[0])
      },
      error: error => {
        console.log(error);
      },
      complete: () => {
        this.blockUIStatus = false;
      }
    })
  }

  getRfmapInfo(groupId) {
    this.blockUIStatus = true
    if (groupId) {
      this._groupInfoService.getCoverageMapInfo(groupId).then(res => {
        // console.log(res)
        this.rfData = res
      }).finally(() => {
        this.blockUIStatus = false;
      })
    } else {
      this.blockUIStatus = false;
    }
  }

  selectedGroup(item) {
    // console.log(item)
    if (item) {
      this.getRfmapInfo(item.id)
      this.groupId = item.id
    } else {
      this.rfData = null
      this.blockUIStatus = false;
    }
  }


  ngOnInit(): void {
    timer(600000, 600000).pipe(untilDestroyed(this)).subscribe(() => {
      this.getRfmapInfo(this.groupId);
    });
  }

  ngAfterViewInit() {
    this._groupDataService.getGroupId().pipe(untilDestroyed(this)).subscribe(groupId => {
      this.blockUIStatus = true
      // console.log(groupId)
      this.getRfmapInfo(groupId)
      this.groupId = groupId
      this.groupselected = groupId
    });
  }


  ngOnDestroy(): void {

  }

}
