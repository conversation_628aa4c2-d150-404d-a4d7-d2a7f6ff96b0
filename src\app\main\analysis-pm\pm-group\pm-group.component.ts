import { Component, OnInit, Input, SimpleChanges, OnChanges } from '@angular/core';
import { AnalysisPmService } from 'app/main/analysis-pm/analysis-pm.service';
import { BlockUI, NgBlockUI } from 'ng-block-ui';
import { selectPMClassify } from '../pm-classify-data';

@Component({
  selector: 'app-pm-group',
  templateUrl: './pm-group.component.html',
  styleUrls: ['./pm-group.component.scss']
})
export class PmGroupComponent implements OnInit, OnChanges {
  @Input() personalTheme: any;
  @Input() groupId: any;
  @Input() duration: any;
  @Input() measureType: any;
  @BlockUI('chartBlock') chartBlock: NgBlockUI;
  private isInitialized = false;
  public blockUIStatus = false;
  public maxDate;
  public minDate;
  public today = new Date();
  public groupName;
  public selectPMClassify = selectPMClassify
  public selectCondition = [
    { name: '=', value: 'EQ' },
    { name: '!=', value: 'NE' },
    { name: '>', value: 'GT' },
    { name: '<', value: 'LT' },
    { name: '<=', value: 'LTE' },
    { name: '>=', value: 'GTE' },
  ];
  public serialNumber;
  public classify;
  public paramName;
  public condition;
  public paramValue;
  public beginTime;
  public endTime;
  public operator = 'And';
  public originalData


  constructor(
    private _analysisPmService: AnalysisPmService,
  ) {

  }

  public groupInfo: any
  loadGroup() {
    this._analysisPmService.getGroupInfo(this.groupId).then(res => {
      // console.log(res)
      this.groupInfo = res
      this.groupName = this.groupInfo.name
      // console.log(this.selectGroup);
    })
  }


  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        break;
    }
  };


  mappingName(value, mappingArr) {
    const item = mappingArr.find(item => item.value === value);
    return item ? item.name : null;
  }


  extractAllKeysAndValues(obj: any, currentKey: string = ''): { key: string, value: any }[] {
    const keyValues: { key: string, value: any }[] = [];

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = currentKey ? `${currentKey}.${key}` : key;
        const value = obj[key];

        if (typeof value === 'object' && !Array.isArray(value)) {
          keyValues.push(...this.extractAllKeysAndValues(value, newKey));
        } else if (Array.isArray(value)) {
          for (let i = 0; i < value.length; i++) {
            if (typeof value[i] === 'object') {
              keyValues.push(...this.extractAllKeysAndValues(value[i], `${newKey}[${i}]`));
            }
          }
        } else {
          keyValues.push({ key: newKey, value });
        }
      }
    }

    return keyValues;
  }

  public res: any
  public resNum: any
  public series = []
  public resultData = []
  chartFormatData(res) {
    this.chartBlock.start();
    this.series = []
    this.resultData = []
    const jsonRes = JSON.parse(res);
    // console.log('jsonRes', jsonRes)
    let dataArr = []
    jsonRes.forEach(item => {
      for (let key = 0; key < item.records.length; key++) {
        dataArr.push(item.records[key])
      }
    })
    // console.log(dataArr)
    const groupedData = {};
    // 遍历输入数据
    dataArr.forEach(item => {
      const serialNumber = item.serialNumber;
      // 如果该serialNumber尚未在分组数据中创建数组，就创建一个空数组
      if (!groupedData[serialNumber]) {
        groupedData[serialNumber] = [];
      }
      // 将当前对象添加到相应的数组中
      groupedData[serialNumber].push(item);
    });
    const groupedArray = Object.values(groupedData);
    // console.log('groupedArray', groupedArray);

    let filterKeyArr = []
    groupedArray.forEach(arr => {
      let itemArr = []
      let arrItem
      arrItem = arr
      // console.log(arrItem)
      for (let i = 0; i < arrItem.length; i++) {
        arrItem.sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime());
        const item = arrItem[i];
        let extractAllKeysAndValues = this.extractAllKeysAndValues(item)
        // console.log(extractAllKeysAndValues)
        extractAllKeysAndValues.forEach(item => {
          if (item.key != 'serialNumber') {
            item.value = [arrItem[i].endTime, item.value]
          }
        })
        itemArr.push(extractAllKeysAndValues)
      }
      filterKeyArr.push(itemArr)
      // console.log('filter keyArr', this.filterKeyArr)
    })

    filterKeyArr.forEach(keyArr => {
      let result: { name: string, value: [string, string][], serial: string }[] = []
      // console.log(keyArr)
      for (let i = 0; i < keyArr.length; i++) {
        // console.log(keyArr[i])
        let serial = keyArr[i].find(item => {
          return item.key == 'serialNumber'
        }).value
        for (const item of keyArr[i]) {
          const existingItem = result.find((r) => r.name === item.key);
          if (existingItem) {
            existingItem.value.push(item.value);
          } else {
            result.push({ name: item.key, value: [item.value], serial: serial });
          }
        }
      }
      let filterkey = result.filter(item => {
        return item.name.includes('Rate') || item.name == 'DRB.UEThpUl.sum' || item.name == 'DRB.UEThpDl.sum'
      })
      this.resultData.push(filterkey)
    })
    // console.log('resultData', this.resultData)

    const result = [];
    for (const subArray of this.resultData) {
      // 在子数组中遍历对象
      for (const item of subArray) {
        const name = item.name;
        result[name] = result[name] || [];
        result[name].push(item);
      }
    }
    // console.log(result);

    for (let i in result) {
      let dataArr = []
      // console.log(i)
      // console.log(result[i])
      for (let k = 0; k < result[i].length; k++) {
        dataArr.push({
          xname: i,
          name: result[i][k].serial,
          data: result[i][k].value,
          type: 'line',
          smooth: true,
          sampling: 'lttb',
        })

      }
      // console.log('dataArr', dataArr)
      this.series.push(dataArr)
    }
    // console.log(this.series)
    this.chartBlock.stop();
  }


  formatDownloadData(res: string): any[] {
    const jsonRes = JSON.parse(res);
    const dataArr: any[] = [];

    jsonRes.forEach(item => {
      for (const record of item.records) {
        dataArr.push(record);
      }
    });

    const groupedData: Record<string, any[]> = {};
    dataArr.forEach(item => {
      const serialNumber = item.serialNumber;
      if (!groupedData[serialNumber]) groupedData[serialNumber] = [];
      groupedData[serialNumber].push(item);
    });

    const groupedArray = Object.values(groupedData);

    const resultData: any[][] = [];
    groupedArray.forEach(arr => {
      const itemArr: any[][] = [];
      arr.sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime());

      for (const item of arr) {
        const extracted = this.extractAllKeysAndValues(item);
        extracted.forEach(kv => {
          if (kv.key !== 'serialNumber') {
            kv.value = [item.endTime, kv.value];
          }
        });
        itemArr.push(extracted);
      }
      resultData.push(itemArr);
    });

    const mergedResultMap: Record<string, any[]> = {};

    resultData.forEach(keyArr => {
      const result: { name: string; value: [string, string][]; serial: string }[] = [];

      for (const arr of keyArr) {
        const serial = arr.find(item => item.key === 'serialNumber')?.value;
        for (const item of arr) {
          const existing = result.find(r => r.name === item.key);
          if (existing) {
            existing.value.push(item.value);
          } else {
            result.push({ name: item.key, value: [item.value], serial });
          }
        }
      }

      const filtered = result.filter(item =>
        item.name.includes('Rate') || item.name === 'DRB.UEThpUl.sum' || item.name === 'DRB.UEThpDl.sum'
      );

      for (const item of filtered) {
        if (!mergedResultMap[item.name]) mergedResultMap[item.name] = [];
        mergedResultMap[item.name].push(item);
      }
    });

    const finalSeries = [];
    for (const name in mergedResultMap) {
      const dataArr = mergedResultMap[name].map(entry => ({
        xname: name,
        name: entry.serial,
        data: entry.value,
      }));
      finalSeries.push(dataArr);
    }

    return finalSeries;
  }


  changDuration(duration) {
    this.chartBlock.start();
    // console.log(duration.currentValue)
    let today = new Date();
    this.endTime = new Date(today.setHours(23, 59, 59)).toISOString();
    if (duration.currentValue == 'month') {
      var oneMonthAgo = new Date(this.today);
      oneMonthAgo.setMonth(this.today.getMonth() - 1);
      if (this.today.getDate() < oneMonthAgo.getDate()) {
        oneMonthAgo.setDate(this.today.getDate());
      }
      // console.log(oneMonthAgo);
      oneMonthAgo.setHours(0, 0, 0, 0)
      this.beginTime = oneMonthAgo.toISOString()
    }
    else if (duration.currentValue == 'week') {
      let sevenDaysAgo = new Date(this.today);
      sevenDaysAgo.setDate(this.today.getDate() - 7);
      sevenDaysAgo.setHours(0, 0, 0, 0)
      // console.log(sevenDaysAgo);
      this.beginTime = sevenDaysAgo.toISOString()
    }
    else {
      this.beginTime = new Date(this.today.setHours(0, 0, 0, 0)).toISOString()
    }
    let param = {
      "beginTime": this.beginTime,
      "endTime": this.endTime,
    }
    // console.log(param)
    this._analysisPmService.postGroupDataByMeasureType(this.groupId, param, this.measureType).then(res => {
      // console.log(res)
      this.res = res
      this.resNum = JSON.parse(this.res).length
      this.chartFormatData(this.res)
    }).finally(() => {
      this.getOriginalData().then(seriesForDownload => {
        // console.log('change seriesForDownload', seriesForDownload);
        this.originalData = seriesForDownload
      }).finally(() => {
        this.chartBlock.stop();
      });
    });
  }

  changMeasure(measureType) {
    this.chartBlock.start();
    // console.log(measureType.currentValue)
    let param = {
      "beginTime": this.beginTime,
      "endTime": this.endTime,
    }
    // console.log(param)
    this._analysisPmService.postGroupDataByMeasureType(this.groupId, param, measureType.currentValue).then(res => {
      // console.log(res)
      this.res = res
      this.resNum = JSON.parse(this.res).length
      this.chartFormatData(this.res)
    }).finally(() => {
      this.chartBlock.stop();
    });
  }



  public originalRes
  getOriginalData(): Promise<any> {
    const param = {
      "beginTime": this.beginTime,
      "endTime": this.endTime,
    };

    return this._analysisPmService.postGroupData(this.groupId, param)
      .then(res => {
        this.originalRes = res;
        // console.log(this.originalRes)
        return this.formatDownloadData(this.originalRes);
      })
      .catch(error => {
        console.error(error);
        return [];
      })
      .finally(() => {

      });
  }

  getOriginalByXname(xname: string): any[] | undefined {
    return this.originalData?.find(arr => arr[0]?.xname === xname);
  }

  ngOnInit(): void {
    // console.log(this.groupId)
    this.isInitialized = true;
    this.loadGroup()
    this.beginTime = this.today.setHours(0, 0, 0, 0);
    this.endTime = this.today.setHours(23, 59, 59);
    let params = {
      "beginTime": (new Date(this.beginTime)).toISOString(),
      "endTime": (new Date(this.endTime)).toISOString(),
    }
    this._analysisPmService.postGroupDataByMeasureType(this.groupId, params, this.measureType).then(res => {
      // console.log(res)
      this.res = res
      this.resNum = JSON.parse(this.res).length
      this.chartFormatData(this.res)

    }).finally(() => {
      this.getOriginalData().then(seriesForDownload => {
        // console.log('seriesForDownload', seriesForDownload);
        this.originalData = seriesForDownload
      });
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    // console.log(changes)
    if (this.isInitialized) {
      if (changes.groupId || changes.duration) {
        // 属性变化时触发的逻辑
        this.changDuration(changes.duration)
      } else if (changes.measureType) {
        this.changMeasure(changes.measureType)
      }
    }
  }

}
