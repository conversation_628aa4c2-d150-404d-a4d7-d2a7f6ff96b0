import { Component, Input } from '@angular/core';
import { ColumnMode } from '@almaobservatory/ngx-datatable';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { AnalysisProvisioningService } from 'app/main/analysis/analysis-provisioning/analysis-provisioning.service';

@Component({
  selector: 'app-total-device-status',
  templateUrl: './total-device-status.component.html',
  styleUrls: ['./total-device-status.component.scss']
})
export class TotalDeviceStatusComponent extends Unsubscribe {
  @Input() accessor: any;
  public blockUIStatus = false;

  constructor(
    private _analysisProvisioningService: AnalysisProvisioningService,
  ) {
    super()
  }

  public selectedOption = 15;
  public ColumnMode = ColumnMode;
  public selected = [];

  public tableDatas = [];
  public provisioningMappingName = {
    'ACS Connection Status': ['ACS Connected', 'ACS DisConnected'],
    'Production Type': ['Production Unit', 'Non-Production Unit'],
    'XMPP Status': ['XMPP Connected', 'XMPP DisConnected'],
    'Firmware Status': ['Software Image Synchronized', 'Software Image UnSynchronized'],
    'IMS Registration Status': ['IMS Registered', 'IMS UnRegistered'],
    'SIM Status': ['SIM Connected ', 'SIM DisConnected'],
    'IPSec Tunnel Status': ['IPSec Connected', 'IPSec DisConnected'],
    'Has E911': ['Has E911 Address', 'E911 Address Missing'],
    'Battery InCharging Status': ['InCharging', 'Not Charging'],
    '2G Status': ['2.4G Up', '2.4G Down'],
    '5G Status': ['5G Up', '5G Down'],
    'Band Steering': ['Band Steering Enabled', 'Band Steering Disabled'],
    'Client Hosts Number': ['Less Than', 'More Than']
  };

  public productNameSelect: any
  public selectedProductName = 'All'


  changeFn(selected) {
    if (selected === undefined) {
      selected = 'All';
    }
    this.selectedProductName = selected
    // console.log(this.selectedProductName);
    this.reportAnalysisProvisioningRes(this.selectedProductName)
  }

  /**
    * get AnalysisProvisioning product name
    */
  getAnalysisProvisioningProductName() {
    this.customSubscribe(this._analysisProvisioningService.onAvailStatisticsProductNamesChanged, res=> {
      if (res === null) {
        this._analysisProvisioningService.getAnalysisProvisioningProductName().then()
      } else {
        this.productNameSelect = res;
      }
    })
  }
  /**
    * report AnalysisProvisioning
    */
  reportAnalysisProvisioningRes(productName) {
    this.blockUIStatus = true;
    this._analysisProvisioningService.reportAnalysisProvisioning(productName, 'total').then((res) => {
      // console.log(res)
      if (res['total']) {
        this.parseProvisionStatusData(res['total']);
      }
    }).finally(() => {
      this.blockUIStatus = false;
    });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.reportAnalysisProvisioningRes(this.selectedProductName)
        break;
    }
  };


  parseProvisionStatusData = function (data) {
    var m_totalNum = data["Total Devices"]
    for (var ii in data) {
      var tableItem = {};
      if (ii == "Total Devices") {
        tableItem['name'] = "Total Devices";
        tableItem['count'] = m_totalNum;
        tableItem['rate'] = this.takeTwoDecimalPercent(m_totalNum, m_totalNum);
        this.tableDatas.push(tableItem);
      } else if (ii == "IMS Unregistration Distribution" && (typeof data[ii] == 'object')) {
        var dataChild = data[ii];

        for (var kk in dataChild) {
          var tableItemChild = {};
          tableItemChild['name'] = kk;
          tableItemChild['count'] = dataChild[kk];
          tableItemChild['rate'] = this.takeTwoDecimalPercent(dataChild[kk], m_totalNum);
          this.tableDatas.push(tableItemChild);
        }
      } else if (ii == "Running Network Mode" && (typeof data[ii] == 'object')) {
        var dataChild = data[ii];

        for (var kk in dataChild) {
          var tableItemChild = {};
          tableItemChild['name'] = kk;
          tableItemChild['count'] = dataChild[kk];
          tableItemChild['rate'] = this.takeTwoDecimalPercent(dataChild[kk], m_totalNum);
          this.tableDatas.push(tableItemChild);
        }
      } else {
        tableItem['name'] = this.provisioningMappingName[ii][0];
        tableItem['count'] = data[ii];
        tableItem['rate'] = this.takeTwoDecimalPercent(data[ii], m_totalNum);
        this.tableDatas.push(tableItem);

        tableItem = {};
        tableItem['name'] = this.provisioningMappingName[ii][1];
        tableItem['count'] = m_totalNum - data[ii];
        tableItem['rate'] = this.takeTwoDecimalPercent((m_totalNum - data[ii]), m_totalNum);
        this.tableDatas.push(tableItem);
      }
    }
  }

  takeTwoDecimalPercent = function (data, total) {
    if (total == 0) {
      return '0%'
    }
    return Math.round(data * 10000 / total) / 100 + '%';
  }

  ngOnInit(): void {
    this.getAnalysisProvisioningProductName()
    this.reportAnalysisProvisioningRes(this.selectedProductName)
    // console.log(this.tableDatas)
  }


}
