<button
  *ngIf="!loading"
  [disabled]="editMode||noPermission"
  (click)="uploadLog();"
  type="button"
  class="btn btn-sm btn-primary mr-50"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.UPLOADLOG' | translate }}"
  [disabled]="!deviceId"
  rippleEffect>
  <span [data-feather]="'upload-cloud'"></span>
</button>
<button
  *ngIf="loading"
  [disabled]="editMode||noPermission"
  type="button"
  class="btn btn-sm btn-primary mr-50"
  style="padding:0.486rem;"
  ngbTooltip="{{ 'DEVICES.UPLOADLOG' | translate }}"
  rippleEffect
  [disabled]="true">
  <div
    class="spinner-border spinner-border-sm"
    role="status">
  </div>
</button>