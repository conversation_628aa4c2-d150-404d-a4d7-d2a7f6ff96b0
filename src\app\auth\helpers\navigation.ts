import { Component, inject, Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanDeactivateFn, Router, RouterStateSnapshot } from "@angular/router";

@Injectable({ providedIn: 'root' })
class NavigationService {
    private urlArray = [];
    private infoReg = /^\/(devices|groups)\/\w{24}\/.+$/;
    constructor(
        private _router: Router
    ) { }

    sameID(url1: string, url2: string) {
        let index = url1.lastIndexOf("/");
        return url1.substring(0, index) === url2.substring(0, index)
    }

    canDeactivate(component: Component, currentRoute: ActivatedRouteSnapshot, currentState: RouterStateSnapshot, nextState?: RouterStateSnapshot) {
        let currUrl = currentState.url;
        let nextUrl = nextState.url;

        if ((nextUrl === '/devices' || nextUrl === '/groups') && currUrl.indexOf(nextUrl) !== -1) {
            if (this.urlArray.length > 1) {
                let firstUrl = this.urlArray.shift();
                if (firstUrl === currUrl) {
                    let secondUrl = this.urlArray[0];
                    if (secondUrl !== nextUrl) {
                        this._router.navigate([secondUrl]);
                        return false;
                    }
                    return true;
                }
            }
        }
        if (this.infoReg.test(nextUrl)) {
            if (this.urlArray.length) {
                let firstUrl = this.urlArray[0];
                if (this.sameID(firstUrl, nextUrl)) {
                    this.urlArray[0] = nextUrl;
                } else if (firstUrl === currUrl) {
                    this.urlArray.unshift(nextUrl);
                } else {
                    this.urlArray = [nextUrl, currUrl];
                }
            } else {
                this.urlArray = [nextUrl, currUrl];
            }
        } else if (this.urlArray.length) {
            this.urlArray = [];
        }
        return true;
    }
}

export const Navigation: CanDeactivateFn<Component> = (component: Component, currentRoute: ActivatedRouteSnapshot, currentState: RouterStateSnapshot, nextState?: RouterStateSnapshot): boolean => {
    return inject(NavigationService).canDeactivate(component, currentRoute, currentState, nextState);
}