<core-card
  [actions]="['reload','maximize','download']"
  [componentId]="accessor.componentId"
  [blockUIStatus]="loading"
  (events)="emittedEvents($event)">
  <h4 class="card-title d-flex justify-content-between align-items-center"
  style="width: calc(100% - 65px);">
    <span
      class="text-truncate"
      ngbTooltip="{{ accessor.name | translate }}"
      container="body">
      {{ accessor.name | translate }}
    </span>
  </h4>
  <div
    class="card-body"
    style="padding:0px 10px 1.5rem 10px">
    <main
      class="w-100 h-100 overflow-hidden"
      #dashboardProducts>
      <app-dount-echarts
        [chartRef]="dashboardProducts"
        [chartData]="resData"
        [isShowNoData]="isShowNoData"
        [title]="'Registered Count'"
        [total]="total"
        [downloadData]="downloadData"
        [openModal]="openModalFlag">
      </app-dount-echarts>
    </main>
  </div>
</core-card>
