import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ConnectionRequestActionComponent } from './connection-request-action.component';

describe('ConnectionRequestActionComponent', () => {
  let component: ConnectionRequestActionComponent;
  let fixture: ComponentFixture<ConnectionRequestActionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ConnectionRequestActionComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ConnectionRequestActionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
