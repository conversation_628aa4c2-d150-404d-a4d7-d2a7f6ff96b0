import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import cloneDeep from 'lodash/cloneDeep';

@Injectable({
  providedIn: 'root'
})
export class DatatableCustomizeService {

  constructor(
    private _httpClient: HttpClient,
    private _gridSystemService: GridSystemService,
  ) { }

  getTableOption(title) {
    return this._gridSystemService.getPersonalColumns(title).tableOption;
  }

  saveTableOption(title, tableOption) {
    this._gridSystemService.savePersonalColumns(title, tableOption);
  }

  // reorder(column, newValue, prevValue, tableOption, columnSelectOptions, title) {
  //   const temp = tableOption.filter(item => Object.keys(columnSelectOptions).includes(item.name) && !columnSelectOptions[item.name])
  //   const option = tableOption.filter(item => !Object.keys(columnSelectOptions).includes(item.name) || columnSelectOptions[item.name]);
  //   option.splice(newValue, 0, option.splice(prevValue, 1)[0]);
  //   temp.forEach(item => {
  //     const index = tableOption.findIndex((currentValue) => currentValue.name === item.name);
  //     option.splice(index, 0, item);
  //   })
  //   tableOption = [].concat(...option);
  //   let columns = {};
  //   tableOption.filter(item => Object.keys(columnSelectOptions).includes(item.name))
  //     .map(item => columns[item.name] = columnSelectOptions[item.name])
  //   this.saveTableOption(title, columns, tableOption);
  //   return { tableOption, columns };
  // }
  getFixedWidth(columns, tableOption) {
    let fixedWidth = 0;
    if (!columns) return fixedWidth;
    columns.forEach(col => {
      if (!tableOption.some(item => item.prop === col.prop)) {
        fixedWidth += +col.width;
      }
    })
    return fixedWidth;
  }
  
  resize(column, newValue, tableOption, accessor, tableRowDetails) {
    const { componentId: title, gridRef } = accessor;
    const scrollWidth = this.getScrollWidth(tableRowDetails, gridRef);
    const fixedWidth = this.getFixedWidth(tableRowDetails.bodyComponent._columns, tableOption);
    const tableWidth = tableRowDetails.bodyComponent.innerWidth;
    const tableOptionTemp = JSON.parse(JSON.stringify(tableOption));
    if (column && newValue) {
      tableOption.forEach(item => {
        if (column.prop === item.prop) {
          item.width = newValue;
        }
      })
      const total = tableOption.filter(item => item.columnStatus).reduce((prev, curv) => prev + curv.width, 0);
      if (Math.ceil(total) > Math.ceil(tableWidth - fixedWidth - scrollWidth)) {
        this.saveTableOption(title, tableOption);
      } else {
        const otherWidth = tableOptionTemp.filter(item => item.columnStatus && item.prop !== column.prop).reduce((prev, curv) => prev + curv.width, 0);
        const minWith = tableWidth - fixedWidth - scrollWidth - otherWidth;
        let isChange = false;
        tableOptionTemp.forEach(item => {
          if (column.prop === item.prop) {
            isChange = item.width !== minWith;
            item.width = minWith;
          }
        })
        tableOption = [...tableOptionTemp];
        if (isChange) {
          this.saveTableOption(title, tableOption);
        }
      }
    }
    return tableOption;
  }
  getScrollWidth(tableRowDetails, gridRef) {
    let scrollWidth = 0;
    const gridsterDom = gridRef?.el || {};
    if (gridsterDom.scrollHeight > gridsterDom.clientHeight) {
      scrollWidth += 10;
    }
    if (tableRowDetails.bodyComponent.scrollHeight > tableRowDetails.bodyHeight) {
      scrollWidth += 10;
    }
    return scrollWidth;
  }
  getContentWidth(element: Element): number{
    const bodyWidth = document.body.clientWidth;
    const style = getComputedStyle(element);
    const marginLeft = parseFloat(style.marginLeft);
    const marginRight = parseFloat(style.marginRight);
    const paddingLeft = parseFloat(style.paddingLeft);
    const paddingRight = parseFloat(style.paddingRight);
    return bodyWidth - marginLeft - marginRight - paddingLeft - paddingRight;
  }
  formatColumn(tableRowDetails, tableOption, tableWidth: number, accessor) {
    const contentDom = document.querySelector('.content.app-content');
    const contentWidth = this.getContentWidth(contentDom);
    const { componentId: tableTitle, gridRef } = accessor;
    const scrollWidth = this.getScrollWidth(tableRowDetails, gridRef);
    const fixedWidth = this.getFixedWidth(tableRowDetails.bodyComponent._columns, tableOption);
    const total = tableOption.filter(item => item.columnStatus).reduce((prev, curv) => prev + curv.width, 0);
    if ((Math.ceil(total) < Math.ceil(tableWidth - fixedWidth - scrollWidth)) && (tableWidth <= contentWidth)) {
      return this.recalculate(cloneDeep(tableOption), fixedWidth, tableWidth, scrollWidth, tableTitle);
    } else {
      return tableOption;
    }
  }

  recalculate(tableOption, fixedWidth, tableWidth, scrollWidth, title) {
    const newWidth = tableWidth - fixedWidth - scrollWidth;
    const total = tableOption.filter(item => item.columnStatus).reduce((prev, curv) => prev + curv.flexGrow, 0);
    tableOption.filter(item => item.columnStatus)
      .forEach(item => {
        const width = newWidth * item.flexGrow / total;
        item.width = width;
        // item.flexGrow = width;
      })
    this.saveTableOption(title, tableOption);
    return tableOption;
  }

  mergeOption(title: string, fixedOption: any[]) {
    const serveOptions = cloneDeep(this._gridSystemService.getPersonalColumns(title).tableOption);
    if (!serveOptions) return fixedOption;
    let mergeOptions = serveOptions.filter(option => fixedOption.some(item => item.prop === option.prop));
    let deleteStatus = mergeOptions.length !== serveOptions.length;
    let addStatus = false;
    fixedOption.forEach((item, index) => {
      if (!serveOptions.some(option => item.prop === option.prop)) {
        mergeOptions.splice(index, 0, item);
        addStatus = true;
      }
    })
    if (deleteStatus || addStatus) {
      this.saveTableOption(title, mergeOptions);
    }
    return mergeOptions;
  }
}
