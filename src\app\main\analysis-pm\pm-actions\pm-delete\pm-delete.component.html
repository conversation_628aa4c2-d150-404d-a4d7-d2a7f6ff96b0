<button
    type="button"
    ngbTooltip="Delete"
    container="body"
    class="btn icon-btn btn-sm tableActionButton"
    *ngIf="mode=='singleMetric'"
    (click)="delete();"
    [disabled]="selectedMetric.isAllowed || noPermission"
    rippleEffect>
    <span
      [data-feather]="'trash-2'"
      class="mr-50 text-primary"></span>
</button>
<a
  [disabled]="selectedMetrics.length==0 || noPermission"
  *ngIf="mode=='multiMetrics'"
  (click)="delete();"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center">
  <i
    data-feather="trash-2"
    class="mr-50"></i>
  {{ 'PM.DELETE' | translate }}
</a>
<a
  *ngIf="mode=='allMetrics'"
  (click)="delete();"
  [disabled]="noPermission"
  href="javascript:void(0)"
  ngbDropdownItem
  class="d-flex align-items-center">
  <i
    data-feather="trash-2"
    class="mr-50"></i>
  {{ 'PM.DELETEALL' | translate }}
</a>
