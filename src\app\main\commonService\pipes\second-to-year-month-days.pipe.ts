import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'secondToYearMonthDays'
})
export class SecondToYearMonthDaysPipe implements PipeTransform {

  transform(time: any, args?: any): any {
    if (!args) {
      if (time) {
        var num = 0;
        var unit = '';
        if (time > 3600 * 24) {
          num = Math.floor((time / 86400) * 100) / 100;
          if (num >= 1) {
            unit = 'days';
          } else {
            unit = 'day';
          }
        } else if (time >= 3600) {
          num = Math.floor((time / 3600) * 100) / 100;
          if (num > 1) {
            unit = 'hrs';
          } else {
            unit = 'hr';
          }
        } else if (time >= 60) {
          num = Math.floor((time / 60) * 100) / 100;
          if (num > 1) {
            unit = 'mins';
          } else {
            unit = 'min';
          }
        } else {
          num = time;
          if (num > 1) {
            unit = 'sec';
          } else {
            unit = 'sec';
          }
        }
        return num + ' ' + unit;
      } else {
        return '0';
      }
    } else {
      if (args == 'millisecond') {
        if (time <= 0) {
          return `-`
        }
        let days = parseInt(String(time / (1000 * 60 * 60 * 24)));
        let hours = parseInt((String((time % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))));
        let minutes = parseInt((String((time % (1000 * 60 * 60)) / (1000 * 60))));
        let dayText = days <= 1 ? 'day' : 'days'
        let hourText = hours <= 1 ? 'hour' : 'hours'
        let minuteText = minutes <= 1 ? 'minute' : 'minutes'
        return `${days} ${dayText} ${hours} ${hourText} ${minutes} ${minuteText}`
      }else if (args == 'second') {
        if (time <= 0) {
          return `-`
        }
        let days = parseInt(String(time / (60 * 60 * 24)));
        let hours = parseInt((String((time % (60 * 60 * 24)) / (60 * 60))));
        let minutes = parseInt((String((time % (60 * 60)) / 60)));
        let dayText = days <= 1 ? 'day' : 'days'
        let hourText = hours <= 1 ? 'hour' : 'hours'
        let minuteText = minutes <= 1 ? 'minute' : 'minutes'
        return `${days} ${dayText} ${hours} ${hourText} ${minutes} ${minuteText}`
      }
    }

  }

}
