import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
declare var Strophe: any;
declare var $iq: any;
declare var $msg: any;
declare var $pres: any;

@Injectable({
  providedIn: 'root'
})
export class XmppAuthService {
  public BOSH_SERVICE = 'http://**************:5280/bosh';
  // connection = null;
  // isConnected: boolean = false;
  // connectedUser = null;
  // mydomain = 'localhost';
  // jid = null;
  // user: any = {};
  // contacts: any[] = [];
  // searchContacts: any[] = [];
  public m_status = null;
  public m_jid = null;
  public m_room_jid = null;
  public m_connect = null;
  public mode = 'cli'

  public PORTOCAL_HEAD_Sync1 = 0x1b;
  public PORTOCAL_HEAD_Sync2 = 0xff;
  // public PORTOCAL_LENGTH = ;
  public PROTOCOL_COMMAND_INPUT = '0';    	//user input from client to server
  public PROTOCOL_COMMAND_OUTPUT = '1';		//console output from server to client
  public PROTOCOL_COMMAND_RESIZE_TERMINAL = '2';  //resize term cmd from client to server
  public resizeTimeout = null;

  public buff_data = '';
  constructor(
    private _httpClient: HttpClient
  ) { }


  registerTroubleShooting(deviceId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/remoteTroubleshooting/${deviceId}/register`, {}).subscribe({
        next: (response: any) => {
          resolve(JSON.parse(response));
        },
        error: reject
      });
    });
  }

  unRegisterTroubleShooting(deviceId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/remoteTroubleshooting/${deviceId}/unregister`, {}).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  destroyTroubleShooting(deviceId, bConfirm): Promise<any[]> {
    let url = bConfirm ? `nbi/device/remoteTroubleshooting/${deviceId}/forceDisconnect?confirm=true` : `nbi/device/remoteTroubleshooting/${deviceId}/forceDisconnect`
    return new Promise((resolve, reject) => {
      this._httpClient.post(url, {}).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  sendPolling(deviceId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.post(`nbi/device/remoteTroubleshooting/${deviceId}/polling`, {}).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }

  formatSeconds(value) {
    var theTime: any = parseInt(value);//  second
    var theTime1: any = 0;// minute
    var theTime2: any = 0;// hour

    if (theTime > 60) {
      theTime1 = (theTime / 60).toFixed(0);
      theTime = (theTime % 60).toFixed(0);

      if (theTime1 > 60) {
        theTime2 = (theTime1 / 60).toFixed(0);
        theTime1 = (theTime1 % 60).toFixed(0);
      }
    }

    var result = "" + parseInt(theTime) + "s";
    if (theTime1 > 0) {
      result = "" + parseInt(theTime1) + "min " + result;
    }
    if (theTime2 > 0) {
      result = "" + parseInt(theTime2) + "h " + result;
    }
    return result;
  }



  auth(BOSH_SERVICE, xmpp_jid, xmpp_pwd, room_jid, cb1?, cb2?, cb3?) {
    var onMessage = (message) => {
      if (message) {
        // console.log('message---innerHTML:' + message.innerHTML);
        // console.log('message---outerHTML:' + message.outerHTML);
        var from = message.getAttribute('from');
        var type = message.getAttribute('type');
        var elems = message.getElementsByTagName('body');
        var m_content = Strophe.getText(elems[0]);

        console.log('message from:' + from);
        console.log('message type:' + type);
        console.log('message content:' + m_content);
        cb3(from, type, m_content);
      }
      return true;
    };

    var onPresence = (presence) => {
      if (presence) {
        // console.log("--presence--innerHTML:"+presence.innerHTML);
        // console.log("--presence--outerHTML:"+presence.outerHTML);
        var from = presence.getAttribute('from');
        var type = presence.getAttribute('type');
        if (from.toLowerCase().indexOf(this.m_room_jid.toLowerCase()) > -1) {
          console.log("--presence--from:" + from);
          console.log("--presence--type:" + type);
          // if (presence_type === 'unavailable')
          var elems = presence.getElementsByTagName('item');
          if (elems && elems[0]) {
            var jid = elems[0].getAttribute('jid');
            var role = elems[0].getAttribute('role');
            console.log("--presence--jid:" + jid);
            console.log("--presence--role:" + role);
            // cb2(jid, role);
            cb2(from, role);
          }
        }
      }
      return true;
    };

    this.m_connect = new Strophe.Connection(BOSH_SERVICE);
    console.log(this.m_connect)
    this.m_jid = xmpp_jid;
    this.m_room_jid = room_jid;
    this.m_connect.connect(xmpp_jid, xmpp_pwd, (status) => {
      cb1(status);
      this.m_status = status;
      if (status === Strophe.Status.CONNECTED) {
        console.log("auth pass");

        this.m_connect.addHandler(onPresence, null, "presence");
        this.m_connect.addHandler(onMessage, null, "message", null, null, null);
        this.m_connect.send($pres().tree());

        // send <presence>add to groupChat
        // .c('history', {maxstanzas: 0, maxchars:0, seconds:0}
        var pres = $pres({
          from: xmpp_jid,
          to: room_jid + "/" + xmpp_jid.substring(0, xmpp_jid.indexOf("@"))
        }).c('x', { xmlns: 'http://jabber.org/protocol/muc' })
          .c('history', { seconds: 1 }).tree();
        this.m_connect.send(pres);

      }
    })
  }

  Base64_encode(str) {
    // convert to base64
    var base64 = btoa(str);
    return base64;
  }

  Base64_decode(base64) {
    var str = atob(base64)
    return str;
  }

  encodeData(data) {
    console.log('data 111 xterm=>', data)
    var sync_magic = String.fromCharCode(0x1B, 0xFF);
    var data_len = data.length;
    var data_len_nbo = String.fromCharCode((data_len >> 8) & 0xFF, data_len & 0xFF);
    var input_data = sync_magic + data_len_nbo + this.PROTOCOL_COMMAND_INPUT + data;
    var m_data = this.Base64_encode(input_data);
    // this.mode = 'data'
    return m_data
  }

  encodeResizeData(e) {
    var event = JSON.stringify({ columns: e.cols, rows: e.rows });
    var sync_magic = String.fromCharCode(0x1B, 0xFF);
    var data_len = event.length;
    var data_len_nbo = String.fromCharCode((data_len >> 8) & 0xFF, data_len & 0xFF);
    var event_data = sync_magic + data_len_nbo + this.PROTOCOL_COMMAND_RESIZE_TERMINAL + event;
    var m_data = this.Base64_encode(event_data);
    this.mode = 'size'
    return m_data
  }




  send(content) {
    if (this.m_status) {
      // creat one <message> element and send
      var msg = $msg({
        to: this.m_room_jid,
        from: this.m_jid,
        type: 'groupchat'
      }).c("body", null, content);
      this.m_connect.send(msg.tree());
    }
  }

  disConnect() {
    if (this.m_connect) {
      if (this.m_connect.connect_callback) {
        this.m_connect.connect_callback = null;
      }
      this.m_connect.disconnect();
      this.m_connect = null;
    }
  }
}
